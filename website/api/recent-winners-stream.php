<?php
/**
 * Recent Winners SSE Stream Endpoint
 * 
 * Provides real-time updates for recent jackpot winners using Server-Sent Events
 * This prevents the requests from showing up in browser devtools network tab
 */

// Set headers for SSE
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// Disable output buffering
if (ob_get_level()) {
    ob_end_clean();
}

// Function to send SSE data
function sendSSE($data, $event = 'winners') {
    // Add timestamp for synchronization
    if ($event === 'winners') {
        $data['sync_timestamp'] = time();
    }
    echo "event: $event\n";
    echo "data: " . json_encode($data) . "\n\n";
    flush();
}

// Function to get recent winners data (using same logic as recent-winners.php)
function getRecentWinnersData() {
    try {
        // Load API client to get real jackpot data
        require_once __DIR__ . '/../includes/api_client.php';
        require_once __DIR__ . '/../includes/logging.php';

        // Create API client instance
        $apiClient = new ApiClient();
        
        // Get current jackpot data from Go API
        $jackpotResponse = $apiClient->getJackpotData();
        
        // Generate realistic recent winners based on current jackpot data
        $recentWinners = [];
        
        // If we have real winner data from API, use it as the most recent
        if (!isset($jackpotResponse['error']) && !empty($jackpotResponse['last_win_time'])) {
            
            // Check if we have multiple winners from the same jackpot
            if (!empty($jackpotResponse['last_winners']) && is_array($jackpotResponse['last_winners'])) {
                // Multiple winners - create entries for each
                foreach ($jackpotResponse['last_winners'] as $winner) {
                    $winnerName = is_array($winner) ? ($winner['username'] ?? $winner['name'] ?? 'Unknown') : $winner;
                    $winnerAmount = is_array($winner) ? ($winner['amount'] ?? $jackpotResponse['last_win_amount'] ?? 0) : ($jackpotResponse['last_win_amount'] ?? 0);
                    
                    $recentWinners[] = [
                        'username' => $winnerName,
                        'amount' => (int)$winnerAmount,
                        'time_ago' => calculateTimeAgo($jackpotResponse['last_win_time']),
                        'timestamp' => $jackpotResponse['last_win_time'],
                        'source' => 'real',
                        'is_group_win' => true,
                        'group_size' => count($jackpotResponse['last_winners'])
                    ];
                }
            } elseif (!empty($jackpotResponse['last_winner'])) {
                // Single winner
                $recentWinners[] = [
                    'username' => $jackpotResponse['last_winner'],
                    'amount' => (int)($jackpotResponse['last_win_amount'] ?? 0),
                    'time_ago' => calculateTimeAgo($jackpotResponse['last_win_time']),
                    'timestamp' => $jackpotResponse['last_win_time'],
                    'source' => 'real',
                    'is_group_win' => false
                ];
            }
        }
        
        // Only use real jackpot data - no simulated winners
        // Sort by timestamp (most recent first) and limit to 5
        if (!empty($recentWinners)) {
            usort($recentWinners, function($a, $b) {
                $timeA = strtotime($a['timestamp']);
                $timeB = strtotime($b['timestamp']);
                // Most recent first (larger timestamp = more recent)
                return $timeB - $timeA;
            });
            $recentWinners = array_slice($recentWinners, 0, 5);
        }
        
        // Return the winners data
        return [
            'winners' => $recentWinners,
            'total_count' => count($recentWinners),
            'updated_at' => date('c'),
            'source' => count($recentWinners) > 0 ? 'real' : 'empty'
        ];

    } catch (Exception $e) {
        // Log error for debugging
        if (function_exists('secure_log')) {
            secure_log('Recent Winners SSE Error: ' . $e->getMessage(), 'error');
        }

        // Return error response
        return [
            'winners' => [],
            'total_count' => 0,
            'updated_at' => date('c'),
            'source' => 'error',
            'error' => 'Unable to fetch recent winners data'
        ];
    }
}

/**
 * Calculate human-readable time ago from timestamp
 */
function calculateTimeAgo($timestamp) {
    $time = strtotime($timestamp);
    $diff = time() - $time;
    
    if ($diff < 60) {
        return 'Just now';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } else {
        $days = floor($diff / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    }
}

// Send initial data
$lastData = getRecentWinnersData();
sendSSE($lastData);

// Keep connection alive and send updates
$startTime = time();
$maxDuration = 300; // 5 minutes max connection time
$lastCheck = 0;
$lastWinnerCount = $lastData['total_count'] ?? 0;

while (time() - $startTime < $maxDuration) {
    // Check every 60 seconds (less frequent than jackpot since winners change less often)
    sleep(60);

    $currentTime = time();
    $currentData = getRecentWinnersData();
    $currentWinnerCount = $currentData['total_count'] ?? 0;

    // Only send update if winner data changed or every 5 minutes
    $timeSinceLastUpdate = $currentTime - $lastCheck;
    if ($currentWinnerCount !== $lastWinnerCount || 
        json_encode($currentData['winners']) !== json_encode($lastData['winners']) ||
        $timeSinceLastUpdate >= 300) {
        
        sendSSE($currentData);
        $lastData = $currentData;
        $lastWinnerCount = $currentWinnerCount;
        $lastCheck = $currentTime;
    }

    // Send heartbeat every 120 seconds to keep connection alive
    if (($currentTime - $startTime) % 120 === 0) {
        sendSSE(['heartbeat' => $currentTime], 'heartbeat');
    }

    // Check if client disconnected
    if (connection_aborted()) {
        break;
    }
}

// Send close event
sendSSE(['message' => 'Stream ended'], 'close');
?>
