<?php
// Vote leaderboard API proxy
// This hides the external API key from client-side requests

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// Get month parameter (default to 'current')
$month = isset($_GET['month']) ? $_GET['month'] : 'current';

// Validate month parameter
if (!in_array($month, ['current', 'previous'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid month parameter. Must be "current" or "previous"']);
    exit();
}

// External API configuration (keep this secret!)
$apiKey = '536tHau2tTSb9KnfChZL0vAOllLGB4XSnvJ';
$apiUrl = 'https://minecraftpocket-servers.com/api/';

// Build the external API URL
$externalUrl = $apiUrl . '?' . http_build_query([
    'object' => 'servers',
    'element' => 'voters',
    'key' => $apiKey,
    'month' => $month,
    'format' => 'json'
]);

// Initialize cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $externalUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'MassacreMC Vote Leaderboard/1.0');

// Execute the request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

// Handle cURL errors
if ($response === false || !empty($error)) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Failed to fetch vote data',
        'details' => $error
    ]);
    exit();
}

// Handle HTTP errors
if ($httpCode !== 200) {
    http_response_code($httpCode);
    echo json_encode([
        'error' => 'External API returned error',
        'http_code' => $httpCode
    ]);
    exit();
}

// Decode and validate the response
$data = json_decode($response, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Invalid JSON response from external API',
        'json_error' => json_last_error_msg()
    ]);
    exit();
}

// Return the data
echo json_encode($data);
?>
