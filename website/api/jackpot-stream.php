<?php
/**
 * Jackpot SSE Stream Endpoint
 * 
 * Provides real-time jackpot updates via Server-Sent Events
 */

// Set SSE headers
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('X-Accel-Buffering: no'); // Disable proxy buffering (if any)
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// Prevent output buffering and timeouts
@ini_set('zlib.output_compression', '0');
if (function_exists('apache_setenv')) { @apache_setenv('no-gzip', '1'); }
@set_time_limit(0);
if (function_exists('session_write_close')) { @session_write_close(); }

// Flush all existing output buffers
while (ob_get_level() > 0) { @ob_end_flush(); }
@flush();

// Function to send SSE data
function sendSSE($data, $event = 'jackpot') {
    // Add timestamp for synchronization
    if ($event === 'jackpot') {
        $data['sync_timestamp'] = time();
    }
    echo "event: $event\n";
    echo "data: " . json_encode($data) . "\n\n";
    flush();
}

// Function to get jackpot data (using same API client as jackpot.php)
function getJackpotData() {
    try {
        // Load API client
        require_once __DIR__ . '/../includes/api_client.php';
        require_once __DIR__ . '/../includes/logging.php';

        // Create API client instance
        $apiClient = new ApiClient();

        // Get jackpot data from Go API
        $response = $apiClient->getJackpotData();

        // Check for errors and provide fallback showing 0
        if (isset($response['error'])) {
            // If Go API fails, show 0 as requested
            return [
                'jackpot' => 0,
                'amount' => 0,
                'last_winner' => null,
                'last_win_time' => null,
                'last_win_amount' => 0,
                'total_wins' => 0,
                'updated_at' => date('c'),
                'source' => 'offline_fallback',
                'api_status' => 'offline'
            ];
        } else {
            // Transform the response to match expected format
            return [
                'jackpot' => (int)($response['amount'] ?? $response['jackpot'] ?? 0),
                'amount' => (int)($response['amount'] ?? $response['jackpot'] ?? 0),
                'last_winner' => $response['last_winner'] ?? null,
                'last_win_time' => $response['last_win_time'] ?? null,
                'last_win_amount' => (int)($response['last_win_amount'] ?? 0),
                'total_wins' => (int)($response['total_wins'] ?? 0),
                'updated_at' => $response['updated_at'] ?? date('c'),
                'source' => 'go_api'
            ];
        }
    } catch (Exception $e) {
        // Fallback if API client fails - show 0
        return [
            'jackpot' => 0,
            'amount' => 0,
            'last_winner' => null,
            'last_win_time' => null,
            'last_win_amount' => 0,
            'total_wins' => 0,
            'updated_at' => date('c'),
            'source' => 'error_fallback',
            'api_status' => 'error'
        ];
    }
}

// Send initial data
$lastData = getJackpotData();
sendSSE($lastData);

// Keep connection alive and send updates
$startTime = time();
$maxDuration = 300; // 5 minutes max connection time
$lastCheck = 0;
$lastAmount = $lastData['amount'] ?? 0;

while (time() - $startTime < $maxDuration) {
    // Check every 30 seconds to reduce API calls (was 10 seconds)
    sleep(30);

    $currentTime = time();
    $currentData = getJackpotData();
    $currentAmount = $currentData['amount'] ?? 0;

    // Only send update if jackpot amount changed or every 2 minutes
    $timeSinceLastUpdate = $currentTime - $lastCheck;
    if ($currentAmount !== $lastAmount || $timeSinceLastUpdate >= 120) {
        sendSSE($currentData);
        $lastData = $currentData;
        $lastAmount = $currentAmount;
        $lastCheck = $currentTime;
    }

    // Send heartbeat every 60 seconds to keep connection alive
    if (($currentTime - $startTime) % 60 === 0) {
        sendSSE(['heartbeat' => $currentTime], 'heartbeat');
    }

    // Check if client disconnected
    if (connection_aborted()) {
        break;
    }
}

// Send close event
sendSSE(['message' => 'Stream ended'], 'close');
?>
