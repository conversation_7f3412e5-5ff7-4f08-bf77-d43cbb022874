document.addEventListener('DOMContentLoaded', function () {
  const el = document.getElementById('vote-jackpot-value');
  if (!el) return;

  // Removed confetti burst animation - keeping only background confetti

  // Individual digit animation with odometer effect
  function animateCount(target) {
    const currentText = el.textContent.replace(/[^0-9]/g, ''); // Remove commas/formatting
    const currentValue = parseInt(currentText) || 0;

    if (currentValue === target) return; // No change needed

    // Add subtle glow effect for jackpot increases
    if (target > currentValue) {
      el.parentElement.style.animation = 'jackpot-glow 2s ease-out';
      setTimeout(() => {
        el.parentElement.style.animation = '';
      }, 2000);
    }

    const targetStr = target.toString();
    const currentStr = currentValue.toString().padStart(targetStr.length, '0');

    // Build simple per-digit spans (no complex transforms)
    el.innerHTML = '';
    el.style.display = 'inline-flex';
    el.style.justifyContent = 'center';
    el.style.alignItems = 'baseline';
    el.style.gap = '2px';

    // Add dollar sign prefix
    const dollar = document.createElement('span');
    dollar.textContent = '$';
    dollar.style.cssText = 'font: inherit; color: inherit; margin-right: 4px; display:inline-block;';
    el.appendChild(dollar);

    const digits = [];

    for (let i = 0; i < targetStr.length; i++) {
      // Insert comma before this digit if needed
      const remaining = targetStr.length - i - 1;
      if (i > 0 && remaining % 3 === 2) {
        const comma = document.createElement('span');
        comma.textContent = ',';
        comma.style.cssText = 'font: inherit; color: inherit; margin: 0 1px;';
        el.appendChild(comma);
      }

      const span = document.createElement('span');
      span.style.cssText = 'display:inline-block; width:0.75em; text-align:center; font: inherit; color: inherit;';

      const startDigit = parseInt(currentStr[i]) || 0;
      const endDigit = parseInt(targetStr[i]);
      span.textContent = startDigit;
      el.appendChild(span);

      digits.push({ el: span, start: startDigit, end: endDigit });
    }

    // Animate each digit with a small pop/fade, updating the number
    digits.forEach((d, idx) => {
      if (d.start === d.end) return;
      const delay = idx * 80;
      setTimeout(() => {
        // Animate out
        d.el.animate([
          { transform: 'translateY(-30%)', opacity: 0.2 },
          { transform: 'translateY(0)', opacity: 1 }
        ], { duration: 200, easing: 'ease-out' });
        // Update content
        d.el.textContent = d.end;
      }, delay);
    });
  }

  // Helper to show API OFFLINE in red
  function showApiOffline() {
    el.textContent = 'API OFFLINE';
    el.style.color = '#ff4d4d';
    el.style.fontWeight = 'bold';
    el.style.letterSpacing = '1px';
  }

  // Render value without any animations
  function renderValue(amount) {
    const num = Number(amount) || 0;
    el.textContent = '$' + num.toLocaleString('en-US');
    // reset styles in case API was offline before
    el.style.color = '';
    el.style.fontWeight = '';
    el.style.letterSpacing = '';
  }


  // Update function used by external callers if needed
  window.setVoteJackpot = function (amount) {
    const num = Number(amount);
    if (!isFinite(num) || num < 0) return showApiOffline();
    animateCount(num);
  };

  // Strategy to obtain jackpot:
  // 1) If a same-origin endpoint is provided via data-endpoint on the element, fetch it.
  //    Expect shape: { jackpot: number }
  // 2) Else, if window.VOTE_CRATE_JACKPOT is set, use it.
  // 3) Else, show 0.

  // SSE connection for real-time updates
  let eventSource = null;
  let reconnectAttempts = 0;
  let maxReconnectAttempts = 5;

  // Function to setup SSE connection
  function setupSSE() {
    const endpoint = el.getAttribute('data-endpoint');
    console.log('SSE setup - endpoint:', endpoint);

    if (!endpoint) {
      console.log('No endpoint found, checking for fallback...');
      if (typeof window.VOTE_CRATE_JACKPOT !== 'undefined') {
        console.log('Using fallback VOTE_CRATE_JACKPOT:', window.VOTE_CRATE_JACKPOT);
        window.setVoteJackpot(window.VOTE_CRATE_JACKPOT);
      } else {
        console.log('No fallback available, showing API offline');
        showApiOffline();
      }
      return;
    }

    // Check if SSE is supported
    if (typeof EventSource === 'undefined') {
      console.log('SSE not supported, using polling');
      fallbackToPolling();
      return;
    }

    // Use SSE stream endpoint
    let sseEndpoint;
    if (endpoint.includes('/jackpot.php')) {
      sseEndpoint = endpoint.replace('/jackpot.php', '/jackpot-stream.php');
    } else if (endpoint.includes('/jackpot')) {
      sseEndpoint = endpoint.replace('/jackpot', '/jackpot-stream.php');
    } else {
      sseEndpoint = endpoint + '-stream.php';
    }
    console.log('Attempting SSE connection to:', sseEndpoint);

    try {
      // Close existing connection if any
      if (eventSource) {
        eventSource.close();
      }

      eventSource = new EventSource(sseEndpoint);

      eventSource.onopen = function(event) {
        console.log('SSE connection established');
        reconnectAttempts = 0; // Reset on successful connection
      };

      eventSource.addEventListener('jackpot', function(event) {
        try {
          const data = JSON.parse(event.data);
          const val = Number((data && (data.jackpot ?? data.value ?? data.amount)) || 0);
          if (val > 0) {
            window.setVoteJackpot(val);
          } else {
            showApiOffline();
          }
        } catch (e) {
          console.warn('Failed to parse SSE data:', e);
        }
      });

      eventSource.addEventListener('heartbeat', function(event) {
        // Keep connection alive - connection is healthy
      });

      eventSource.addEventListener('close', function(event) {
        eventSource.close();
        // Reconnect after 3 seconds
        setTimeout(setupSSE, 3000);
      });

      eventSource.onerror = function(event) {
        console.warn('SSE connection error, attempt:', reconnectAttempts + 1);
        eventSource.close();

        if (reconnectAttempts < maxReconnectAttempts) {
          reconnectAttempts++;
          // Exponential backoff: 2s, 4s, 8s, 16s, 32s
          const delay = Math.min(2000 * Math.pow(2, reconnectAttempts - 1), 32000);
          console.log(`Retrying SSE connection in ${delay}ms...`);
          setTimeout(setupSSE, delay);
        } else {
          console.warn('Max SSE reconnection attempts reached, falling back to polling');
          fallbackToPolling();
        }
      };

      // Add timeout to detect if SSE never connects
      setTimeout(() => {
        if (eventSource && eventSource.readyState === EventSource.CONNECTING) {
          console.warn('SSE connection timeout, falling back to polling');
          eventSource.close();
          fallbackToPolling();
        }
      }, 10000); // 10 second timeout

    } catch (e) {
      console.warn('SSE not supported, falling back to polling');
      fallbackToPolling();
    }
  }

  // Fallback to polling if SSE fails
  function fallbackToPolling() {
    console.log('Falling back to polling...');
    const endpoint = el.getAttribute('data-endpoint');
    if (endpoint) {
      // Initial fetch
      fetchJackpotOnce();
      // Poll every 60 seconds (reduced from 15 seconds to minimize API calls)
      setInterval(fetchJackpotOnce, 60000);
    } else {
      console.log('No endpoint for polling fallback');
      showApiOffline();
    }
  }

  // Single fetch function for polling fallback
  function fetchJackpotOnce() {
    const endpoint = el.getAttribute('data-endpoint');
    if (endpoint) {
      try {
        const cacheBuster = Date.now();
        const url = endpoint + (endpoint.includes('?') ? '&' : '?') + '_t=' + cacheBuster;
        fetch(url, {
          credentials: 'same-origin',
          cache: 'no-cache'
        })
          .then((res) => (res.ok ? res.json() : Promise.reject(new Error(`HTTP ${res.status}`))))
          .then((data) => {
            const val = Number((data && (data.jackpot ?? data.value ?? data.amount)) || 0);
            window.setVoteJackpot(val);
          })
          .catch(() => showApiOffline());
      } catch {
        showApiOffline();
      }
    }
  }

  // Initial display with test value to show animation
  window.setVoteJackpot(27332805);

  // Setup real-time SSE connection with delay to ensure DOM is ready
  setTimeout(() => {
    console.log('Setting up SSE connection...');
    setupSSE();
  }, 1000);
});

