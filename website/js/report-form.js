/**
 * Report Form JavaScript
 * Handles form validation, rule population, and form reset functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Define rule sets
    const playerRules = [
        "Hacking/Cheating",
        "Kill Farming", 
        "Punishment Evading",
        "Harassment/Bullying",
        "Inappropriate Content",
        "Inappropriate Language",
        "Hate Speech",
        "Scamming",
        "Exploiting Bugs",
        "Advertising",
        "Other"
    ];

    const factionRules = [
        "Inappropriate Faction Name",
        "Faction Griefing",
        "Faction Scamming", 
        "Faction Harassment",
        "Faction Exploiting",
        "Other"
    ];

    // Get form elements
    const reportTypeSelect = document.getElementById("reportType");
    const ruleBrokenSelect = document.getElementById("ruleBroken");
    const offenderNameInput = document.getElementById("offenderName");
    const offenderNameLabel = document.getElementById("offenderNameLabel");
    const offenderNameFeedback = document.getElementById("offenderNameFeedback");
    const offenderNameHelp = document.getElementById("offenderNameHelp");
    const evidenceTextarea = document.getElementById("evidence");

    if (!reportTypeSelect || !ruleBrokenSelect) {
        return;
    }

    function updateFormFields() {
        const reportType = reportTypeSelect.value;

        // Reset form data
        if (offenderNameInput) offenderNameInput.value = "";
        if (evidenceTextarea) evidenceTextarea.value = "";

        // Clear validation states
        if (offenderNameInput) {
            offenderNameInput.classList.remove("is-valid", "is-invalid");
            offenderNameInput.dataset.valid = "false";
        }
        if (evidenceTextarea) evidenceTextarea.classList.remove("is-valid", "is-invalid");
        if (ruleBrokenSelect) ruleBrokenSelect.classList.remove("is-valid", "is-invalid");

        // Clear existing options and rebuild dropdown
        ruleBrokenSelect.innerHTML = '<option value="" selected disabled>Select a rule violation</option>';
        ruleBrokenSelect.selectedIndex = 0;

        // Hide any existing suggestions
        if (offenderNameInput && offenderNameInput.parentNode) {
            const suggestions = offenderNameInput.parentNode.querySelector(".player-suggestions");
            if (suggestions) {
                suggestions.style.display = "none";
                suggestions.innerHTML = "";
            }
        }

        // Remove form validation state
        const formEl = document.getElementById("reportForm");
        if (formEl) formEl.classList.remove("was-validated");

        // Update based on report type
        if (reportType === "faction") {
            
            // Update labels and placeholders
            if (offenderNameLabel) offenderNameLabel.textContent = "Faction Name";
            if (offenderNameInput) {
                offenderNameInput.placeholder = "Enter the name of the faction you are reporting";
                offenderNameInput.setAttribute("data-search-type", "faction");
                offenderNameInput.setAttribute("data-player-search", "false");
            }
            if (offenderNameFeedback) offenderNameFeedback.textContent = "Please provide the name of the faction you are reporting.";
            if (offenderNameHelp) offenderNameHelp.innerHTML = "<small>Please enter a valid faction name that exists on our server</small>";

            // Populate faction rules
            factionRules.forEach((rule) => {
                const option = document.createElement("option");
                option.value = rule;
                option.textContent = rule;
                ruleBrokenSelect.appendChild(option);
            });
        } else {
            
            // Update labels and placeholders
            if (offenderNameLabel) offenderNameLabel.textContent = "Player Username";
            if (offenderNameInput) {
                offenderNameInput.placeholder = "Enter the username of the player you are reporting";
                offenderNameInput.setAttribute("data-player-search", "true");
                offenderNameInput.removeAttribute("data-search-type");
            }
            if (offenderNameFeedback) offenderNameFeedback.textContent = "Please provide the username of the player you are reporting.";
            if (offenderNameHelp) offenderNameHelp.innerHTML = "<small>Please enter a valid player name that exists on our server</small>";

            // Populate player rules
            playerRules.forEach((rule) => {
                const option = document.createElement("option");
                option.value = rule;
                option.textContent = rule;
                ruleBrokenSelect.appendChild(option);
            });
        }

        // Refresh evidence character counter
        if (evidenceTextarea) {
            try {
                // Update character counter after clearing the textarea
                const charCountElement = document.getElementById("charCount");
                if (charCountElement) {
                    const maxLength = parseInt(evidenceTextarea.getAttribute('maxlength') || '500', 10);
                    charCountElement.textContent = `0/${maxLength}`;
                    charCountElement.classList.remove('text-warning', 'text-danger');
                }
                evidenceTextarea.dispatchEvent(new Event("input"));
            } catch (e) {
                // Silently handle error
            }
        }
    }

    // Initialize form on page load
    updateFormFields();

    // Bind change handler
    reportTypeSelect.addEventListener("change", updateFormFields);

    // Set up character counter for evidence textarea
    function initCharacterCounter() {
        const evidenceTextarea = document.getElementById("evidence");
        const charCountElement = document.getElementById("charCount");

        if (!evidenceTextarea || !charCountElement) {
            return;
        }

        const updateCount = function() {
            const text = evidenceTextarea.value || '';
            const currentLength = text.length;
            const maxLength = parseInt(evidenceTextarea.getAttribute('maxlength') || '500', 10);

            charCountElement.textContent = `${currentLength}/${maxLength}`;

            // Update styling based on character count
            charCountElement.classList.remove('text-warning', 'text-danger');
            if (currentLength > maxLength * 0.9) {
                charCountElement.classList.add('text-danger');
            } else if (currentLength > maxLength * 0.8) {
                charCountElement.classList.add('text-warning');
            }
        };

        // Initial count update
        updateCount();

        // Add event listeners for real-time updates
        ['input', 'keyup', 'keydown', 'change', 'paste', 'cut', 'blur', 'focus'].forEach(function(eventType) {
            evidenceTextarea.addEventListener(eventType, updateCount);
        });
    }

    // Initialize character counter
    initCharacterCounter();

    // Set up form validation
    const form = document.getElementById("reportForm");
    if (form) {
        Array.from(form.elements).forEach(input => {
            input.addEventListener("input", function() {
                this.classList.remove("is-invalid");
            });
        });
    }


});
