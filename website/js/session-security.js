/**
 * Session Security Manager
 * Handles session tokens and enhanced security for AJAX requests
 */

class SessionSecurity {
    constructor() {
        this.sessionToken = null;
        this.tokenExpiry = null;
        this.init();
    }

    /**
     * Initialize session security
     */
    init() {
        // Try to get token from various sources
        this.sessionToken = this.getStoredToken();
        
        // Set up automatic token refresh
        this.setupTokenRefresh();
        
        // Intercept all fetch requests to add token
        this.interceptFetchRequests();
        
        // Intercept jQuery AJAX if available
        this.interceptJQueryAjax();
    }

    /**
     * Get stored session token
     */
    getStoredToken() {
        // Try sessionStorage first (cleared when tab closes)
        let token = sessionStorage.getItem('session_token');
        if (token) {
            return token;
        }

        // Try from meta tag (set by server)
        const metaToken = document.querySelector('meta[name="session-token"]');
        if (metaToken) {
            token = metaToken.getAttribute('content');
            this.setToken(token);
            return token;
        }

        return null;
    }

    /**
     * Set session token
     */
    setToken(token, expiryMinutes = 30) {
        this.sessionToken = token;
        this.tokenExpiry = Date.now() + (expiryMinutes * 60 * 1000);
        
        // Store in sessionStorage (not localStorage - we want it to expire with tab)
        sessionStorage.setItem('session_token', token);
        sessionStorage.setItem('token_expiry', this.tokenExpiry.toString());
        
        console.log('Session token updated');
    }

    /**
     * Get current session token
     */
    getToken() {
        // Check if token is expired
        if (this.tokenExpiry && Date.now() > this.tokenExpiry) {
            console.warn('Session token expired');
            this.clearToken();
            return null;
        }
        
        return this.sessionToken;
    }

    /**
     * Clear session token
     */
    clearToken() {
        this.sessionToken = null;
        this.tokenExpiry = null;
        sessionStorage.removeItem('session_token');
        sessionStorage.removeItem('token_expiry');
    }

    /**
     * Add session token to request headers
     */
    addTokenToHeaders(headers = {}) {
        const token = this.getToken();
        if (token) {
            headers['X-Session-Token'] = token;
        }
        return headers;
    }

    /**
     * Setup automatic token refresh
     */
    setupTokenRefresh() {
        const schedule = (ttlSec) => {
            const ms = Math.max(60000, Math.floor(ttlSec * 0.8 * 1000));
            if (this._refreshTimer) clearTimeout(this._refreshTimer);
            this._refreshTimer = setTimeout(() => this.refreshToken(), ms);
        };
        this._scheduleRefresh = schedule;
        const remainingMs = this.tokenExpiry ? (this.tokenExpiry - Date.now()) : (20 * 60 * 1000);
        const ttlSec = Math.max(60, Math.floor(remainingMs / 1000));
        schedule(ttlSec);
    }

    /**
     * Refresh session token
     */
    async refreshToken() {
        try {
            // Determine the correct refresh endpoint based on current portal
            let refreshUrl = '/auth/refresh-token'; // Default (staff portal)

            if (window.location.pathname.startsWith('/admin/')) {
                refreshUrl = '/admin/auth/refresh-token';
            } else if (window.location.pathname.startsWith('/account/') ||
                       window.location.pathname.startsWith('/auth/player-')) {
                refreshUrl = '/auth/player-refresh-token';
            }

            const response = await fetch(refreshUrl, {
                method: 'POST',
                headers: this.addTokenToHeaders({
                    'Content-Type': 'application/json'
                })
            });

            if (response.ok) {
                const ttlHeader = response.headers.get('X-Token-Expires-In');
                if (ttlHeader) {
                    const ttl = parseInt(ttlHeader, 10);
                    if (!isNaN(ttl)) {
                        this.tokenExpiry = Date.now() + ttl * 1000;
                        sessionStorage.setItem('token_expiry', this.tokenExpiry.toString());
                        if (this._scheduleRefresh) this._scheduleRefresh(ttl);
                    }
                }
                const data = await response.json();
                if (data.session_token) {
                    this.setToken(data.session_token);
                }
                if (!ttlHeader && typeof data.expires_in === 'number' && this._scheduleRefresh) {
                    this._scheduleRefresh(data.expires_in);
                }
            } else if (response.status === 401) {
                // Session expired, redirect to login
                this.handleSessionExpired();
            }
        } catch (error) {
            console.error('Token refresh failed:', error);
        }
    }

    /**
     * Handle session expiration
     */
    handleSessionExpired() {
        this.clearToken();
        
        // Show user-friendly message and redirect to appropriate login
        if (typeof showSessionExpiredModal === 'function') {
            showSessionExpiredModal();
        } else {
            alert('Your session has expired. Please log in again.');

            // Redirect to appropriate login page based on current portal
            if (window.location.pathname.startsWith('/admin/')) {
                window.location.href = '/admin/login';
            } else if (window.location.pathname.startsWith('/account/') ||
                       window.location.pathname.startsWith('/auth/player-')) {
                window.location.href = '/auth/player-login';
            } else {
                window.location.href = '/login'; // Staff portal
            }
        }
    }

    /**
     * Intercept fetch requests to add session token
     */
    interceptFetchRequests() {
        const originalFetch = window.fetch;
        const self = this;

        window.fetch = function(url, options = {}) {
            // Only attach token for same-origin requests
            try {
                const reqUrl = new URL(url, window.location.href);
                if (reqUrl.origin === window.location.origin) {
                    options.headers = self.addTokenToHeaders(options.headers || {});
                }
            } catch (e) {
                // If URL parsing fails, default to adding only for relative paths
                if (typeof url === 'string' && url.startsWith('/')) {
                    options.headers = self.addTokenToHeaders(options.headers || {});
                }
            }

            return originalFetch(url, options).then(response => {
                // Check for session expiration
                if (response.status === 401) {
                    const authHeader = response.headers.get('X-Auth-Required');
                    if (authHeader === 'session-expired') {
                        self.handleSessionExpired();
                    }
                } else {
                    // Refresh schedule if server provides TTL
                    const ttlHeader = response.headers.get('X-Token-Expires-In');
                    if (ttlHeader) {
                        const ttl = parseInt(ttlHeader, 10);
                        if (!isNaN(ttl)) {
                            self.tokenExpiry = Date.now() + ttl * 1000;
                            sessionStorage.setItem('token_expiry', self.tokenExpiry.toString());
                            if (self._scheduleRefresh) self._scheduleRefresh(ttl);
                        }
                    }
                }
                return response;
            });
        };
    }

    /**
     * Intercept jQuery AJAX requests to add session token
     */
    interceptJQueryAjax() {
        if (typeof $ !== 'undefined' && $.ajaxSetup) {
            const self = this;
            
            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    // Only attach token for same-origin requests
                    try {
                        const reqUrl = new URL(settings && settings.url ? settings.url : '', window.location.href);
                        if (reqUrl.origin === window.location.origin) {
                            const token = self.getToken();
                            if (token) xhr.setRequestHeader('X-Session-Token', token);
                        }
                    } catch (e) {
                        if (settings && typeof settings.url === 'string' && settings.url.startsWith('/')) {
                            const token = self.getToken();
                            if (token) xhr.setRequestHeader('X-Session-Token', token);
                        }
                    }
                },
                error: function(xhr, status, error) {
                    // Check for session expiration
                    if (xhr.status === 401) {
                        const authHeader = xhr.getResponseHeader('X-Auth-Required');
                        if (authHeader === 'session-expired') {
                            self.handleSessionExpired();
                        }
                    }
                }
            });
        }
    }

    /**
     * Make a secure API request with automatic token handling
     */
    async secureRequest(url, options = {}) {
        // Only attach token for same-origin requests
        try {
            const reqUrl = new URL(url, window.location.href);
            if (reqUrl.origin === window.location.origin) {
                options.headers = this.addTokenToHeaders(options.headers || {});
            }
        } catch (e) {
            if (typeof url === 'string' && url.startsWith('/')) {
                options.headers = this.addTokenToHeaders(options.headers || {});
            }
        }

        try {
            const response = await fetch(url, options);
            
            if (response.status === 401) {
                this.handleSessionExpired();
                throw new Error('Session expired');
            }
            
            return response;
        } catch (error) {
            console.error('Secure request failed:', error);
            throw error;
        }
    }
}

// Initialize session security when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.sessionSecurity = new SessionSecurity();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SessionSecurity;
}
