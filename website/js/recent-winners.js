document.addEventListener('DOMContentLoaded', function () {
  const winnersContainer = document.getElementById('recent-winners');
  console.log('Winners container found:', winnersContainer);
  if (!winnersContainer) {
    console.error('Winners container not found!');
    return;
  }

  let currentWinners = [];
  let isAnimating = false;

  // Format number with commas
  function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  // Create winner element HTML
  function createWinnerElement(winner, index) {
    return `
      <div class="col-12 winner-entry" data-username="${winner.username}" data-timestamp="${winner.timestamp}" style="animation-delay: ${index * 100}ms;">
        <div class="d-flex align-items-center justify-content-between p-3 bg-dark rounded border border-secondary winner-card">
          <div class="d-flex align-items-center">
            <div>
              <div class="fw-bold text-light winner-name">${winner.username}</div>
              <small class="text-muted winner-time">${winner.time_ago}</small>
            </div>
          </div>
          <div class="text-end">
            <div class="fw-bold text-success winner-amount">$${formatNumber(winner.amount)}</div>
            <small class="text-muted">Vote Crate Jackpot</small>
          </div>
        </div>
      </div>
    `;
  }

  // Animate winner entry
  function animateWinnerIn(element) {
    element.style.opacity = '0';
    element.style.transform = 'translateX(-100%) scale(0.8)';
    
    requestAnimationFrame(() => {
      element.style.transition = 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
      element.style.opacity = '1';
      element.style.transform = 'translateX(0) scale(1)';
    });
  }

  // Animate winner out
  function animateWinnerOut(element) {
    return new Promise((resolve) => {
      element.style.transition = 'all 0.4s ease-in';
      element.style.opacity = '0';
      element.style.transform = 'translateX(100%) scale(0.8)';
      
      setTimeout(() => {
        element.remove();
        resolve();
      }, 400);
    });
  }

  // Update winners with smooth animations
  async function updateWinners(newWinners) {
    if (isAnimating) return;
    isAnimating = true;

    // Sort winners by timestamp (most recent first) and limit to exactly 5
    const sortedWinners = newWinners
      .sort((a, b) => {
        const timeA = new Date(a.timestamp).getTime();
        const timeB = new Date(b.timestamp).getTime();
        return timeB - timeA; // Most recent first (larger timestamp = more recent)
      })
      .slice(0, 5);

    console.log('Displaying exactly 5 winners:', sortedWinners.map(w => `${w.username}: ${w.time_ago}`));

    // Clear loading spinner and any non-winner elements
    const nonWinnerElements = winnersContainer.querySelectorAll(':not(.winner-entry)');
    nonWinnerElements.forEach(el => {
      if (el.classList.contains('col-12') && (el.textContent.includes('Loading') || el.querySelector('.spinner-border'))) {
        el.remove();
      }
    });

    // Clear ALL existing winners to ensure exactly 5 are displayed
    const existingElements = Array.from(winnersContainer.querySelectorAll('.winner-entry'));
    existingElements.forEach(el => el.remove());

    // Add exactly 5 sorted winners
    sortedWinners.forEach((winner, index) => {
      const winnerHTML = createWinnerElement(winner, index);
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = winnerHTML;
      const winnerElement = tempDiv.firstElementChild;

      winnersContainer.appendChild(winnerElement);
      animateWinnerIn(winnerElement);
    });

    currentWinners = sortedWinners;
    isAnimating = false;
  }

  // Process winners data from SSE or fallback
  function processWinnersData(data) {
    console.log('Processing winners data:', data);

    if (data.winners && Array.isArray(data.winners)) {
      console.log('Updating winners with data:', data.winners);

      if (data.winners.length === 0) {
        // Show "no winners" message
        winnersContainer.innerHTML = `
          <div class="col-12 text-center py-4">
            <div class="text-muted mb-2">
              <i class="fas fa-trophy fa-2x mb-3" style="opacity: 0.3;"></i>
              <h5>No recent winners yet</h5>
              <p class="mb-0">Be the first to win the jackpot!</p>
            </div>
          </div>
        `;
      } else {
        updateWinners(data.winners);

        // Add subtle glow effect to container when updated
        winnersContainer.parentElement.style.boxShadow = '0 0 20px rgba(255, 215, 0, 0.3)';
        setTimeout(() => {
          winnersContainer.parentElement.style.boxShadow = '';
        }, 2000);
      }
    } else {
      console.warn('Invalid winners data format:', data);
      showErrorMessage();
    }
  }

  // Show error message
  function showErrorMessage() {
    winnersContainer.innerHTML = `
      <div class="col-12 text-center py-4">
        <div class="text-warning">
          <i class="fas fa-exclamation-triangle me-2"></i>
          Unable to load recent winners
        </div>
        <small class="text-muted">Please try again later</small>
      </div>
    `;
  }

  // SSE connection for real-time updates
  let eventSource = null;
  let reconnectAttempts = 0;
  let maxReconnectAttempts = 5;

  // Function to setup SSE connection
  function setupSSE() {
    console.log('Setting up SSE connection for recent winners...');

    // Check if SSE is supported
    if (typeof EventSource === 'undefined') {
      console.log('SSE not supported, using polling fallback');
      fallbackToPolling();
      return;
    }

    try {
      // Close existing connection if any
      if (eventSource) {
        eventSource.close();
      }

      eventSource = new EventSource('/api/recent-winners-stream.php');

      eventSource.onopen = function(event) {
        console.log('Recent winners SSE connection established');
        reconnectAttempts = 0; // Reset on successful connection
      };

      eventSource.addEventListener('winners', function(event) {
        try {
          const data = JSON.parse(event.data);
          processWinnersData(data);
        } catch (e) {
          console.warn('Failed to parse recent winners SSE data:', e);
        }
      });

      eventSource.addEventListener('heartbeat', function(event) {
        // Keep connection alive - connection is healthy
      });

      eventSource.addEventListener('close', function(event) {
        eventSource.close();
        // Reconnect after 3 seconds
        setTimeout(setupSSE, 3000);
      });

      eventSource.onerror = function(event) {
        console.warn('Recent winners SSE connection error, attempt:', reconnectAttempts + 1);
        eventSource.close();

        if (reconnectAttempts < maxReconnectAttempts) {
          reconnectAttempts++;
          // Exponential backoff: 2s, 4s, 8s, 16s, 32s
          const delay = Math.min(2000 * Math.pow(2, reconnectAttempts - 1), 32000);
          console.log(`Retrying recent winners SSE connection in ${delay}ms...`);
          setTimeout(setupSSE, delay);
        } else {
          console.warn('Max recent winners SSE reconnection attempts reached, falling back to polling');
          fallbackToPolling();
        }
      };

      // Add timeout to detect if SSE never connects
      setTimeout(() => {
        if (eventSource && eventSource.readyState === EventSource.CONNECTING) {
          console.warn('Recent winners SSE connection timeout, falling back to polling');
          eventSource.close();
          fallbackToPolling();
        }
      }, 10000); // 10 second timeout

    } catch (e) {
      console.warn('Recent winners SSE not supported, falling back to polling');
      fallbackToPolling();
    }
  }

  // Fallback to polling if SSE fails
  function fallbackToPolling() {
    console.log('Falling back to polling for recent winners...');
    // Initial fetch
    fetchRecentWinnersOnce();
    // Poll every 60 seconds
    setInterval(fetchRecentWinnersOnce, 60000);
  }

  // Single fetch function for polling fallback
  async function fetchRecentWinnersOnce() {
    try {
      const cacheBuster = Date.now();
      const url = '/api/recent-winners.php?_t=' + cacheBuster;
      const response = await fetch(url, {
        credentials: 'same-origin',
        cache: 'no-cache'
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      processWinnersData(data);
    } catch (error) {
      console.warn('Failed to fetch recent winners:', error);
      showErrorMessage();
    }
  }

  // Add hover effects to winner cards
  function addHoverEffects() {
    winnersContainer.addEventListener('mouseenter', (e) => {
      if (e.target.closest('.winner-card')) {
        const card = e.target.closest('.winner-card');
        card.style.transition = 'all 0.3s ease';
        card.style.transform = 'translateY(-2px) scale(1.02)';
        card.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.3)';
        card.style.borderColor = 'rgba(255, 215, 0, 0.5)';
      }
    }, true);

    winnersContainer.addEventListener('mouseleave', (e) => {
      if (e.target.closest('.winner-card')) {
        const card = e.target.closest('.winner-card');
        card.style.transform = '';
        card.style.boxShadow = '';
        card.style.borderColor = '';
      }
    }, true);
  }

  // Initialize
  addHoverEffects();

  // Setup real-time SSE connection with delay to ensure DOM is ready
  setTimeout(() => {
    console.log('Setting up recent winners SSE connection...');
    setupSSE();
  }, 1500); // Slight delay after jackpot SSE

  // Expose function globally for manual updates (fallback)
  window.updateRecentWinners = fetchRecentWinnersOnce;
});
