/**
 * Event Leaderboard JavaScript
 * Live updates and podium management
 */

document.addEventListener('DOMContentLoaded', function () {
    let currentEventId = null;
    let refreshInterval = null;
    let currentPage = 1;
    const itemsPerPage = 15;
    let leaderboardData = [];
    let cubeTimer = null;

    // Initialize the leaderboard
    init();

    function init() {
        initializeCubeTimer();
        checkActiveEvents();
        setupAutoRefresh();
    }

    function initializeCubeTimer() {
        if (window.CubeTimer && document.getElementById('cubeCanvas')) {
            cubeTimer = new window.CubeTimer('cubeCanvas');
            cubeTimer.startTimerUpdates();
        }
    }

    // Check for active events
    function checkActiveEvents() {
        showRefreshIndicator();

        fetch('/event/event_handler.php?action=active_events', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            hideRefreshIndicator();
            
            if (data.success && data.data && data.data.length > 0) {
                // Find the highest priority event
                const activeEvent = data.data.reduce((prev, current) => 
                    (prev.priority > current.priority) ? prev : current
                );
                
                currentEventId = activeEvent.id;
                displayActiveEvent(activeEvent);
                fetchEventLeaderboard(activeEvent.id);

                // Update cube timer with event data
                if (cubeTimer) {
                    cubeTimer.updateEventData(activeEvent);
                }


            } else {
                displayNoActiveEvent();
            }
        })
        .catch(error => {
            console.error('Error fetching active events:', error);
            hideRefreshIndicator();
            displayNoActiveEvent();
        });
    }

    // Display active event information
    function displayActiveEvent(event) {
        document.getElementById('no-event-alert').style.display = 'none';
        document.getElementById('event-timer-alert').style.display = 'block';
        document.getElementById('leaderboard-container').style.display = 'block';

        // Update event info
        document.getElementById('event-name').textContent = event.name;
        document.getElementById('event-title').textContent = event.name;
        document.getElementById('event-description').textContent = event.description || 'Join the event and compete for the top spot!';
        
        // Update countdown timer display
        if (event.remaining && event.remaining > 0) {
            updateCountdownDisplay(event.remaining);
        } else {
            updateCountdownDisplay(0);
        }
    }

    // Display no active event state
    function displayNoActiveEvent() {
        document.getElementById('event-timer-alert').style.display = 'none';
        document.getElementById('leaderboard-container').style.display = 'none';
        document.getElementById('no-event-alert').style.display = 'block';
        currentEventId = null;
    }

    // Fetch event leaderboard data
    function fetchEventLeaderboard(eventId) {
        if (!eventId) return;

        showRefreshIndicator();

        fetch(`/event/event_handler.php?action=event_participants&event_id=${encodeURIComponent(eventId)}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            hideRefreshIndicator();
            
            if (data.success && data.data) {
                leaderboardData = data.data.sort((a, b) => b.score - a.score);
                updateLeaderboardTable(leaderboardData);
                updateEventStats(leaderboardData);
                setupPagination(leaderboardData);
            } else {
                showError('No participant data available');
            }
        })
        .catch(error => {
            console.error('Error fetching leaderboard:', error);
            hideRefreshIndicator();
            showError('Failed to load leaderboard data');
        });
    }



    // Update leaderboard table
    function updateLeaderboardTable(data) {
        const tableBody = document.querySelector('#leaderboard-events tbody');
        if (!tableBody) return;

        // Clear existing rows except loading and error
        const rowsToKeep = ['loading', 'error-message'];
        Array.from(tableBody.children).forEach(child => {
            if (!rowsToKeep.includes(child.id)) {
                tableBody.removeChild(child);
            }
        });

        // Hide loading
        const loadingElement = document.getElementById('loading');
        if (loadingElement) loadingElement.style.display = 'none';

        // Show paginated data
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const pageData = data.slice(startIndex, endIndex);

        pageData.forEach((player, index) => {
            const row = document.createElement('tr');
            const rank = startIndex + index + 1;
            
            // Add special styling for top 3
            if (rank <= 3) {
                row.classList.add(`rank-${rank}`);
            }

            // Rank cell
            const rankCell = document.createElement('td');
            rankCell.className = 'rank-column';
            rankCell.textContent = getOrdinal(rank);
            row.appendChild(rankCell);

            // Player cell
            const playerCell = document.createElement('td');
            playerCell.className = 'entity-column';
            playerCell.innerHTML = `
                <img src="/img/avatars/${player.player_name || player.username || 'default'}.png"
                     alt="${player.player_name || player.username || 'Unknown'}"
                     class="player-avatar"
                     onerror="this.src='/img/default-avatar.png'"
                     style="width: 32px; height: 32px; border-radius: 50%; margin-right: 10px; vertical-align: middle;">
                <span class="player-name">${player.player_name || player.username || 'Unknown'}</span>
            `;
            row.appendChild(playerCell);

            // Score cell
            const scoreCell = document.createElement('td');
            scoreCell.className = 'value-column';
            scoreCell.textContent = player.score.toLocaleString();
            row.appendChild(scoreCell);

            tableBody.appendChild(row);
        });
    }

    // Update event statistics
    function updateEventStats(data) {
        const participantsElement = document.getElementById('event-participants');
        if (participantsElement) {
            participantsElement.textContent = `${data.length} Players`;
        }
    }

    // Setup pagination
    function setupPagination(data) {
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer) return;

        paginationContainer.innerHTML = '';

        const totalPages = Math.ceil(data.length / itemsPerPage);

        if (totalPages <= 1) return;

        // Previous button
        const prevButton = document.createElement('button');
        prevButton.textContent = 'Previous';
        prevButton.classList.add('btn', 'btn-secondary');
        prevButton.disabled = currentPage === 1;
        prevButton.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                updateLeaderboardTable(data);
                setupPagination(data);
            }
        });
        paginationContainer.appendChild(prevButton);

        // Next button
        const nextButton = document.createElement('button');
        nextButton.textContent = 'Next';
        nextButton.classList.add('btn', 'btn-secondary');
        nextButton.disabled = currentPage === totalPages;
        nextButton.addEventListener('click', () => {
            if (currentPage < totalPages) {
                currentPage++;
                updateLeaderboardTable(data);
                setupPagination(data);
            }
        });
        paginationContainer.appendChild(nextButton);
    }

    // Setup auto-refresh
    function setupAutoRefresh() {
        // Refresh every 30 seconds to reduce server load
        refreshInterval = setInterval(() => {
            if (currentEventId) {
                fetchEventLeaderboard(currentEventId);
            } else {
                checkActiveEvents();
            }
        }, 30000);
    }

    // Utility functions
    function formatDuration(seconds) {
        if (seconds <= 0) return '00:00';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    }

    // Update countdown display with individual digit boxes
    function updateCountdownDisplay(totalSeconds) {
        if (totalSeconds <= 0) {
            // Set all to zero when event is not active or ended
            setCountdownDigits(0, 0, 0, 0);
            return;
        }

        const days = Math.floor(totalSeconds / 86400);
        const hours = Math.floor((totalSeconds % 86400) / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;

        setCountdownDigits(days, hours, minutes, seconds);
    }

    function setCountdownDigits(days, hours, minutes, seconds) {
        // Days
        const daysTens = Math.floor(days / 10);
        const daysOnes = days % 10;
        const daysT = document.getElementById('days-tens');
        const daysO = document.getElementById('days-ones');
        if (daysT) daysT.textContent = daysTens;
        if (daysO) daysO.textContent = daysOnes;

        // Hours
        const hoursTens = Math.floor(hours / 10);
        const hoursOnes = hours % 10;
        const hoursT = document.getElementById('hours-tens');
        const hoursO = document.getElementById('hours-ones');
        if (hoursT) hoursT.textContent = hoursTens;
        if (hoursO) hoursO.textContent = hoursOnes;

        // Minutes
        const minutesTens = Math.floor(minutes / 10);
        const minutesOnes = minutes % 10;
        const minutesT = document.getElementById('minutes-tens');
        const minutesO = document.getElementById('minutes-ones');
        if (minutesT) minutesT.textContent = minutesTens;
        if (minutesO) minutesO.textContent = minutesOnes;

        // Seconds
        const secondsTens = Math.floor(seconds / 10);
        const secondsOnes = seconds % 10;
        const secondsT = document.getElementById('seconds-tens');
        const secondsO = document.getElementById('seconds-ones');
        if (secondsT) secondsT.textContent = secondsTens;
        if (secondsO) secondsO.textContent = secondsOnes;
    }

    function getOrdinal(n) {
        const s = ["th", "st", "nd", "rd"];
        const v = n % 100;
        return n + (s[(v - 20) % 10] || s[v] || s[0]);
    }

    function showRefreshIndicator() {
        const indicator = document.getElementById('refresh-indicator');
        if (indicator) indicator.classList.add('active');
    }

    function hideRefreshIndicator() {
        const indicator = document.getElementById('refresh-indicator');
        if (indicator) indicator.classList.remove('active');
    }

    function showError(message) {
        const errorRow = document.getElementById('error-message');
        const loadingRow = document.getElementById('loading');
        
        if (loadingRow) loadingRow.style.display = 'none';
        
        if (errorRow) {
            const errorCell = errorRow.querySelector('td');
            if (errorCell) {
                errorCell.textContent = `⚠️ ${message}`;
            }
            errorRow.style.display = 'table-row';
        }
    }

    // Manual refresh function (global scope for onclick)
    window.manualRefresh = function() {
        if (currentEventId) {
            fetchEventLeaderboard(currentEventId);
        } else {
            checkActiveEvents();
        }
    };

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
        if (cubeTimer) {
            cubeTimer.destroy();
        }
    });
});
