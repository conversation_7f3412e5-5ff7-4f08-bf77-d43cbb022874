#!/bin/bash

# Comprehensive Security Monitoring System for MassacreMC
# Real-time monitoring, alerting, and automated response

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="/var/log/security"
CONFIG_DIR="/etc/massacremc/security"
ALERT_EMAIL="<EMAIL>"  # Configure your email

# Create directories
mkdir -p "$LOG_DIR" "$CONFIG_DIR"

# Configuration files
MAIN_LOG="$LOG_DIR/security_monitor.log"
THREAT_LOG="$LOG_DIR/threats.log"
BLOCKED_IPS="$CONFIG_DIR/blocked_ips.txt"
WHITELIST="$CONFIG_DIR/whitelist.txt"
ALERT_CONFIG="$CONFIG_DIR/alert_config.conf"

# Initialize configuration
init_config() {
    # Create whitelist with trusted IPs
    cat > "$WHITELIST" << EOF
127.0.0.1
::1
# Add your trusted IPs here
# ***********/24
# 10.0.0.0/8
EOF

    # Create alert configuration
    cat > "$ALERT_CONFIG" << EOF
# Alert thresholds
MAX_404_PER_MINUTE=20
MAX_CONNECTIONS_PER_IP=50
MAX_FAILED_LOGINS=5
SCAN_DETECTION_THRESHOLD=10

# Email settings
SMTP_SERVER=localhost
SMTP_PORT=25
ALERT_EMAIL=$ALERT_EMAIL

# Monitoring intervals (seconds)
LOG_CHECK_INTERVAL=30
SYSTEM_CHECK_INTERVAL=300
NETWORK_CHECK_INTERVAL=60
EOF

    touch "$BLOCKED_IPS"
}

# Logging function
log_event() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$MAIN_LOG"
    
    if [ "$level" = "THREAT" ] || [ "$level" = "CRITICAL" ]; then
        echo "[$timestamp] $message" >> "$THREAT_LOG"
        send_alert "$level" "$message"
    fi
}

# Send email alerts
send_alert() {
    local level="$1"
    local message="$2"
    local subject="[MassacreMC Security] $level Alert"
    
    # Simple email sending (requires mailutils or similar)
    if command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "$subject" "$ALERT_EMAIL"
    fi
    
    # Log to system log as well
    logger -p security.warn "MassacreMC Security Alert: $message"
}

# Block IP address
block_ip() {
    local ip="$1"
    local reason="$2"
    
    # Check if IP is whitelisted
    if grep -q "^$ip" "$WHITELIST"; then
        log_event "INFO" "IP $ip is whitelisted, not blocking"
        return
    fi
    
    # Check if already blocked
    if grep -q "^$ip" "$BLOCKED_IPS"; then
        return
    fi
    
    # Add to blocked list
    echo "$ip" >> "$BLOCKED_IPS"
    
    # Block with iptables
    iptables -A INPUT -s "$ip" -j DROP 2>/dev/null
    
    log_event "THREAT" "BLOCKED IP: $ip - Reason: $reason"
}

# Monitor web access logs
monitor_web_logs() {
    local access_log="/var/log/apache2/access.log"
    
    if [ ! -f "$access_log" ]; then
        return
    fi
    
    # Monitor last 100 lines for threats
    tail -n 100 "$access_log" | while read -r line; do
        local ip=$(echo "$line" | awk '{print $1}')
        local status=$(echo "$line" | awk '{print $9}')
        local request=$(echo "$line" | cut -d'"' -f2)
        
        # Skip invalid IPs
        if [[ ! "$ip" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
            continue
        fi
        
        # Check for malicious patterns
        if echo "$line" | grep -qiE "(device\.rsp|setup\.cgi|\.env|urbotnet|botnet|malware)"; then
            block_ip "$ip" "Malicious request pattern detected"
            continue
        fi
        
        # Check for SQL injection attempts
        if echo "$request" | grep -qiE "(union.*select|drop.*table|insert.*into|delete.*from)"; then
            block_ip "$ip" "SQL injection attempt"
            continue
        fi
        
        # Check for XSS attempts
        if echo "$request" | grep -qiE "(<script|javascript:|onload=|onerror=)"; then
            block_ip "$ip" "XSS attempt"
            continue
        fi
        
        # Check for excessive 404s
        if [ "$status" = "404" ]; then
            local count_404=$(grep "$ip" "$access_log" | grep " 404 " | tail -n 20 | wc -l)
            if [ "$count_404" -gt 15 ]; then
                block_ip "$ip" "Excessive 404 errors (scanning)"
            fi
        fi
        
        # Check for suspicious user agents
        if echo "$line" | grep -qE '"(Mozila/5\.0|curl|wget|python|bot)"\s*$'; then
            block_ip "$ip" "Suspicious user agent"
        fi
    done
}

# Monitor system resources
monitor_system() {
    # Check CPU usage
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    if (( $(echo "$cpu_usage > 90" | bc -l) )); then
        log_event "WARNING" "High CPU usage: ${cpu_usage}%"
    fi
    
    # Check memory usage
    local mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    if (( $(echo "$mem_usage > 90" | bc -l) )); then
        log_event "WARNING" "High memory usage: ${mem_usage}%"
    fi
    
    # Check disk usage
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | cut -d'%' -f1)
    if [ "$disk_usage" -gt 90 ]; then
        log_event "WARNING" "High disk usage: ${disk_usage}%"
    fi
    
    # Check for suspicious processes
    if pgrep -f "(wget|curl).*http" >/dev/null; then
        log_event "WARNING" "Suspicious download processes detected"
    fi
}

# Monitor network connections
monitor_network() {
    # Check for unusual connection patterns
    local connection_count=$(netstat -tn | grep ESTABLISHED | wc -l)
    if [ "$connection_count" -gt 1000 ]; then
        log_event "WARNING" "High number of established connections: $connection_count"
    fi
    
    # Check for connections to suspicious ports
    netstat -tn | grep ESTABLISHED | while read -r line; do
        local remote_addr=$(echo "$line" | awk '{print $5}' | cut -d':' -f1)
        local remote_port=$(echo "$line" | awk '{print $5}' | cut -d':' -f2)
        
        # Check for connections to common malware ports
        case "$remote_port" in
            6667|6668|6669|7000|8080|9999)
                log_event "WARNING" "Connection to suspicious port $remote_port from $remote_addr"
                ;;
        esac
    done
}

# Check for file integrity
monitor_file_integrity() {
    # Check for modifications to critical files
    local critical_files=(
        "/etc/passwd"
        "/etc/shadow"
        "/etc/hosts"
        "/var/www/html/.htaccess"
        "/etc/apache2/sites-enabled/"
    )
    
    for file in "${critical_files[@]}"; do
        if [ -f "$file" ]; then
            # Simple timestamp check (in production, use proper file integrity monitoring)
            local mod_time=$(stat -c %Y "$file")
            local current_time=$(date +%s)
            local diff=$((current_time - mod_time))
            
            # Alert if modified in last 5 minutes
            if [ "$diff" -lt 300 ]; then
                log_event "WARNING" "Critical file modified recently: $file"
            fi
        fi
    done
}

# Generate security report
generate_report() {
    local report_file="$LOG_DIR/security_report_$(date +%Y%m%d).txt"
    
    cat > "$report_file" << EOF
MassacreMC Security Report - $(date)
=====================================

Blocked IPs: $(wc -l < "$BLOCKED_IPS")
Threats Detected Today: $(grep "$(date +%Y-%m-%d)" "$THREAT_LOG" | wc -l)
System Uptime: $(uptime)

Top Blocked IPs:
$(sort "$BLOCKED_IPS" | uniq -c | sort -nr | head -10)

Recent Threats:
$(tail -20 "$THREAT_LOG")

System Status:
CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}')
Memory: $(free -h | grep Mem | awk '{print $3 "/" $2}')
Disk: $(df -h / | tail -1 | awk '{print $5}')

Active Connections: $(netstat -tn | grep ESTABLISHED | wc -l)
EOF

    log_event "INFO" "Security report generated: $report_file"
}

# Main monitoring loop
main_monitor() {
    log_event "INFO" "Starting comprehensive security monitoring..."
    
    local last_system_check=0
    local last_network_check=0
    local last_report=0
    
    while true; do
        local current_time=$(date +%s)
        
        # Always monitor web logs
        monitor_web_logs
        
        # System monitoring every 5 minutes
        if [ $((current_time - last_system_check)) -gt 300 ]; then
            monitor_system
            monitor_file_integrity
            last_system_check=$current_time
        fi
        
        # Network monitoring every minute
        if [ $((current_time - last_network_check)) -gt 60 ]; then
            monitor_network
            last_network_check=$current_time
        fi
        
        # Generate daily report
        if [ $((current_time - last_report)) -gt 86400 ]; then
            generate_report
            last_report=$current_time
        fi
        
        sleep 30
    done
}

# Command line interface
case "${1:-monitor}" in
    "init")
        init_config
        log_event "INFO" "Security monitoring system initialized"
        ;;
    "monitor")
        init_config
        main_monitor
        ;;
    "status")
        echo "Blocked IPs: $(wc -l < "$BLOCKED_IPS")"
        echo "Recent threats: $(tail -5 "$THREAT_LOG")"
        ;;
    "report")
        generate_report
        ;;
    "unblock")
        if [ -n "$2" ]; then
            sed -i "/^$2$/d" "$BLOCKED_IPS"
            iptables -D INPUT -s "$2" -j DROP 2>/dev/null
            log_event "INFO" "Unblocked IP: $2"
        fi
        ;;
    *)
        echo "Usage: $0 {init|monitor|status|report|unblock <ip>}"
        exit 1
        ;;
esac
