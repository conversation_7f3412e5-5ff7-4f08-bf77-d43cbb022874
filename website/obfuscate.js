const fs = require('fs');
const JavaScriptObfuscator = require('javascript-obfuscator');
require('dotenv').config({ path: '/etc/massacremc/config/api.env' });


const files = [
  './js/player-suggestions.js',
  './js/account-portal.js',
  './js/account-portal-mobile.js',
  './js/main-new.js',
  './js/punishment-redesign.js',
  './admin/js/punishment-types.js',
  './js/search-api-bridge.js',
  './admin/js/punishments.js',
  './js/session-security.js',  
  './js/staff-application.js',
  './js/appealsubmit.js',
  './js/appeal.js',
  './js/factionsleaderboards.js',
  './js/leaderboards.js',
  './js/search.js',
  './js/search-page.js',
  './js/report-form.js',
  './js/vote-jackpot.js',
  './js/recent-winners.js',
  './js/legal.js',
  './js/vote-leaderboard.js',
  './admin/js/admin-common.js',
  './admin/js/dashboard.js',
  './admin/js/sidebar-fix.js',
  './admin/js/appeals.js',
  './admin/js/reports.js',
  './admin/js/playerinfo.js',
  './admin/js/datatables-fallback.js',
  './admin/js/tickets.js',


];

files.forEach(file => {
  try {
    // Process file silently without console logs
    const sourceCode = fs.readFileSync(file, 'utf8');

    const obfuscationResult = JavaScriptObfuscator.obfuscate(sourceCode, {
      compact: true,
      controlFlowFlattening: true,
      controlFlowFlatteningThreshold: 0.5,
      deadCodeInjection: true,
      deadCodeInjectionThreshold: 0.4,
      debugProtection: true,
      debugProtectionInterval: 3000,
      disableConsoleOutput: true,
      identifierNamesGenerator: 'hexadecimal',
      log: false,
      renameGlobals: false,
      selfDefending: true,
      stringArray: true,
      stringArrayEncoding: ['base64'],
      stringArrayThreshold: 0.8,
      transformObjectKeys: true,
      unicodeEscapeSequence: false,
      stringArrayCallsTransform: true,
      stringArrayCallsTransformThreshold: 0.8,
      stringArrayIndexShift: true,
      stringArrayRotate: true,
      stringArrayShuffle: true,
      stringArrayWrappersCount: 5,
      transformObjectKeys: true
    });

    const outputFile = file.replace('.js', '.min.js');
    fs.writeFileSync(outputFile, obfuscationResult.getObfuscatedCode());
  } catch (error) {
    // Log errors to a file instead of console
    fs.appendFileSync('obfuscation-errors.log', `Error processing ${file}: ${error.message}\n`);
  }
});