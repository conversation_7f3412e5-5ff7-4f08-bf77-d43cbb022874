<?php
/**
 * API Client for Go API Integration
 * 
 * Provides functions to interact with the Go API endpoints
 */

class ApiClient {
    private $base_url;
    private $jwt_token;
    private $timeout;
    private static $cache = [];
    private static $cache_ttl = 10; // 10 seconds cache for jackpot data

    public function __construct() {
        // Load environment configuration
        require_once __DIR__ . '/config_parser.php';
        
        try {
            $env = load_env_config([
                '/etc/massacremc/config/api.env',
                '/etc/massacremc/config/admin.env'
            ]);
        } catch (Exception $e) {
            secure_log("API Client: Failed to load environment config: " . $e->getMessage());
            $env = [];
        }

        // Set API configuration
        // Default to HTTP for local/internal development to avoid TLS issues
        $this->base_url = $env['API_BASE_URL'] ?? 'http://localhost:8080';
        $this->jwt_token = $env['SECRET_KEY'] ?? null; // Use SECRET_KEY from environment
        if (!$this->jwt_token) {
            $is_production = getenv('APP_ENV') === 'production';
            if ($is_production) {
                throw new Exception('SECRET_KEY not configured');
            }
            secure_log("API Client: SECRET_KEY not configured; using temporary dev token", "warning");
            $this->jwt_token = bin2hex(random_bytes(16));
        }
        $this->timeout = intval($env['API_TIMEOUT'] ?? '30');
    }

    /**
     * Make a request to the API
     */
    public function makeRequest($endpoint, $method = 'GET', $data = null) {
        $url = rtrim($this->base_url, '/') . '/' . ltrim($endpoint, '/');
        $scheme = parse_url($url, PHP_URL_SCHEME) ?: 'http';
        $host = parse_url($url, PHP_URL_HOST) ?: '';

        $context_options = [
            'http' => [
                'method' => $method,
                'header' => [
                    'Authorization: Bearer ' . $this->jwt_token,
                    'Content-Type: application/json',
                    'User-Agent: MassacreMC-Website/1.0'
                ],
                'timeout' => $this->timeout,
                'ignore_errors' => true
            ]
        ];
        // Only attach SSL options for HTTPS
        if ($scheme === 'https') {
            $isInternal = (strpos($host, '.') === false) || preg_match('/^(localhost|127\.0\.0\.1|production-servers-\d+|servers-\d+)$/i', $host);

            // Check for client certificates for mutual TLS first
            $clientCertPath = '/etc/ssl/certs/client.crt';
            $clientKeyPath = '/etc/ssl/private/client.key';
            $caCertPath = '/etc/ssl/certs/ca.pem';

            $hasClientCerts = file_exists($clientCertPath) && file_exists($clientKeyPath) && file_exists($caCertPath);

            if ($hasClientCerts) {
                // Use client certificates for mutual TLS authentication
                $context_options['ssl'] = [
                    'local_cert' => $clientCertPath,
                    'local_pk' => $clientKeyPath,
                    'cafile' => $caCertPath,
                    'verify_peer' => true,
                    'verify_peer_name' => true,
                    'allow_self_signed' => false,
                    'disable_compression' => true
                ];
                secure_log("API Client: Using mutual TLS with client certificates for host: {$host}");
            } else {
                // For internal hosts, default to insecure TLS unless explicitly disabled
                $insecureTlsEnv = getenv('API_INSECURE_INTERNAL_TLS');
                $allowInsecure = $isInternal && ($insecureTlsEnv === false || strtolower((string)$insecureTlsEnv) !== 'false');

                if ($allowInsecure) {
                    // For internal hosts, allow self-signed/insecure TLS unless explicitly disabled
                    $context_options['ssl'] = [
                        'verify_peer' => false,
                        'verify_peer_name' => false,
                        'allow_self_signed' => true,
                        'disable_compression' => true,
                        'SNI_enabled' => false
                    ];
                    secure_log("API Client: Using insecure TLS for internal host: {$host} (no client certs found)");
                } else {
                    $context_options['ssl'] = [
                        'verify_peer' => true,
                        'verify_peer_name' => true,
                        'allow_self_signed' => false
                    ];
                    secure_log("API Client: Using secure TLS for host: {$host}");
                }
            }
        }

        if ($data && in_array($method, ['POST','PUT','PATCH'], true)) {
            $context_options['http']['content'] = json_encode($data);
        }

        secure_log("API Client: Making {$method} request to {$url}");

        // Use cURL for better HTTP/2 and TLS support
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $this->jwt_token,
                'Content-Type: application/json',
                'User-Agent: MassacreMC-API-Client/1.0'
            ],
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_2_0,
            CURLOPT_FOLLOWLOCATION => false,
            CURLOPT_MAXREDIRS => 0
        ]);

        // Configure TLS/SSL options
        if ($scheme === 'https') {
            if ($hasClientCerts) {
                // Use client certificates for mutual TLS authentication
                $sslVerifyHost = $isInternal ? 0 : 2; // Disable hostname verification for internal hosts
                curl_setopt_array($ch, [
                    CURLOPT_SSLCERT => $clientCertPath,
                    CURLOPT_SSLKEY => $clientKeyPath,
                    CURLOPT_CAINFO => $caCertPath,
                    CURLOPT_SSL_VERIFYPEER => true,
                    CURLOPT_SSL_VERIFYHOST => $sslVerifyHost
                ]);
                secure_log("API Client: Using mutual TLS with client certificates for cURL request (hostname verification: " . ($sslVerifyHost ? "enabled" : "disabled") . ")");
            } else {
                // For internal hosts, default to insecure TLS unless explicitly disabled
                $insecureTlsEnv = getenv('API_INSECURE_INTERNAL_TLS');
                $allowInsecure = $isInternal && ($insecureTlsEnv === false || strtolower((string)$insecureTlsEnv) !== 'false');

                if ($allowInsecure) {
                    curl_setopt_array($ch, [
                        CURLOPT_SSL_VERIFYPEER => false,
                        CURLOPT_SSL_VERIFYHOST => 0
                    ]);
                    secure_log("API Client: Using insecure TLS for cURL request (no client certs found)");
                } else {
                    curl_setopt_array($ch, [
                        CURLOPT_SSL_VERIFYPEER => true,
                        CURLOPT_SSL_VERIFYHOST => 2
                    ]);
                    secure_log("API Client: Using secure TLS for cURL request");
                }
            }
        }

        if ($data && in_array($method, ['POST','PUT','PATCH'], true)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($response === false || !empty($curlError)) {
            secure_log("API Client: cURL request failed - " . ($curlError ?: 'Unknown error'));
            throw new Exception('API request failed: ' . ($curlError ?: 'Unknown error'));
        }

        // Check HTTP response code
        if ($httpCode >= 400) {
            secure_log("API Client: HTTP error {$httpCode} - {$response}");
            throw new Exception("HTTP {$httpCode} error: {$response}");
        }

        $decoded = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            secure_log("API Client: JSON decode error - " . json_last_error_msg());
            throw new Exception('Invalid JSON response from API');
        }

        return $decoded;
    }


    /**
     * Search for a player by username
     * First converts username to UUID via database, then fetches data from API
     */
    public function searchPlayer($username) {
        try {
            if (empty($username)) {
                return ['error' => 'Username is required', 'status' => 400];
            }

            // First, get the UUID from the database (this doesn't call the API)
            require_once __DIR__ . '/db_access.php';
            $db = new DatabaseAccess();
            secure_log("API Client: Looking up UUID for username: {$username}");
            $uuid_result = $db->get_player_uuid($username);
            secure_log("API Client: UUID lookup result: " . json_encode($uuid_result));

            if (isset($uuid_result['error'])) {
                secure_log("API Client: Failed to get UUID for username {$username}: " . $uuid_result['error']);
                return $uuid_result; // Return the error from database lookup
            }

            if (!isset($uuid_result['uuid'])) {
                secure_log("API Client: No UUID found for username {$username}");
                return ['error' => 'Player not found', 'status' => 404];
            }

            // Make a single API call to get the player data using the UUID
            $uuid = $uuid_result['uuid'];
            secure_log("API Client: About to call Go API with UUID: {$uuid} for username: {$username}");
            $endpoint = 'api/players/' . urlencode($uuid);
            secure_log("API Client: Full endpoint URL: {$endpoint}");
            $response = $this->makeRequest($endpoint);

            if (isset($response['data'])) {
                secure_log("API Client: Successfully found player data for username {$username} (UUID: {$uuid_result['uuid']})");
                return $response['data'];
            } else {
                secure_log("API Client: No data field in response for UUID {$uuid_result['uuid']}");
                return ['error' => 'Invalid response format from API', 'status' => 500];
            }

        } catch (Exception $e) {
            secure_log("API Client: Error searching for player {$username}: " . $e->getMessage());
            return ['error' => 'Failed to search for player: ' . $e->getMessage(), 'status' => 500];
        }
    }

    /**
     * Get player data from Go API
     */
    public function getPlayerData($uuid) {
        try {
            if (empty($uuid)) {
                return ['error' => 'UUID is required', 'status' => 400];
            }

            $endpoint = 'api/players/' . urlencode($uuid);
            $response = $this->makeRequest($endpoint);

            if (isset($response['data'])) {
                secure_log("API Client: Successfully retrieved player data for UUID {$uuid}");
                return $response['data'];
            } else {
                secure_log("API Client: No data field in response for UUID {$uuid}");
                return ['error' => 'Invalid response format from API', 'status' => 500];
            }

        } catch (Exception $e) {
            secure_log("API Client: Error getting player data for UUID {$uuid}: " . $e->getMessage());
            return ['error' => 'Failed to retrieve player data: ' . $e->getMessage(), 'status' => 500];
        }
    }

    /**
     * Get faction data from Go API
     */
    public function getFactionData($factionName) {
        try {
            if (empty($factionName)) {
                return ['error' => 'Faction name is required', 'status' => 400];
            }

            $endpoint = 'api/factions/' . urlencode($factionName);
            $response = $this->makeRequest($endpoint);

            if (isset($response['data'])) {
                secure_log("API Client: Successfully retrieved faction data for {$factionName}");
                return $response['data'];
            } else {
                secure_log("API Client: No data field in faction response for {$factionName}");
                return ['error' => 'Faction not found', 'status' => 404];
            }

        } catch (Exception $e) {
            secure_log("API Client: Error getting faction data for {$factionName}: " . $e->getMessage());
            return ['error' => 'Failed to retrieve faction data: ' . $e->getMessage(), 'status' => 500];
        }
    }



    /**
     * Update player data using Go API
     */
    public function updatePlayerData($playerUuid, $playerData) {
        try {
            if (empty($playerUuid)) {
                return ['error' => 'Player UUID is required', 'status' => 400];
            }

            $endpoint = 'api/players/update';
            $response = $this->makeRequest($endpoint, 'POST', $playerData);

            secure_log("API Client: Successfully updated player data for UUID: {$playerUuid}");
            return ['success' => true, 'response' => $response];

        } catch (Exception $e) {
            secure_log("API Client: Error updating player data for {$playerUuid}: " . $e->getMessage());
            return ['error' => 'Failed to update player data: ' . $e->getMessage(), 'status' => 500];
        }
    }

    /**
     * Update player faction data in Go API to clear faction information
     */
    public function updatePlayerFactionData($playerUuid, $clearFaction = true) {
        try {
            if (empty($playerUuid)) {
                return ['error' => 'Player UUID is required', 'status' => 400];
            }

            if ($clearFaction) {
                // Use the new approach with existing Go API endpoints
                try {
                    // Get current player data
                    $playerData = $this->getPlayerData($playerUuid);

                    if (isset($playerData['Faction'])) {
                        // Clear faction data
                        $playerData['Faction']['Name'] = "";
                        $playerData['Faction']['Role'] = 0; // Member role

                        // Update player using Go API
                        return $this->updatePlayerData($playerUuid, $playerData);
                    }

                    return ['success' => true, 'message' => 'No faction data to clear'];

                } catch (Exception $e) {
                    secure_log("API Client: New approach failed for player {$playerUuid}: " . $e->getMessage());
                    // Fall back to old approaches below
                }
            }

            // Fallback: Try multiple approaches to update player faction data
            $success = false;
            $lastError = '';

            // Approach 1: Use dedicated faction update endpoint (may not exist)
            try {
                $updateData = [
                    'faction' => [
                        'Name' => '',
                        'Role' => 0,
                        'Request' => null
                    ]
                ];
                $endpoint = 'api/players/' . urlencode($playerUuid) . '/faction';
                $response = $this->makeRequest($endpoint, 'PUT', $updateData);
                $success = true;
                secure_log("API Client: Successfully updated faction data for player {$playerUuid} via faction endpoint");
            } catch (Exception $e) {
                $lastError = $e->getMessage();
                secure_log("API Client: Faction endpoint failed for player {$playerUuid}: " . $lastError);
            }

            // Approach 2: Use general player update endpoint
            if (!$success) {
                try {
                    $updateData = [
                        'Faction' => [
                            'Name' => '',
                            'Role' => 0,
                            'Request' => null
                        ]
                    ];
                    $endpoint = 'api/players/' . urlencode($playerUuid);
                    $response = $this->makeRequest($endpoint, 'PATCH', $updateData);
                    $success = true;
                    secure_log("API Client: Successfully updated faction data for player {$playerUuid} via player endpoint");
                } catch (Exception $e) {
                    $lastError = $e->getMessage();
                    secure_log("API Client: Player endpoint failed for player {$playerUuid}: " . $lastError);
                }
            }

            // Approach 3: Use admin command to force update
            if (!$success) {
                try {
                    $commandData = [
                        'command' => 'player_update_faction',
                        'parameters' => [
                            'player_uuid' => $playerUuid,
                            'faction_name' => '',
                            'faction_role' => 0,
                            'admin_forced' => true
                        ]
                    ];
                    $endpoint = 'api/admin/execute';
                    $response = $this->makeRequest($endpoint, 'POST', $commandData);
                    $success = true;
                    secure_log("API Client: Successfully updated faction data for player {$playerUuid} via admin command");
                } catch (Exception $e) {
                    $lastError = $e->getMessage();
                    secure_log("API Client: Admin command failed for player {$playerUuid}: " . $lastError);
                }
            }

            if ($success) {
                return ['success' => true, 'data' => $response ?? null];
            } else {
                return ['error' => 'All update approaches failed: ' . $lastError, 'status' => 500];
            }

        } catch (Exception $e) {
            secure_log("API Client: Error updating player faction data for {$playerUuid}: " . $e->getMessage());
            return ['error' => 'Failed to update player faction data: ' . $e->getMessage(), 'status' => 500];
        }
    }

    /**
     * Notify Go API about faction deletion to properly disband the faction
     * Also updates all faction members' player data
     */
    public function notifyFactionDeletion($factionName) {
        try {
            if (empty($factionName)) {
                return ['error' => 'Faction name is required', 'status' => 400];
            }

            // First, get all faction members before deletion
            $factionMembers = [];
            try {
                $factionData = $this->getFactionData($factionName);
                if (isset($factionData['Members']) && is_array($factionData['Members'])) {
                    $factionMembers = $factionData['Members'];
                    secure_log("API Client: Found " . count($factionMembers) . " members in faction {$factionName}");
                }
            } catch (Exception $e) {
                secure_log("API Client: Could not get faction members before deletion: " . $e->getMessage());
            }

            // Use existing Go API endpoints to properly handle faction disband
            $success = false;
            $warnings = [];

            // Step 1: Update all faction members to clear their faction data using the existing API
            if (!empty($factionMembers)) {
                secure_log("API Client: Clearing faction data for " . count($factionMembers) . " members using Go API", "info");

                foreach ($factionMembers as $member) {
                    if (isset($member['UUID']) && !empty($member['UUID'])) {
                        try {
                            // Get current player data first
                            $playerData = $this->getPlayerData($member['UUID']);
                            if (isset($playerData['Faction'])) {
                                // Clear faction data
                                $playerData['Faction']['Name'] = "";
                                $playerData['Faction']['Role'] = 0; // Member role (database.Member = 0)

                                // Update player using existing Go API endpoint
                                $updateResult = $this->updatePlayerData($member['UUID'], $playerData);
                                if (isset($updateResult['success']) && $updateResult['success']) {
                                    secure_log("API Client: Successfully cleared faction data for player {$member['UUID']}", "info");
                                } else {
                                    $warnings[] = "Failed to clear faction data for player {$member['UUID']}";
                                }
                            }
                        } catch (Exception $e) {
                            secure_log("API Client: Error clearing faction data for player {$member['UUID']}: " . $e->getMessage(), "warning");
                            $warnings[] = "Error updating player {$member['UUID']}: " . $e->getMessage();
                        }
                    }
                }
            }

            // Step 2: Delete the faction using database operations (since Go API doesn't have delete endpoint)
            // The Go API will pick up the deletion when it next accesses the faction
            secure_log("API Client: Faction member data cleared via Go API, faction will be deleted via database", "info");
            $success = true;

            // Verification step: Check if faction still exists in Go API after database deletion
            // This helps confirm the coordination is working
            try {
                // Small delay to allow for any caching to clear
                usleep(500000); // 0.5 seconds

                $checkEndpoint = 'api/factions/' . urlencode($factionName);
                $checkResponse = $this->makeRequest($checkEndpoint, 'GET');

                if (isset($checkResponse['data']) && !empty($checkResponse['data'])) {
                    secure_log("API Client: Warning - Faction {$factionName} still exists in Go API cache", "warning");
                    $warnings[] = "Faction may still exist in Go API cache - will sync on next access";
                } else {
                    secure_log("API Client: Confirmed - Faction {$factionName} not found in Go API", "info");
                }
            } catch (Exception $e) {
                // If GET fails with 404, that's actually good - faction is deleted
                if (strpos($e->getMessage(), '404') !== false) {
                    secure_log("API Client: Faction {$factionName} not found in Go API (confirmed deleted)", "info");
                } else {
                    secure_log("API Client: Could not verify faction deletion in Go API: " . $e->getMessage(), "warning");
                    $warnings[] = "Could not verify Go API sync: " . $e->getMessage();
                }
            }



            // Player faction data has already been updated above using Go API
            $playerUpdateResults = [];
            if ($success && !empty($factionMembers)) {
                secure_log("API Client: Player faction data already updated via Go API for " . count($factionMembers) . " members");

                // Create summary of what was updated
                foreach ($factionMembers as $member) {
                    if (isset($member['UUID']) && !empty($member['UUID'])) {
                        $playerUpdateResults[] = [
                            'uuid' => $member['UUID'],
                            'username' => $member['Username'] ?? 'unknown',
                            'result' => ['success' => true, 'method' => 'go_api_update']
                        ];
                    }
                }
            }

            if ($success) {
                $result = ['success' => true];
                if (!empty($warnings)) {
                    $result['warnings'] = $warnings;
                }
                if (!empty($playerUpdateResults)) {
                    $result['player_updates'] = $playerUpdateResults;
                }
                return $result;
            } else {
                return [
                    'error' => 'All deletion approaches failed',
                    'warnings' => $warnings,
                    'status' => 500
                ];
            }

        } catch (Exception $e) {
            secure_log("API Client: Error notifying faction deletion for {$factionName}: " . $e->getMessage());
            return ['error' => 'Failed to notify API: ' . $e->getMessage(), 'status' => 500];
        }
    }

    /**
     * Trigger Go API cache refresh by making a simple request
     * This helps ensure the Go API picks up database changes
     */
    public function triggerCacheRefresh() {
        try {
            // Make a simple request to trigger any cache refresh mechanisms
            $response = $this->makeRequest('api/health');
            secure_log("API Client: Successfully triggered cache refresh", "info");
            return ['success' => true, 'message' => 'Cache refresh triggered'];
        } catch (Exception $e) {
            secure_log("API Client: Failed to trigger cache refresh: " . $e->getMessage(), "warning");
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get jackpot data from Go API with caching
     */
    public function getJackpotData() {
        try {
            $cache_key = 'jackpot_data';
            $now = time();

            // Check cache first
            if (isset(self::$cache[$cache_key]) &&
                isset(self::$cache[$cache_key]['timestamp']) &&
                ($now - self::$cache[$cache_key]['timestamp']) < self::$cache_ttl) {

                secure_log("API Client: Using cached jackpot data");
                return self::$cache[$cache_key]['data'];
            }

            $response = $this->makeRequest('api/jackpot');

            if (isset($response['amount'])) {
                secure_log("API Client: Successfully retrieved jackpot data");

                // Cache the response
                self::$cache[$cache_key] = [
                    'data' => $response,
                    'timestamp' => $now
                ];

                return $response;
            } else {
                secure_log("API Client: No amount field in jackpot response");
                return ['error' => 'Invalid response format from API', 'status' => 500];
            }

        } catch (Exception $e) {
            secure_log("API Client: Error getting jackpot data: " . $e->getMessage());
            return ['error' => 'Failed to retrieve jackpot data: ' . $e->getMessage(), 'status' => 500];
        }
    }

    /**
     * Test API connection
     */
    public function testConnection() {
        try {
            // Try a simple endpoint to test connectivity
            $response = $this->makeRequest('api/health');
            return ['success' => true, 'response' => $response];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Search for players with partial matching
     *
     * @param string $query Search query
     * @param int $limit Maximum number of results
     * @return array Array of matching players
     */
    public function searchPlayersPartial($query, $limit = 5) {
        try {
            $endpoint = 'api/players/search';
            $params = [
                'query' => $query,
                'partial' => true,
                'limit' => $limit
            ];

            $url_params = http_build_query($params);
            $response = $this->makeRequest($endpoint . '?' . $url_params);

            if (isset($response['players']) && is_array($response['players'])) {
                $results = [];
                foreach ($response['players'] as $player) {
                    $results[] = [
                        'username' => $player['username'] ?? $player['Username'] ?? 'Unknown',
                        'uuid' => $player['uuid'] ?? $player['UUID'] ?? '',
                        'known_names' => $player['known_names'] ?? $player['KnownNames'] ?? [],
                        'last_seen' => $player['last_seen'] ?? $player['LastSeen'] ?? null
                    ];
                }
                return $results;
            }

            return [];

        } catch (Exception $e) {
            secure_log("API Client: Error in searchPlayersPartial: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Search for factions with partial matching
     *
     * @param string $query Search query
     * @param int $limit Maximum number of results
     * @return array Array of matching factions
     */
    public function searchFactionsPartial($query, $limit = 5) {
        try {
            $endpoint = 'api/factions/search';
            $params = [
                'query' => $query,
                'partial' => true,
                'limit' => $limit
            ];

            $url_params = http_build_query($params);
            $response = $this->makeRequest($endpoint . '?' . $url_params);

            if (isset($response['factions']) && is_array($response['factions'])) {
                $results = [];
                foreach ($response['factions'] as $faction) {
                    $results[] = [
                        'faction_name' => $faction['name'] ?? $faction['Name'] ?? 'Unknown',
                        'leader' => $faction['leader'] ?? $faction['Leader'] ?? 'Unknown',
                        'member_count' => $faction['member_count'] ?? $faction['MemberCount'] ?? 0,
                        'created_at' => $faction['created_at'] ?? $faction['CreatedAt'] ?? null
                    ];
                }
                return $results;
            }

            return [];

        } catch (Exception $e) {
            secure_log("API Client: Error in searchFactionsPartial: " . $e->getMessage());
            return [];
        }
    }
}