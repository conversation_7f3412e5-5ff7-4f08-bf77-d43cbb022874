RewriteEngine On
RewriteBase /

# Block direct access to non-minified JavaScript files
<IfModule mod_rewrite.c>
    RewriteCond %{REQUEST_URI} \.js$ [NC]
    RewriteCond %{REQUEST_URI} !\.min\.js$ [NC]
    RewriteRule ^ - [F,L]
</IfModule>


# Security headers
<IfModule mod_headers.c>
    # Content Security Policy


    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"

    # Prevent clickjacking
    Header always set X-Frame-Options "DENY"

    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"

    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Permissions policy
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"

    # HTTP Strict Transport Security (HSTS) - ALWAYS apply on HTTPS
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
</IfModule>

# Block access to source JavaScript files (allow only minified versions)
# Temporarily disabled due to configuration conflicts
# <FilesMatch "\.js$">
#     Order deny,allow
#     Deny from all
# </FilesMatch>
#
# <FilesMatch "\.min\.js$">
#     Order allow,deny
#     Allow from all
# </FilesMatch>

# Block malicious attack patterns
<IfModule mod_rewrite.c>
    # Block IoT/router exploit attempts
    RewriteCond %{REQUEST_URI} device\.rsp [NC,OR]
    RewriteCond %{REQUEST_URI} setup\.cgi [NC,OR]
    RewriteCond %{REQUEST_URI} \.env [NC,OR]
    RewriteCond %{QUERY_STRING} (wget|curl|chmod|rm\s|cd\s|\.\./) [NC,OR]
    RewriteCond %{QUERY_STRING} (urbotnet|botnet|malware) [NC,OR]
    RewriteCond %{QUERY_STRING} (base64_decode|eval|exec|system) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<script|\<iframe|\<object) [NC]
    RewriteRule .* - [F,L]

    # Block suspicious user agents
    RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
    RewriteCond %{HTTP_USER_AGENT} (Mozila/5\.0)$ [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (bot|crawler|spider|scraper) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# Block access to development/build files
RewriteRule ^(.*/)?obfuscate\.js$ - [F,L]

# Allow access to .well-known directory for Microsoft identity association
RewriteCond %{REQUEST_URI} ^/\.well-known/microsoft-identity-association\.json$ [NC]
RewriteRule ^ - [L]

# API endpoints - handle SSE stream endpoints
<Files "recent-winners-stream">
    ForceType application/x-httpd-php
</Files>
<Files "jackpot-stream">
    ForceType application/x-httpd-php
</Files>
<Files "vote-leaderboard">
    ForceType application/x-httpd-php
</Files>

# API endpoints - preserve .php extension for API calls
RewriteCond %{REQUEST_URI} ^/api/ [NC]
RewriteRule ^ - [L]

# Auth endpoints - clean URLs
RewriteRule ^auth/player-login/?$ auth/player-login.php [L,QSA]
RewriteRule ^auth/player-logout/?$ auth/player-logout.php [L,QSA]
RewriteRule ^auth/player-callback/?$ auth/player-callback.php [L,QSA]
RewriteRule ^auth/xbox-oauth/?$ auth/xbox-oauth.php [L,QSA]
RewriteRule ^auth/xbox-callback/?$ auth/xbox-callback.php [L,QSA]
RewriteRule ^auth/test-xbox/?$ auth/test-xbox.php [L,QSA]

# Account portal endpoints - clean URLs
RewriteRule ^account/?$ account/index.php [L,QSA]
RewriteRule ^account/appeals/?$ account/appeals.php [L,QSA]
RewriteRule ^account/reports/?$ account/reports.php [L,QSA]
RewriteRule ^account/tickets/?$ account/tickets.php [L,QSA]
RewriteRule ^account/punishments/?$ account/punishments.php [L,QSA]
RewriteRule ^account/staff-applications/?$ account/staff-applications.php [L,QSA]
RewriteRule ^account/settings/?$ account/settings.php [L,QSA]

# Staff application form
RewriteRule ^staff-application/?$ staff-application.php [L,QSA]

# Help/Tickets system - clean URLs
RewriteRule ^help/?$ help.php [L,QSA]
RewriteRule ^help/conversations/?$ help/conversations.php [L,QSA]

# Leaderboards - clean URLs
RewriteRule ^leaderboards/balance/?$ leaderboards/balance.php [L,QSA]
RewriteRule ^leaderboards/bounties/?$ leaderboards/bounties.php [L,QSA]
RewriteRule ^leaderboards/deaths/?$ leaderboards/deaths.php [L,QSA]
RewriteRule ^leaderboards/kdr/?$ leaderboards/kdr.php [L,QSA]
RewriteRule ^leaderboards/kills/?$ leaderboards/kills.php [L,QSA]
RewriteRule ^leaderboards/killstreaks/?$ leaderboards/killstreaks.php [L,QSA]
RewriteRule ^leaderboards/vote/?$ leaderboards/vote.php [L,QSA]

# Faction leaderboards - clean URLs
RewriteRule ^leaderboards/factions/?$ leaderboards/factions/index.php [L,QSA]

# Main pages - clean URLs
RewriteRule ^appeal/?$ appeal.php [L,QSA]
RewriteRule ^appeal-confirmation/?$ appeal_confirmation.php [L,QSA]
RewriteRule ^faction/?$ faction.php [L,QSA]
RewriteRule ^faction/([^/]+)/?$ faction.php?name=$1 [L,QSA]
RewriteRule ^legal/?$ legal.php [L,QSA]
RewriteRule ^player/?$ player.php [L,QSA]
RewriteRule ^player/([^/]+)/?$ player.php?username=$1 [L,QSA]
RewriteRule ^report/?$ report.php [L,QSA]
RewriteRule ^rules/?$ rules.php [L,QSA]
RewriteRule ^search/?$ search.php [L,QSA]
RewriteRule ^services/?$ services.php [L,QSA]
RewriteRule ^success/?$ success.php [L,QSA]

# Form submission endpoints - keep .php for POST handling
RewriteRule ^submit-appeal/?$ submit_appeal.php [L,QSA]

# Error pages
RewriteRule ^404/?$ 404.php [L,QSA]
RewriteRule ^500/?$ 500.php [L,QSA]
RewriteRule ^503/?$ 503.php [L,QSA]

# Generic clean URL handler for remaining files
# This handles any other .php files not explicitly listed above
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/admin/ [NC]
RewriteCond %{REQUEST_URI} !^/api/ [NC]
RewriteCond %{REQUEST_URI} !\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ [NC]
RewriteRule ^([^/]+)/?$ $1.php [L,QSA]

# Redirect .php URLs to clean URLs (except for API, admin, and OAuth callbacks)
RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/([^.]+)\.php [NC]
RewriteCond %{REQUEST_URI} !^/admin/ [NC]
RewriteCond %{REQUEST_URI} !^/api/.*\.php$ [NC]
RewriteCond %{REQUEST_URI} !^/submit_appeal\.php [NC]
RewriteCond %{REQUEST_URI} !^/auth/player-callback\.php [NC]
RewriteCond %{REQUEST_URI} !^/auth/ [NC]
RewriteRule ^ /%1? [R=301,L]

# Prevent access to .well-known directory except for microsoft-identity-association.json
RewriteCond %{REQUEST_URI} ^/\.well-known/ [NC]
RewriteCond %{REQUEST_URI} !^/\.well-known/microsoft-identity-association\.json$ [NC]
RewriteRule ^ - [F,L]

# Prevent access to sensitive files
RewriteRule ^(.*/)?\.env$ - [F,L]
RewriteRule ^(.*/)?\.env\.example$ - [F,L]
RewriteRule ^(.*/)?composer\.json$ - [F,L]
RewriteRule ^(.*/)?composer\.lock$ - [F,L]
RewriteRule ^(.*/)?README\.md$ - [F,L]
RewriteRule ^(.*/)?package\.json$ - [F,L]
RewriteRule ^(.*/)?package-lock\.json$ - [F,L]
RewriteRule ^(.*/)?\.htaccess$ - [F,L]
RewriteRule ^(.*/)?\.htpasswd$ - [F,L]
RewriteRule ^(.*/)?config\.php$ - [F,L]
RewriteRule ^(.*/)?\.log$ - [F,L]
RewriteRule ^(.*/)?\.bak$ - [F,L]

# Prevent access to .git directory
RedirectMatch 404 /\.git

# Prevent directory listing
RewriteCond %{REQUEST_FILENAME} -d
RewriteCond %{REQUEST_FILENAME}/index.php !-f
RewriteCond %{REQUEST_FILENAME}/index.html !-f
RewriteRule ^(.*)$ - [F,L]

# Cache control for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
</IfModule>

# Disable caching for dynamic content
<FilesMatch "\.(php)$">
    Header set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
    Header set Pragma "no-cache"
    Header set Expires "0"
</FilesMatch>

# PHP settings
<IfModule mod_php7.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
    php_flag register_globals Off
    php_flag allow_url_fopen Off
    php_flag allow_url_include Off
    php_flag session.cookie_httponly On
    php_flag session.cookie_secure On
    php_flag session.use_only_cookies On
    php_value session.cookie_samesite "Lax"
</IfModule>