<?php
/**
 * Staff Application Status Update API
 * Update the status of a staff application
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/db_access.php';
require_once __DIR__ . '/../../includes/security.php';


header('Content-Type: application/json');


if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}


require_auth(ROLE_ADMIN);


require_csrf_check(true); // true for API mode

try {
    $application_id = $_POST['application_id'] ?? null;
    $status = $_POST['status'] ?? null;
    $reviewer_notes = $_POST['reviewer_notes'] ?? '';
    
    if (!$application_id || !$status) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Application ID and status are required']);
        exit;
    }
    
    // Validate status
    $valid_statuses = ['pending', 'approved', 'denied', 'under_review'];
    if (!in_array($status, $valid_statuses)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid status']);
        exit;
    }
    
    // Get database connection
    $connection = get_db_connection();
    $db = $connection['db'];

    if (!$db) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database connection failed']);
        exit;
    }

    // Check if application exists
    $application = $db->staff_applications->findOne([
        'application_id' => $application_id
    ]);

    if (!$application) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Application not found']);
        exit;
    }
    
    // Prepare update data
    $updateData = [
        'status' => $status,
        'reviewed_at' => new MongoDB\BSON\UTCDateTime(),
        'reviewed_by' => $_SESSION['discord_user_id']
    ];
    
    if (!empty($reviewer_notes)) {
        $updateData['reviewer_notes'] = sanitize_input($reviewer_notes);
    }
    
    // Update the application
    $result = $db->staff_applications->updateOne(
        ['application_id' => $application_id],
        ['$set' => $updateData]
    );

    if ($result->getModifiedCount() > 0) {
        // Log the action
        secure_log("Staff application status updated: {$application_id} from {$application['status']} to {$status} by " . ($_SESSION['discord_user_id'] ?? 'unknown'));

        // Send status update email to applicant
        try {
            $email_sent = send_staff_application_status_email($application_id, $application, $status, $reviewer_notes);
            if ($email_sent) {
                secure_log("Staff application status email sent for application: {$application_id}");
            } else {
                secure_log("Failed to send staff application status email for application: {$application_id}");
            }
        } catch (Exception $email_error) {
            secure_log("Error sending staff application status email: " . $email_error->getMessage());
        }

        echo json_encode([
            'success' => true,
            'message' => 'Application status updated successfully'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to update application status']);
    }
    
} catch (Exception $e) {
    secure_log("Error updating staff application status: " . $e->getMessage());

    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'An error occurred while updating the application']);
}
?>
