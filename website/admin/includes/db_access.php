<?php
/**
 * Database Access Layer for MongoDB
 * Provides functions to interact with MongoDB for the Massacre Portal admin panel
 */


require_once __DIR__ . '/config.php';
require_once __DIR__ . '/security.php';


require_once __DIR__ . '/../vendor/autoload.php';
use MongoDB\Client;
use MongoDB\BSON\UTCDateTime;
use MongoDB\BSON\ObjectId;

/**
 * Get MongoDB connection
 *
 * @return MongoDB\Client MongoDB client instance
 */
if (!function_exists('get_mongo_connection')) {
function get_mongo_connection() {
    static $client = null;


    if ($client !== null) {
        return $client;
    }


    $connection = get_db_connection();


    return $connection['client'];
}
}

/**
 * Get MongoDB connection
 *
 * @return array Connection object and database instance
 */
if (!function_exists('get_db_connection')) {
function get_db_connection() {
    static $connection = null;


    if ($connection !== null) {
        return $connection;
    }

    try {
        $mongo_uri = env('M<PERSON>GO_URI');
        $db_name = env('MONGO_DB', 'mmc'); // Default database name

        if (!$mongo_uri) {
            throw new Exception("MONGO_URI not set in environment configuration");
        }

        // Extract database name from URI if present
        $uri_parts = parse_url($mongo_uri);
        if (isset($uri_parts['path']) && strlen($uri_parts['path']) > 1) {
            $uri_db_name = substr($uri_parts['path'], 1);
            if (!empty($uri_db_name)) {
                $db_name = $uri_db_name;
            }
        }


        // Connection options - SSL disabled for local MongoDB
        $connection_options = [
            'connectTimeoutMS' => 3000,
            'serverSelectionTimeoutMS' => 3000,
            'retryWrites' => true,
            'w' => 'majority',
            'ssl' => false
        ];

        // Attempt connection and quick server selection test
        $client = new MongoDB\Client($mongo_uri, $connection_options);
        $client->listDatabases(['maxTimeMS' => 2000]);

        // Use 'mmc' database if available
        try {
            $dbs = [];
            foreach ($client->listDatabases() as $dbInfo) {
                $dbs[] = $dbInfo->getName();
            }
            if (in_array('mmc', $dbs)) {
                $db_name = 'mmc';
            }
        } catch (Exception $e) {
            // Continue with configured database name if listing fails
        }

        $db = $client->selectDatabase($db_name);

        $connection = ['client' => $client, 'db' => $db];
        return $connection;
    } catch (Exception $e) {
        secure_log("MongoDB connection error: " . $e->getMessage());
        return ['client' => null, 'db' => null];
    }
}
}

/**
 * Initialize database collections and indexes
 */
if (!function_exists('initialize_database')) {
function initialize_database() {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Failed to connect to database");
        }


        if (!collection_exists($db, 'staff_activity')) {
            $db->createCollection('staff_activity');
        }

        if (!collection_exists($db, 'staff_activity_log')) {
            $db->createCollection('staff_activity_log');
        }


        $db->staff_activity->createIndex(['last_active' => 1]);
        $db->staff_activity->createIndex(['user_id' => 1], ['unique' => true]);
        $db->staff_activity_log->createIndex(['timestamp' => 1]);


    } catch (Exception $e) {
        secure_log("Error initializing database: " . $e->getMessage());
    }
}
}

/**
 * Check if collection exists
 *
 * @param MongoDB\Database $db Database instance
 * @param string $collection_name Collection name
 * @return bool Whether collection exists
 */
if (!function_exists('collection_exists')) {
function collection_exists($db, $collection_name) {
    $collections = $db->listCollectionNames();
    foreach ($collections as $collection) {
        if ($collection === $collection_name) {
            return true;
        }
    }
    return false;
}
}

/**
 * Update staff activity
 *
 * @param string $user_id Discord user ID
 * @param string $username Discord username
 * @param string $role User role
 * @param string $avatar_hash Avatar hash
 * @param string $activity_type Optional activity type (e.g., 'login')
 * @param string $activity_details Optional activity details
 * @return bool Success status
 */
function update_staff_activity($user_id, $username, $role = null, $avatar_hash_or_url = null, $activity_type = null, $activity_details = null) {

    if (empty($user_id) || empty($username)) {
        return false;
    }


    $avatar_hash = $avatar_hash_or_url;
    if (!empty($avatar_hash_or_url) && strpos($avatar_hash_or_url, 'https://cdn.discordapp.com/avatars/') === 0) {

        $parts = explode('/', $avatar_hash_or_url);
        if (count($parts) >= 6) {
            $file_part = end($parts); // Gets "avatar_hash.png" or "avatar_hash.gif"
            $avatar_hash = pathinfo($file_part, PATHINFO_FILENAME); // Removes extension
        }
    }

    $connection = get_db_connection();
    $db = $connection['db'];

    if (!$db) {
        return false;
    }


    $now = new MongoDB\BSON\UTCDateTime(time() * 1000); // Convert to MongoDB format

    try {

        $one_hour_ago = new MongoDB\BSON\UTCDateTime((time() - 3600) * 1000);
        $recent_activity = $db->staff_activity->findOne([
            'user_id' => $user_id,
            'last_active' => ['$gte' => $one_hour_ago]
        ]);


        $db->staff_activity->updateOne(
            ['user_id' => $user_id],
            [
                '$set' => [
                    'username' => $username,
                    'role' => $role ?: 'STAFF', // Default to STAFF if role is null
                    'avatar_hash' => $avatar_hash,
                    'last_active' => $now
                ]
            ],
            ['upsert' => true]
        );


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('staff_activity_log', $collections)) {
            $db->createCollection('staff_activity_log');
            $db->staff_activity_log->createIndex(['timestamp' => -1]);
            $db->staff_activity_log->createIndex(['user_id' => 1]);
        }


        if ($activity_type === 'login') {

            $action = 'Logged in to admin panel';
            $type = 'login';
            $description = 'User logged in to the admin panel';
            $details = $activity_details ?: 'New login session started';
        } else if ($activity_type && $activity_type !== 'login') {

            $action = $activity_details ?: 'Performed ' . $activity_type . ' action';
            $type = $activity_type;
            $description = $activity_details ?: 'User performed ' . $activity_type . ' action';
            $details = $activity_details ?: '';
        } else {

            if ($recent_activity) {
                return true;
            }


            $action = 'Started new session';
            $type = 'session';
            $description = 'User started a new session';
            $details = 'Session activity after period of inactivity';
        }


        $db->staff_activity_log->insertOne([
            'user_id' => $user_id,
            'username' => $username,
            'action' => $action,
            'type' => $type,
            'timestamp' => $now,
            'description' => $description,
            'target' => '',
            'details' => $details
        ]);



        return true;
    } catch (Exception $e) {
        secure_log("Error updating staff activity: " . $e->getMessage());
        return false;
    }
}
/**
 * Get online staff
 *
 * @return array Online staff members
 */
function get_online_staff() {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }


        $online_threshold = new DateTime('now', new DateTimeZone('UTC'));
        $online_threshold->modify('-10 minutes');


        $cursor = $db->staff_activity->find([
            'last_active' => ['$gte' => $online_threshold],
            'role' => ['$ne' => 'UNAUTHORIZED'] // Exclude unauthorized users
        ], ['projection' => ['_id' => 0]]);

        $cursor->sort(['last_active' => -1]);

        $online_staff = [];
        foreach ($cursor as $staff) {

            $staff_array = (array)$staff;


            if (!empty($staff_array['avatar_hash'])) {
                $user_id = $staff_array['user_id'];
                $avatar_hash = $staff_array['avatar_hash'];
                $is_animated = strpos($avatar_hash, 'a_') === 0;
                $extension = $is_animated ? 'gif' : 'png';
                $staff_array['avatar_url'] = "https://cdn.discordapp.com/avatars/{$user_id}/{$avatar_hash}.{$extension}";
            }

            $online_staff[] = $staff_array;
        }

        return $online_staff;
    } catch (Exception $e) {
        secure_log("Error getting online staff: " . $e->getMessage());
        return [];
    }
}

/**
 * Get all reports
 *
 * @param int $limit Maximum number of reports to return
 * @return array Reports
 */
if (!function_exists('get_reports')) {
function get_reports($limit = 100, $offset = 0) {
    try {
        $connection = get_db_connection();
        if (!$connection) {
            throw new Exception("Failed to get database connection");
        }

        $db = $connection['db'];
        if (!$db) {
            throw new Exception("Database connection failed");
        }

        // Find reports collection
        try {
            $collections = [];
            foreach ($db->listCollections() as $collectionInfo) {
                $collections[] = $collectionInfo->getName();
            }

            $possible_collection_names = ['reports', 'Reports', 'player_reports', 'PlayerReports'];
            $collection_name = null;

            foreach ($possible_collection_names as $name) {
                if (in_array($name, $collections)) {
                    $collection_name = $name;
                    break;
                }
            }

            if (!$collection_name) {
                return [];
            }
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'No servers yet eligible for rescan') !== false) {
                return [];
            }
            throw $e;
        }

        // Query options with field projection and pagination to reduce memory usage
        $options = [
            'sort' => ['created_at' => -1],
            'limit' => min($limit, 50), // Further reduced max limit to prevent memory issues
            'skip' => max($offset, 0), // Add pagination support
            'maxTimeMS' => 5000, // Reduced timeout to 5 seconds
            'projection' => [ // Only fetch essential fields to reduce memory usage
                '_id' => 1,
                'report_id' => 1,
                'offender_name' => 1,
                'reporter_name' => 1,
                'rule_broken' => 1, // Essential field for display
                'status' => 1,
                'created_at' => 1,
                'processed_at' => 1,
                'processed_by_name' => 1,
                'priority' => 1,
                'evidence' => 1, // Include evidence field for display
                'report_type' => 1 // Include report type for faction/player distinction
                // Exclude other large fields like detailed screenshots, etc. to save memory
            ]
        ];

        $collection = $db->$collection_name;

        try {
            $cursor = $collection->find([], $options);
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'No servers yet eligible for rescan') !== false) {
                return [];
            }
            throw $e;
        }

        $reports = [];
        $count = 0;
        $batch_size = 50; // Process in batches to manage memory

        foreach ($cursor as $report) {
            $count++;

            $report_array = (array)$report;

            // Generate report_id if missing
            if (!isset($report_array['report_id'])) {
                $report_array['report_id'] = (string)$report_array['_id'];
            }

            // Format created_at date
            if (isset($report_array['created_at']) && $report_array['created_at'] instanceof MongoDB\BSON\UTCDateTime) {
                $date = $report_array['created_at']->toDateTime();
                $report_array['created_at'] = $date->format('Y-m-d H:i:s');
            }

            // Format processed_at date if exists
            if (isset($report_array['processed_at']) && $report_array['processed_at'] instanceof MongoDB\BSON\UTCDateTime) {
                $date = $report_array['processed_at']->toDateTime();
                $report_array['processed_at'] = $date->format('Y-m-d H:i:s');
            }

            // Check if the report has an email (for has_email flag)
            $has_email = isset($report_array['reporter_email']) || isset($report_array['reporters_email']);
            if ($has_email) {
                $report_array['has_email'] = true;
            }

            // Remove sensitive fields
            unset($report_array['ip_address']);
            unset($report_array['reporter_email']);
            unset($report_array['reporters_email']);

            $reports[] = $report_array;

            // Garbage collection every batch_size reports
            if ($count % $batch_size === 0) {
                gc_collect_cycles();
            }
        }

        // Final garbage collection
        gc_collect_cycles();

        return $reports;
    } catch (Exception $e) {
        secure_log("Error getting reports: " . $e->getMessage());
        throw $e;
    }
}
}

/**
 * Handle report action (accept/reject)
 *
 * @param string $report_id Report ID
 * @param string $action Action (accept/reject)
 * @param string $staff_id Staff ID
 * @param string $staff_name Staff name
 * @return array Result with success status and message
 */
if (!function_exists('handle_report_action')) {
function handle_report_action($report_id, $action, $staff_id, $staff_name) {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }

        // Find reports collection
        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        $possible_collection_names = ['reports', 'Reports', 'player_reports', 'PlayerReports'];
        $collection_name = null;

        foreach ($possible_collection_names as $name) {
            if (in_array($name, $collections)) {
                $collection_name = $name;
                break;
            }
        }

        if (!$collection_name) {
            throw new Exception("Reports collection not found");
        }

        $collection = $db->$collection_name;


        $report = null;


        $report = $collection->findOne(['report_id' => $report_id]);


        if (!$report) {
            $report = $collection->findOne(['_id' => $report_id]);
        }


        if (!$report && strlen($report_id) == 24) {
            try {
                $objectId = new MongoDB\BSON\ObjectId($report_id);
                $report = $collection->findOne(['_id' => $objectId]);
            } catch (Exception $e) {
                secure_log("Failed to convert to ObjectId: " . $e->getMessage());
            }
        }

        if (!$report) {
            secure_log("Report not found with ID: $report_id");
            return ['success' => false, 'message' => 'Report not found'];
        }


        if ($report['status'] !== 'Pending') {
            return ['success' => false, 'message' => 'Report is already processed'];
        }


        $new_status = ($action === 'accept') ? 'Accepted' : 'Denied';
        $processed_at = new MongoDB\BSON\UTCDateTime(time() * 1000);


        $processed_date = $processed_at->toDateTime();
        $processed_date->setTimezone(new DateTimeZone('America/New_York'));
        $processed_date_formatted = $processed_date->format('c'); // ISO 8601 format



        $collection->updateOne(
            ['_id' => $report['_id']],
            [
                '$set' => [
                    'status' => $new_status,
                    'processed_by' => $staff_id,
                    'processed_by_name' => $staff_name,
                    'processed_at' => $processed_at,
                    'processed_at_formatted' => $processed_date_formatted
                ]
            ]
        );


        try {

            $collections = [];
            foreach ($db->listCollections() as $collectionInfo) {
                $collections[] = $collectionInfo->getName();
            }

            if (!in_array('staff_activity_log', $collections)) {
                $db->createCollection('staff_activity_log');
                $db->staff_activity_log->createIndex(['timestamp' => -1]);
                $db->staff_activity_log->createIndex(['user_id' => 1]);
            }


            $activity = [
                'user_id' => $staff_id,
                'username' => $staff_name,
                'action' => $new_status === 'Accepted' ? 'Accepted report for ' . ($report['offender_name'] ?? 'Unknown Player') :
                                                        'Denied report for ' . ($report['offender_name'] ?? 'Unknown Player'),
                'type' => 'report',
                'target' => $report['offender_name'] ?? 'Unknown Player',
                'details' => "Report ID: " . ($report['report_id'] ?? $report_id) .
                             "\nReason: " . ($report['reason'] ?? 'Not specified') .
                             "\nReporter: " . ($report['reporter_name'] ?? 'Anonymous'),
                'timestamp' => $processed_at,
                'description' => ($new_status === 'Accepted' ? 'Accepted' : 'Denied') . " report against " . ($report['offender_name'] ?? 'Unknown Player')
            ];


            $db->staff_activity_log->insertOne($activity);
        } catch (Exception $e) {
            // Log activity error silently

        }


        $email = $report['reporter_email'] ?? $report['reporters_email'] ?? null;
        $email_sent = false;



        if (!empty($email)) {
            $email_sent = send_report_notification_email(
                $email,
                $report['reporter_name'] ?? 'Player',
                $report['offender_name'] ?? 'Unknown',
                $new_status,
                $report_id,
                $report['report_type'] ?? 'player'
            );
        }


        $success_message = "";
        $entity_type = ucfirst($report['report_type'] ?? 'player');
        if ($new_status === 'Accepted') {
            $success_message = "{$entity_type} report has been accepted successfully. The reporter will be notified that appropriate action has been taken.";
        } else {
            $success_message = "{$entity_type} report has been denied. The reporter will be notified that no action was deemed necessary at this time.";
        }


        if ($email_sent) {
            $success_message .= " Email notification sent successfully.";
        } else if (!empty($email)) {
            $success_message .= " However, the email notification could not be sent.";
        }

        return [
            'success' => true,
            'message' => $success_message,
            'notification_sent' => $email_sent,
            'status' => $new_status
        ];
    } catch (Exception $e) {
        secure_log("Error processing report {$report_id}: " . $e->getMessage());
        return [
            'success' => false,
            'message' => "Failed to {$action} report: " . $e->getMessage()
        ];
    }
}
}

/**
 * Log player note activity
 *
 * @param string $staff_id Staff ID
 * @param string $staff_name Staff name
 * @param string $player_name Player name
 * @param string $action Action (add/edit/delete)
 * @param string $note_content Note content
 * @param bool $is_important Whether the note is marked as important
 * @return bool Whether the activity was logged successfully
 */
function log_player_note_activity($staff_id, $staff_name, $player_name, $action, $note_content, $is_important = false) {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            secure_log("Database connection failed when logging player note activity");
            return false;
        }


        $now = new MongoDB\BSON\UTCDateTime(time() * 1000);


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('staff_activity_log', $collections)) {
            $db->createCollection('staff_activity_log');
            $db->staff_activity_log->createIndex(['timestamp' => -1]);
            $db->staff_activity_log->createIndex(['user_id' => 1]);
        }


        $action_text = '';
        switch ($action) {
            case 'add':
                $action_text = 'Added note for ' . $player_name;
                break;
            case 'edit':
                $action_text = 'Updated note for ' . $player_name;
                break;
            case 'delete':
                $action_text = 'Deleted note for ' . $player_name;
                break;
            default:
                $action_text = 'Modified note for ' . $player_name;
        }


        $activity = [
            'user_id' => $staff_id,
            'username' => $staff_name,
            'action' => $action_text,
            'type' => 'note',
            'target' => $player_name,
            'details' => "Note content: " . substr($note_content, 0, 100) . (strlen($note_content) > 100 ? '...' : '') .
                         ($is_important ? "\nMarked as important" : ""),
            'timestamp' => $now,
            'description' => $action_text
        ];


        $db->staff_activity_log->insertOne($activity);
        return true;
    } catch (Exception $e) {
        secure_log("Error logging player note activity: " . $e->getMessage());
        return false;
    }
}

/**
 * Log punishment activity
 *
 * @param string $staff_id Staff ID
 * @param string $staff_name Staff name
 * @param string $player_name Player name
 * @param string $action Action (add/remove)
 * @param string $punishment_type Type of punishment (ban/mute/warning)
 * @param string $reason Reason for punishment
 * @param string $duration Duration of punishment
 * @return bool Whether the activity was logged successfully
 */
function log_punishment_activity($staff_id, $staff_name, $player_name, $action, $punishment_type, $reason, $duration = 'permanent') {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            secure_log("Database connection failed when logging punishment activity");
            return false;
        }


        $now = new MongoDB\BSON\UTCDateTime(time() * 1000);


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('staff_activity_log', $collections)) {
            $db->createCollection('staff_activity_log');
            $db->staff_activity_log->createIndex(['timestamp' => -1]);
            $db->staff_activity_log->createIndex(['user_id' => 1]);
        }


        $action_text = '';
        if ($action === 'add') {
            switch ($punishment_type) {
                case 'ban':
                    $action_text = 'Banned ' . $player_name;
                    break;
                case 'mute':
                    $action_text = 'Muted ' . $player_name;
                    break;
                case 'warning':
                    $action_text = 'Warned ' . $player_name;
                    break;
                default:
                    $action_text = 'Punished ' . $player_name;
            }
        } else {
            $action_text = 'Removed punishment for ' . $player_name;
        }


        $activity = [
            'user_id' => $staff_id,
            'username' => $staff_name,
            'action' => $action_text,
            'type' => 'punishment',
            'target' => $player_name,
            'details' => "Type: " . ucfirst($punishment_type) .
                         "\nReason: " . $reason .
                         "\nDuration: " . ($duration === 'permanent' ? 'Permanent' : $duration),
            'timestamp' => $now,
            'description' => $action_text
        ];


        $db->staff_activity_log->insertOne($activity);
        return true;
    } catch (Exception $e) {
        secure_log("Error logging punishment activity: " . $e->getMessage());
        return false;
    }
}

/**
 * Log appeal activity
 *
 * @param string $staff_id Staff ID
 * @param string $staff_name Staff name
 * @param string $player_name Player name
 * @param string $action Action (accept/deny)
 * @param string $appeal_id Appeal ID
 * @param string $reason Reason for decision
 * @return bool Whether the activity was logged successfully
 */
function log_appeal_activity($staff_id, $staff_name, $player_name, $action, $appeal_id, $reason) {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            secure_log("Database connection failed when logging appeal activity");
            return false;
        }


        $now = new MongoDB\BSON\UTCDateTime(time() * 1000);


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('staff_activity_log', $collections)) {
            $db->createCollection('staff_activity_log');
            $db->staff_activity_log->createIndex(['timestamp' => -1]);
            $db->staff_activity_log->createIndex(['user_id' => 1]);
        }


        $action_text = '';
        if ($action === 'accept') {
            $action_text = 'Accepted appeal from ' . $player_name;
        } else {
            $action_text = 'Denied appeal from ' . $player_name;
        }


        $activity = [
            'user_id' => $staff_id,
            'username' => $staff_name,
            'action' => $action_text,
            'type' => 'appeal',
            'target' => $player_name,
            'details' => "Appeal ID: " . $appeal_id .
                         "\nReason: " . $reason,
            'timestamp' => $now,
            'description' => $action_text
        ];


        $db->staff_activity_log->insertOne($activity);
        return true;
    } catch (Exception $e) {
        secure_log("Error logging appeal activity: " . $e->getMessage());
        return false;
    }
}

/**
 * Log settings activity
 *
 * @param string $staff_id Staff ID
 * @param string $staff_name Staff name
 * @param string $setting_type Type of setting changed
 * @param string $details Details of the change
 * @param string $target Target of the change (if applicable)
 * @return bool Whether the activity was logged successfully
 */
function log_settings_activity($staff_id, $staff_name, $setting_type, $details, $target = '') {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            secure_log("Database connection failed when logging settings activity");
            return false;
        }


        $now = new MongoDB\BSON\UTCDateTime(time() * 1000);


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('staff_activity_log', $collections)) {
            $db->createCollection('staff_activity_log');
            $db->staff_activity_log->createIndex(['timestamp' => -1]);
            $db->staff_activity_log->createIndex(['user_id' => 1]);
        }


        $action_text = 'Updated ' . $setting_type . ' settings';
        if (!empty($target)) {
            $action_text .= ' for ' . $target;
        }


        $activity = [
            'user_id' => $staff_id,
            'username' => $staff_name,
            'action' => $action_text,
            'type' => 'settings',
            'target' => $target,
            'details' => $details,
            'timestamp' => $now,
            'description' => $action_text
        ];


        $db->staff_activity_log->insertOne($activity);
        return true;
    } catch (Exception $e) {
        secure_log("Error logging settings activity: " . $e->getMessage());
        return false;
    }
}

/**
 * Send email notification for appeal status change
 *
 * @param string $email Recipient email
 * @param string $player_name Player name
 * @param string $status New appeal status (Approved/Denied)
 * @param string $appeal_id Appeal ID
 * @param string $punishment_id Punishment ID
 * @param string $staff_notes Optional staff notes about the decision
 * @return bool Whether email was sent successfully
 */
function send_appeal_status_notification_email($email, $player_name, $status, $appeal_id, $punishment_id, $staff_notes = '') {

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        secure_log("EMAIL ERROR: Invalid email format: " . $email);
        return false;
    }


    // Load environment configuration with support for special characters
    require_once __DIR__ . '/../../includes/config_parser.php';

    try {
        $env = load_env_config(['/etc/massacremc/config/admin.env']);
        secure_log("Loaded admin.env config file for email notification");
    } catch (Exception $e) {
        secure_log("EMAIL ERROR: Config file error - " . $e->getMessage());
        return false;
    }

    $api_key = $env['MAILGUN_API_KEY'] ?? '';
    $domain = $env['MAILGUN_DOMAIN'] ?? '';
    $from_email = $env['SENDER_EMAIL'] ?? '<EMAIL>';




    if (!$api_key || !$domain || !$from_email) {
        secure_log("Email notification failed: Missing Mailgun configuration");
        return false;
    }


    $now = new DateTime();
    $now->setTimezone(new DateTimeZone('America/New_York'));
    $timestamp = $now->format('F d, Y \a\t h:i A') . ' ET';


    $status_text = ($status === 'Approved') ? 'Approved' : 'Denied';
    $subject = "🔔 MassacreMC - Appeal #{$appeal_id} {$status_text}";


    $status_color = ($status === 'Approved') ? '#28a745' : '#dc3545';
    $status_icon = ($status === 'Approved') ? '✅' : '❌';
    $status_bg_color = ($status === 'Approved') ? '#e8f5e9' : '#ffebee';
    $status_border_color = ($status === 'Approved') ? '#c8e6c9' : '#ffcdd2';


    $status_message = '';
    if ($status === 'Approved') {
        $status_message = "Your appeal has been <strong>approved</strong>. The punishment has been removed.";
    } else {
        $status_message = "Your appeal has been <strong>denied</strong>. The punishment will remain in effect.";
    }


    $message_html = "
    <html>
        <head>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Appeal Status Update</title>
        </head>
        <body style='margin: 0; padding: 0; font-family: \"Segoe UI\", Arial, sans-serif; background-color: #121212; color: #e0e0e0;'>
            <div style='max-width: 600px; margin: 20px auto; background: #1e1e1e; border: 1px solid #333; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.3);'>
                <!-- Header with Logo -->
                <div style='background: linear-gradient(135deg, #7b1fa2, #4a148c); color: white; padding: 30px; text-align: center;'>
                    <img src='https://portal.massacremc.net/img/logo.png' alt='MassacreMC Logo' style='max-width: 150px; margin-bottom: 15px;'>
                    <h1 style='margin: 0; font-size: 28px; letter-spacing: 0.5px; text-transform: uppercase;'>Appeal Status Update</h1>
                    <p style='margin: 10px 0 0; font-size: 16px; opacity: 0.9;'>Reference: #{$appeal_id}</p>
                </div>

                <div style='padding: 40px 30px; background-color: #1e1e1e; border-bottom: 1px solid #333;'>
                    <!-- Status Banner -->
                    <div style='background-color: rgba(" . ($status === 'Approved' ? "40, 167, 69, 0.15" : "220, 53, 69, 0.15") . "); border-left: 5px solid {$status_color}; border-radius: 8px; padding: 20px; margin-bottom: 30px;'>
                        <h2 style='margin: 0 0 10px; color: {$status_color}; font-size: 24px; display: flex; align-items: center;'>
                            <span style='margin-right: 10px; font-size: 28px;'>{$status_icon}</span>
                            Appeal {$status_text}
                        </h2>
                        <p style='margin: 0; color: #e0e0e0; font-size: 16px; line-height: 1.6;'>{$status_message}</p>
                    </div>

                    <!-- Appeal Details Section -->
                    <h2 style='color: #bb86fc; font-size: 22px; margin-top: 0; margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 10px;'>
                        <span style='margin-right: 8px;'>📋</span> Appeal Details
                    </h2>

                    <div style='background-color: #252525; border: 1px solid #333; border-radius: 8px; padding: 25px; margin-bottom: 30px; box-shadow: 0 2px 5px rgba(0,0,0,0.2); border-left: 4px solid #bb86fc;'>
                        <div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>
                            <div style='margin-bottom: 15px;'>
                                <div style='color: #bb86fc; font-size: 14px; margin-bottom: 5px; font-weight: 600;'>Player Name</div>
                                <div style='color: #e0e0e0; font-weight: 600; font-size: 16px; background: #333; padding: 8px 12px; border-radius: 4px;'>{$player_name}</div>
                            </div>

                            <div style='margin-bottom: 15px;'>
                                <div style='color: #bb86fc; font-size: 14px; margin-bottom: 5px; font-weight: 600;'>Decision Date</div>
                                <div style='color: #e0e0e0; font-weight: 600; font-size: 16px; background: #333; padding: 8px 12px; border-radius: 4px;'>{$timestamp}</div>
                            </div>
                        </div>

                        <div style='margin-bottom: 15px;'>
                            <div style='color: #bb86fc; font-size: 14px; margin-bottom: 5px; font-weight: 600;'>Punishment ID</div>
                            <div style='color: #e0e0e0; font-weight: 600; font-family: monospace; font-size: 16px; background: #333; padding: 8px 12px; border-radius: 4px;'>{$punishment_id}</div>
                        </div>

                        <div style='margin-bottom: 0;'>
                            <div style='color: #bb86fc; font-size: 14px; margin-bottom: 5px; font-weight: 600;'>Appeal ID</div>
                            <div style='color: #e0e0e0; font-weight: 600; font-family: monospace; font-size: 16px; background: #333; padding: 8px 12px; border-radius: 4px;'>{$appeal_id}</div>
                        </div>
                    </div>";


    if (!empty($staff_notes)) {
        $message_html .= "
                    <div style='margin-bottom: 30px;'>
                        <h3 style='color: #bb86fc; font-size: 20px; margin-top: 0; margin-bottom: 15px; border-bottom: 2px solid #333; padding-bottom: 10px;'>
                            <span style='margin-right: 8px;'>📝</span> Staff Notes
                        </h3>
                        <div style='background-color: #252525; border-left: 3px solid #bb86fc; border-radius: 8px; padding: 20px; color: #e0e0e0; font-style: italic; line-height: 1.6; box-shadow: 0 2px 5px rgba(0,0,0,0.2);'>
                            \"{$staff_notes}\"
                        </div>
                    </div>";
    }


    if ($status === 'Approved') {
        $message_html .= "
                    <div style='margin-bottom: 30px;'>
                        <h3 style='color: #bb86fc; font-size: 20px; margin-top: 0; margin-bottom: 15px; border-bottom: 2px solid #333; padding-bottom: 10px;'>
                            <span style='margin-right: 8px;'>✨</span> Next Steps
                        </h3>
                        <div style='background-color: rgba(40, 167, 69, 0.15); border-left: 3px solid #28a745; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);'>
                            <h4 style='margin-top: 0; margin-bottom: 10px; color: #2ecc71;'>Your punishment has been removed</h4>
                            <p style='margin: 0; color: #e0e0e0; line-height: 1.6;'>You can now rejoin the server and continue playing.</p>
                        </div>
                        <p style='margin: 0; color: #e0e0e0; line-height: 1.6; padding: 0 10px;'>Thank you for your patience during this process. We appreciate your commitment to following our community guidelines and look forward to seeing you back in the game.</p>
                    </div>";
    } else {
        $message_html .= "
                    <div style='margin-bottom: 30px;'>
                        <h3 style='color: #bb86fc; font-size: 20px; margin-top: 0; margin-bottom: 15px; border-bottom: 2px solid #333; padding-bottom: 10px;'>
                            <span style='margin-right: 8px;'>ℹ️</span> Next Steps
                        </h3>
                        <div style='background-color: rgba(220, 53, 69, 0.15); border-left: 3px solid #dc3545; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);'>
                            <h4 style='margin-top: 0; margin-bottom: 10px; color: #e74c3c;'>The punishment will remain in effect</h4>
                            <p style='margin: 0; color: #e0e0e0; line-height: 1.6;'>If you believe there has been a misunderstanding, you can contact our support team for further assistance.</p>
                        </div>
                        <p style='margin: 0; color: #e0e0e0; line-height: 1.6; padding: 0 10px;'>We encourage you to review our <a href='https://portal.massacremc.net/rules' style='color: #bb86fc; text-decoration: none; font-weight: 600;'>server rules</a> to ensure a positive experience for everyone in our community. You may submit a new appeal after 30 days if your circumstances have changed.</p>
                    </div>";
    }


    $message_html .= "
                </div>
                <div style='background: linear-gradient(135deg, #7b1fa2, #4a148c); padding: 40px; text-align: center; border-top: 3px solid #7b1fa2;'>
                    <h3 style='color: #ffffff; margin-top: 0; font-size: 24px; margin-bottom: 20px; font-weight: 600; letter-spacing: 0.5px;'>Connect With Us</h3>

                    <div style='margin: 30px 0; text-align: left; max-width: 400px; margin-left: auto; margin-right: auto;'>
                        <ul style='list-style-type: none; padding: 0; margin: 0;'>
                            <li style='margin-bottom: 12px; line-height: 1.6;'>
                                <span style='color: #ff9800; font-weight: 600; margin-right: 5px;'>Server IP:</span>
                                <span style='color: #999999; font-weight: 600; font-family: monospace;'>play.massacremc.net</span>
                            </li>
                            <li style='margin-bottom: 12px; line-height: 1.6;'>
                                <span style='color: #ff9800; font-weight: 600; margin-right: 5px;'>Discord:</span>
                                <a href='https://discord.gg/mBjd5cXSxR' style='color: #999999; font-weight: 600; text-decoration: none; font-family: monospace;'>discord.gg/mBjd5cXSxR</a>
                            </li>
                            <li style='margin-bottom: 12px; line-height: 1.6;'>
                                <span style='color: #ff9800; font-weight: 600; margin-right: 5px;'>Website:</span>
                                <a href='https://portal.massacremc.net' style='color: #999999; font-weight: 600; text-decoration: none; font-family: monospace;'>portal.massacremc.net</a>
                            </li>
                        </ul>
                    </div>

                    <div style='margin-top: 35px; padding-top: 25px; border-top: 1px solid rgba(255,255,255,0.2);'>
                        <p style='margin: 0 0 15px; color: #ffffff; font-size: 16px;'>Need assistance? Email us at <a href='mailto:<EMAIL>' style='color: #ffffff; text-decoration: none; font-weight: 700; border-bottom: 2px solid rgba(255,255,255,0.4); padding-bottom: 2px;'><EMAIL></a></p>

                        <div style='background-color: rgba(0,0,0,0.2); border-radius: 8px; padding: 15px; margin-top: 20px; display: inline-block;'>
                            <p style='margin: 0 0 5px; font-size: 13px; color: rgba(255,255,255,0.7);'>This is an automated message. Please do not reply to this email.</p>
                            <p style='margin: 5px 0 0; font-size: 13px; color: rgba(255,255,255,0.7);'>© 2024 MassacreMC. All rights reserved.</p>
                        </div>
                    </div>
                </div>
            </div>
        </body>
    </html>";


    $plain_text = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $message_html));

    try {
        secure_log("Preparing to send appeal status notification email via Mailgun API");

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.mailgun.net/v3/{$domain}/messages");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERPWD, "api:{$api_key}");
        curl_setopt($ch, CURLOPT_POST, true);

        // Use certificate file for SSL verification
        $cert_file = __DIR__ . '/certs/massacremc.net.pem';
        if (file_exists($cert_file)) {
            secure_log("Using certificate file for appeal email: " . $cert_file);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            curl_setopt($ch, CURLOPT_CAINFO, $cert_file);
        } else {
            // Try alternative locations
            $alt_cert_files = [
                '/home/<USER>/website/certs/massacremc.net.pem',
                '/etc/ssl/certs/ca.pem',
                '/etc/ssl/certs/massacremc.net.pem'
            ];

            $cert_found = false;
            foreach ($alt_cert_files as $alt_file) {
                if (file_exists($alt_file)) {
                    secure_log("Using alternative certificate file for appeal email: " . $alt_file);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                    curl_setopt($ch, CURLOPT_CAINFO, $alt_file);
                    $cert_found = true;
                    break;
                }
            }

            if (!$cert_found) {
                secure_log("No certificate file found; relying on system CA bundle for appeal email");
                // Rely on system CA bundle; do not disable SSL verification
            }
        }

        $post_fields = [
            'from' => "MassacreMC Moderation Team <{$from_email}>",
            'to' => $email,
            'subject' => $subject,
            'html' => $message_html,
            'text' => $plain_text
        ];

        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);

        secure_log("Appeal email request details: from={$from_email}, to={$email}, subject={$subject}");

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        $curl_errno = curl_errno($ch);
        curl_close($ch);

        if ($curl_error) {
            secure_log("cURL error sending appeal email: {$curl_error} (Error code: {$curl_errno})");

            // Do not retry with SSL verification disabled
            if ($curl_errno == 60 || $curl_errno == 77) { // TLS certificate problem
                secure_log("TLS certificate error when sending appeal email; not retrying without verification");
            }
            return false;
        }

        if ($http_code >= 200 && $http_code < 300) {
            secure_log("Appeal status notification email sent successfully to {$email} for appeal #{$appeal_id}");
            secure_log("Mailgun API response: " . $response);
            return true;
        } else {
            secure_log("Failed to send appeal status notification email: HTTP Code {$http_code}, Response: {$response}");
            return false;
        }
    } catch (Exception $e) {
        secure_log("Exception sending appeal status notification email: " . $e->getMessage());
        return false;
    }
}

/**
 * Send email notification for report status change
 *
 * @param string $email Recipient email
 * @param string $name Recipient name
 * @param string $offender_name Reported player/faction name
 * @param string $status New report status
 * @param string $report_id Report ID
 * @param string $report_type Type of report (player or faction)
 * @return bool Whether email was sent successfully
 */
function send_report_notification_email($email, $name, $offender_name, $status, $report_id, $report_type = 'player') {
    secure_log("send_report_notification_email called with: email=$email, name=$name, offender=$offender_name, status=$status, report_id=$report_id");

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        secure_log("EMAIL ERROR: Invalid email format: " . $email);
        return false;
    }


    // Load environment configuration with support for special characters
    require_once __DIR__ . '/../../includes/config_parser.php';

    try {
        $env = load_env_config(['/etc/massacremc/config/admin.env']);
        secure_log("Loaded admin.env config file for email notification");
    } catch (Exception $e) {
        secure_log("EMAIL ERROR: Config file error - " . $e->getMessage());
        return false;
    }

    $api_key = $env['MAILGUN_API_KEY'] ?? '';
    $domain = $env['MAILGUN_DOMAIN'] ?? '';
    $from_email = $env['SENDER_EMAIL'] ?? '<EMAIL>';




    if (!$api_key || !$domain || !$from_email) {
        secure_log("Email notification failed: Missing Mailgun configuration");
        return false;
    }


    $now = new DateTime();
    $now->setTimezone(new DateTimeZone('America/New_York'));
    $timestamp = $now->format('F d, Y \a\t h:i A') . ' ET';

    $entity_type = ucfirst($report_type);
    $subject = "🛡️ MassacreMC {$entity_type} Report Status Update";


    $message_html = "
    <html>
        <head>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Report Status Update</title>
        </head>
        <body style='margin: 0; padding: 0; font-family: \"Segoe UI\", Arial, sans-serif; background-color: #121212; color: #e0e0e0;'>
            <div style='max-width: 600px; margin: 20px auto; background: #1e1e1e; border: 1px solid #333; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.3);'>
                <!-- Header with Logo -->
                <div style='background: linear-gradient(135deg, #7b1fa2, #4a148c); color: white; padding: 30px; text-align: center;'>
                    <img src='https://portal.massacremc.net/img/logo.png' alt='MassacreMC Logo' style='max-width: 150px; margin-bottom: 15px;'>
                    <h1 style='margin: 0; font-size: 28px; letter-spacing: 0.5px; text-transform: uppercase;'>{$entity_type} Report Status Update</h1>
                    <p style='margin: 10px 0 0; font-size: 16px; opacity: 0.9;'>Reference: #{$report_id}</p>
                </div>

                <div style='padding: 40px 30px; background-color: #1e1e1e; border-bottom: 1px solid #333;'>
                    <!-- Status Banner -->
                    <div style='background-color: rgba(" . ($status === 'Accepted' ? "40, 167, 69, 0.15" : "220, 53, 69, 0.15") . "); border-left: 5px solid " . ($status === 'Accepted' ? '#28a745' : '#dc3545') . "; border-radius: 8px; padding: 20px; margin-bottom: 30px;'>
                        <h2 style='margin: 0 0 10px; color: " . ($status === 'Accepted' ? '#2ecc71' : '#e74c3c') . "; font-size: 24px; display: flex; align-items: center;'>
                            <span style='margin-right: 10px; font-size: 28px;'>" . ($status === 'Accepted' ? '✅' : '❌') . "</span>
                            Report " . ($status === 'Accepted' ? 'Accepted' : 'Denied') . "
                        </h2>
                        <p style='margin: 0; color: #e0e0e0; font-size: 16px; line-height: 1.6;'>" .
                            ($status === 'Accepted' ?
                                'Our moderation team has reviewed your report and taken appropriate action.' :
                                'After review, our moderation team has determined that no action is required at this time.') .
                        "</p>
                    </div>

                    <!-- Report Details Section -->
                    <h2 style='color: #bb86fc; font-size: 22px; margin-top: 0; margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 10px;'>
                        <span style='margin-right: 8px;'>📋</span> Report Details
                    </h2>

                    <div style='background-color: #252525; border: 1px solid #333; border-radius: 8px; padding: 25px; margin-bottom: 30px; box-shadow: 0 2px 5px rgba(0,0,0,0.2); border-left: 4px solid #bb86fc;'>
                        <div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>
                            <div style='margin-bottom: 15px;'>
                                <div style='color: #bb86fc; font-size: 14px; margin-bottom: 5px; font-weight: 600;'>Report ID</div>
                                <div style='color: #e0e0e0; font-weight: 600; font-size: 16px; background: #333; padding: 8px 12px; border-radius: 4px;'>#{$report_id}</div>
                            </div>

                            <div style='margin-bottom: 15px;'>
                                <div style='color: #bb86fc; font-size: 14px; margin-bottom: 5px; font-weight: 600;'>Resolution Date</div>
                                <div style='color: #e0e0e0; font-weight: 600; font-size: 16px; background: #333; padding: 8px 12px; border-radius: 4px;'>{$timestamp}</div>
                            </div>
                        </div>

                        <div style='margin-bottom: 0;'>
                            <div style='color: #bb86fc; font-size: 14px; margin-bottom: 5px; font-weight: 600;'>Reported {$entity_type}</div>
                            <div style='color: #e0e0e0; font-weight: 600; font-size: 16px; background: #333; padding: 8px 12px; border-radius: 4px;'>{$offender_name}</div>
                        </div>
                    </div>";


    if ($status === 'Accepted') {
        $message_html .= "
                    <div style='margin-bottom: 30px;'>
                        <h3 style='color: #bb86fc; font-size: 20px; margin-top: 0; margin-bottom: 15px; border-bottom: 2px solid #333; padding-bottom: 10px;'>
                            <span style='margin-right: 8px;'>🔍</span> Our Decision
                        </h3>
                        <div style='background-color: rgba(40, 167, 69, 0.15); border-left: 3px solid #28a745; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);'>
                            <h4 style='margin-top: 0; margin-bottom: 10px; color: #2ecc71;'>Action has been taken</h4>
                            <p style='margin: 0; color: #e0e0e0; line-height: 1.6;'>Our moderation team has reviewed the evidence and taken appropriate measures.</p>
                        </div>

                        <p style='margin: 0 0 15px; color: #e0e0e0; line-height: 1.6; padding: 0 10px;'>Due to our privacy policy, we cannot share specific details about the actions taken against other players. However, please be assured that we have addressed the situation according to our server rules and guidelines.</p>

                        <p style='margin: 0; color: #e0e0e0; line-height: 1.6; padding: 0 10px;'>Thank you for your vigilance in helping us maintain a fair and enjoyable gaming environment for everyone. Your contribution to our community is greatly appreciated.</p>
                    </div>";
    } else {
        $message_html .= "
                    <div style='margin-bottom: 30px;'>
                        <h3 style='color: #bb86fc; font-size: 20px; margin-top: 0; margin-bottom: 15px; border-bottom: 2px solid #333; padding-bottom: 10px;'>
                            <span style='margin-right: 8px;'>🔍</span> Our Decision
                        </h3>
                        <div style='background-color: rgba(220, 53, 69, 0.15); border-left: 3px solid #dc3545; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);'>
                            <h4 style='margin-top: 0; margin-bottom: 10px; color: #e74c3c;'>No action required at this time</h4>
                            <p style='margin: 0; color: #e0e0e0; line-height: 1.6;'>Our moderation team has determined that the reported behavior does not violate our server rules in a way that requires administrative action.</p>
                        </div>

                        <p style='margin: 0 0 15px; color: #e0e0e0; line-height: 1.6; padding: 0 10px;'>This could be due to insufficient evidence, the situation being resolved naturally through gameplay, or the reported behavior falling within acceptable parameters of our community guidelines.</p>

                        <p style='margin: 0; color: #e0e0e0; line-height: 1.6; padding: 0 10px;'>We appreciate your vigilance in helping maintain our community standards. If you have additional evidence or if the situation persists, please feel free to submit a new report with more details through our <a href='https://portal.massacremc.net/report' style='color: #bb86fc; text-decoration: none; font-weight: 600;'>reporting system</a>.</p>
                    </div>";
    }


    $message_html .= "
                    <div style='margin-bottom: 30px;'>
                        <h3 style='color: #bb86fc; font-size: 20px; margin-top: 0; margin-bottom: 15px; border-bottom: 2px solid #333; padding-bottom: 10px;'>
                            <span style='margin-right: 8px;'>✨</span> What's Next
                        </h3>
                        <div style='background-color: #252525; border: 1px solid #333; border-radius: 8px; padding: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);'>
                            <ul style='margin: 0; padding: 0 0 0 20px; color: #e0e0e0;'>
                                <li style='margin-bottom: 10px;'>Continue to enjoy your gameplay experience on MassacreMC</li>
                                <li style='margin-bottom: 10px;'>Report any future rule violations you may encounter</li>
                                <li style='margin-bottom: 10px;'>Consider joining our <a href='https://discord.gg/mBjd5cXSxR' style='color: #bb86fc; text-decoration: none; font-weight: 600;'>Discord community</a> for server updates</li>
                                <li style='margin-bottom: 0;'>Review our <a href='https://portal.massacremc.net/rules' style='color: #bb86fc; text-decoration: none; font-weight: 600;'>server rules</a> to stay informed about our community guidelines</li>
                            </ul>
                        </div>
                    </div>";

    $message_html .= "
                </div>
                <div style='background: linear-gradient(135deg, #7b1fa2, #4a148c); padding: 40px; text-align: center; border-top: 3px solid #7b1fa2;'>
                    <h3 style='color: #ffffff; margin-top: 0; font-size: 24px; margin-bottom: 20px; font-weight: 600; letter-spacing: 0.5px;'>Connect With Us</h3>

                    <div style='margin: 30px 0; text-align: left; max-width: 400px; margin-left: auto; margin-right: auto;'>
                        <ul style='list-style-type: none; padding: 0; margin: 0;'>
                            <li style='margin-bottom: 12px; line-height: 1.6;'>
                                <span style='color: #ff9800; font-weight: 600; margin-right: 5px;'>Server IP:</span>
                                <span style='color: #999999; font-weight: 600; font-family: monospace;'>play.massacremc.net</span>
                            </li>
                            <li style='margin-bottom: 12px; line-height: 1.6;'>
                                <span style='color: #ff9800; font-weight: 600; margin-right: 5px;'>Discord:</span>
                                <a href='https://discord.gg/mBjd5cXSxR' style='color: #999999; font-weight: 600; text-decoration: none; font-family: monospace;'>discord.gg/mBjd5cXSxR</a>
                            </li>
                            <li style='margin-bottom: 12px; line-height: 1.6;'>
                                <span style='color: #ff9800; font-weight: 600; margin-right: 5px;'>Website:</span>
                                <a href='https://portal.massacremc.net' style='color: #999999; font-weight: 600; text-decoration: none; font-family: monospace;'>portal.massacremc.net</a>
                            </li>
                        </ul>
                    </div>

                    <div style='margin-top: 35px; padding-top: 25px; border-top: 1px solid rgba(255,255,255,0.2);'>
                        <p style='margin: 0 0 15px; color: #ffffff; font-size: 16px;'>Need assistance? Email us at <a href='mailto:<EMAIL>' style='color: #ffffff; text-decoration: none; font-weight: 700; border-bottom: 2px solid rgba(255,255,255,0.4); padding-bottom: 2px;'><EMAIL></a></p>

                        <div style='background-color: rgba(0,0,0,0.2); border-radius: 8px; padding: 15px; margin-top: 20px; display: inline-block;'>
                            <p style='margin: 0 0 5px; font-size: 13px; color: rgba(255,255,255,0.7);'>This is an automated message. Please do not reply to this email.</p>
                            <p style='margin: 5px 0 0; font-size: 13px; color: rgba(255,255,255,0.7);'>© 2024 MassacreMC. All rights reserved.</p>
                        </div>
                    </div>
                </div>
            </div>
        </body>
    </html>";

    try {
        secure_log("Preparing to send email via Mailgun API");

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.mailgun.net/v3/{$domain}/messages");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERPWD, "api:{$api_key}");
        curl_setopt($ch, CURLOPT_POST, true);

        // Use our certificate file for SSL verification
        $cert_file = __DIR__ . '/certs/massacremc.net.pem';
        if (file_exists($cert_file)) {
            secure_log("Using certificate file for report notification email: " . $cert_file);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            curl_setopt($ch, CURLOPT_CAINFO, $cert_file);
        } else {
            // Try alternative locations
            $alt_cert_files = [
                '/home/<USER>/website/certs/massacremc.net.pem',
                '/etc/ssl/certs/ca.pem',
                '/etc/ssl/certs/massacremc.net.pem'
            ];

            $cert_found = false;
            foreach ($alt_cert_files as $alt_file) {
                if (file_exists($alt_file)) {
                    secure_log("Using alternative certificate file for report notification email: " . $alt_file);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                    curl_setopt($ch, CURLOPT_CAINFO, $alt_file);
                    $cert_found = true;
                    break;
                }
            }

            if (!$cert_found) {
                secure_log("No certificate file found; relying on system CA bundle for report notification email");
                // Rely on system CA bundle; do not disable SSL verification
            }
        }

        $post_fields = [
            'from' => "MassacreMC Moderation Team <{$from_email}>",
            'to' => $email,
            'subject' => $subject,
            'html' => $message_html,
            'text' => strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $message_html))
        ];

        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);

        secure_log("Email request details: from={$from_email}, to={$email}, subject={$subject}");

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        $curl_errno = curl_errno($ch);
        curl_close($ch);

        if ($curl_error) {
            secure_log("cURL error when sending report notification email: " . $curl_error . " (Error code: " . $curl_errno . ")");

            // If it's an SSL certificate error, try again with SSL verification disabled
            if ($curl_errno == 60 || $curl_errno == 77) { // SSL certificate problem
                secure_log("Retrying report notification email with SSL verification disabled");

                $ch = curl_init("https://api.mailgun.net/v3/{$domain}/messages");
                curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
                curl_setopt($ch, CURLOPT_USERPWD, "api:{$api_key}");
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);

                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $curl_error = curl_error($ch);
                curl_close($ch);

                if ($curl_error) {
                    secure_log("Second attempt failed: " . $curl_error);
                    return false;
                }
            } else {
                return false;
            }
        }

        if ($http_code >= 200 && $http_code < 300) {
            secure_log("Report notification email sent successfully to {$email} for report #{$report_id}");
            secure_log("Mailgun API response: " . $response);
            return true;
        } else {
            secure_log("Failed to send report notification email: HTTP Code {$http_code}, Response: {$response}");
            return false;
        }
    } catch (Exception $e) {
        secure_log("Exception sending email notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Send staff application status update email
 */
function send_staff_application_status_email($application_id, $application_data, $status, $reviewer_notes = '') {
    try {
        secure_log("send_staff_application_status_email called with application_id: $application_id, status: $status");

        if (!file_exists('/etc/massacremc/config/api.env')) {
            secure_log("Email config file not found: /etc/massacremc/config/api.env");
            return false;
        }

        $env = parse_config_file_safe('/etc/massacremc/config/api.env');

        if (empty($env['MAILGUN_API_KEY']) || empty($env['MAILGUN_DOMAIN']) || empty($env['SENDER_EMAIL'])) {
            secure_log("Missing email configuration for staff application status update");
            return false;
        }

        $email = $application_data['discord_email'] ?? '';
        if (empty($email)) {
            secure_log("No email address provided for staff application status update");
            return false;
        }

        // Create email content based on status
        $discord_username = $application_data['discord_username'] ?? 'Unknown';
        $position = ucfirst($application_data['position'] ?? 'Unknown');
        $reviewed_date = date('F d, Y \a\t h:i A') . ' ET';

        $status_info = [
            'accepted' => [
                'emoji' => '🎉',
                'title' => 'Application Accepted',
                'color' => '#28a745',
                'bg_color' => '#d4edda',
                'border_color' => '#28a745',
                'message' => 'Congratulations! Your staff application has been accepted.',
                'next_steps' => [
                    'You will be contacted by a senior staff member within 24-48 hours',
                    'Please keep your Discord DMs open for further instructions',
                    'You may be invited to a brief interview or orientation session',
                    'Welcome to the MassacreMC staff team!'
                ]
            ],
            'rejected' => [
                'emoji' => '❌',
                'title' => 'Application Not Accepted',
                'color' => '#dc3545',
                'bg_color' => '#f8d7da',
                'border_color' => '#dc3545',
                'message' => 'Thank you for your interest in joining our staff team. Unfortunately, your application was not accepted at this time.',
                'next_steps' => [
                    'You may reapply after 30 days from your original submission',
                    'Consider gaining more experience in the community',
                    'Continue being active and helpful to other players',
                    'Feel free to ask current staff for advice on improving future applications'
                ]
            ],
            'under_review' => [
                'emoji' => '🔍',
                'title' => 'Application Under Review',
                'color' => '#ffc107',
                'bg_color' => '#fff3cd',
                'border_color' => '#ffc107',
                'message' => 'Your staff application is currently under review by our team.',
                'next_steps' => [
                    'We are carefully reviewing all aspects of your application',
                    'You may be contacted for additional information or an interview',
                    'Please continue being active in the community',
                    'We will notify you once a final decision is made'
                ]
            ]
        ];

        $info = $status_info[$status] ?? $status_info['under_review'];
        $subject = "{$info['emoji']} Staff Application Update - MassacreMC";

        $reviewer_notes_html = '';
        if (!empty($reviewer_notes)) {
            $reviewer_notes_html = "
            <div style='background-color: #f8f9fa; border-left: 4px solid #6c757d; padding: 15px; margin-bottom: 25px;'>
                <h3 style='color: #495057; margin: 0 0 10px 0; font-size: 18px;'>💬 Additional Notes</h3>
                <p style='color: #495057; margin: 0; line-height: 1.6;'>" . nl2br(htmlspecialchars($reviewer_notes)) . "</p>
            </div>";
        }

        $next_steps_html = '';
        foreach ($info['next_steps'] as $step) {
            $next_steps_html .= "<li style='margin-bottom: 8px;'>{$step}</li>";
        }

        $html_body = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8f9fa; padding: 20px;'>
            <div style='background-color: #ffffff; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>
                <div style='text-align: center; margin-bottom: 30px;'>
                    <h1 style='color: {$info['color']}; margin: 0; font-size: 28px;'>{$info['emoji']} {$info['title']}</h1>
                    <p style='color: #6c757d; margin: 10px 0 0 0; font-size: 16px;'>MassacreMC Staff Team</p>
                </div>

                <div style='background-color: {$info['bg_color']}; border-left: 4px solid {$info['border_color']}; padding: 20px; margin-bottom: 25px; border-radius: 5px;'>
                    <p style='color: #495057; margin: 0; font-size: 16px; line-height: 1.6;'>{$info['message']}</p>
                </div>

                <div style='background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 25px;'>
                    <h2 style='color: #495057; margin: 0 0 15px 0; font-size: 20px;'>Application Details</h2>
                    <table style='width: 100%; border-collapse: collapse;'>
                        <tr>
                            <td style='padding: 8px 0; color: #6c757d; font-weight: bold; width: 40%;'>Application ID:</td>
                            <td style='padding: 8px 0; color: #495057;'>{$application_id}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; color: #6c757d; font-weight: bold;'>Position Applied:</td>
                            <td style='padding: 8px 0; color: #495057;'>{$position}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; color: #6c757d; font-weight: bold;'>Discord Username:</td>
                            <td style='padding: 8px 0; color: #495057;'>{$discord_username}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; color: #6c757d; font-weight: bold;'>Status:</td>
                            <td style='padding: 8px 0; color: {$info['color']}; font-weight: bold;'>" . ucfirst(str_replace('_', ' ', $status)) . "</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; color: #6c757d; font-weight: bold;'>Reviewed:</td>
                            <td style='padding: 8px 0; color: #495057;'>{$reviewed_date}</td>
                        </tr>
                    </table>
                </div>

                {$reviewer_notes_html}

                <div style='background-color: #e3f2fd; border-left: 4px solid #2196f3; padding: 15px; margin-bottom: 25px;'>
                    <h3 style='color: #1976d2; margin: 0 0 10px 0; font-size: 18px;'>📋 Next Steps</h3>
                    <ul style='color: #495057; margin: 0; padding-left: 20px; line-height: 1.6;'>
                        {$next_steps_html}
                    </ul>
                </div>

                <div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;'>
                    <p style='color: #6c757d; margin: 0 0 15px 0; font-size: 14px;'>
                        Thank you for your interest in joining the MassacreMC staff team!
                    </p>
                    <div style='margin: 20px 0;'>
                        <a href='https://portal.massacremc.net/account/staff-applications'
                           style='background-color: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;'>
                            View Application Status
                        </a>
                    </div>
                    <p style='color: #6c757d; margin: 15px 0 0 0; font-size: 12px;'>
                        This is an automated message. Please do not reply to this email.
                    </p>
                </div>
            </div>
        </div>";

        // Send email using Mailgun
        $ch = curl_init("https://api.mailgun.net/v3/{$env['MAILGUN_DOMAIN']}/messages");
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, "api:{$env['MAILGUN_API_KEY']}");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Use same SSL handling as other functions

        $post_fields = [
            'from' => "MassacreMC Staff Team <{$env['SENDER_EMAIL']}>",
            'to' => $email,
            'subject' => $subject,
            'html' => $html_body,
            'text' => strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $html_body))
        ];

        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);

        secure_log("Staff application status email request details: from={$env['SENDER_EMAIL']}, to={$email}, subject={$subject}");

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error) {
            secure_log("cURL error when sending staff application status email: " . $curl_error);
            return false;
        }

        if ($http_code >= 200 && $http_code < 300) {
            secure_log("Staff application status email sent successfully to {$email} for application #{$application_id}");
            return true;
        } else {
            secure_log("Failed to send staff application status email: HTTP Code {$http_code}, Response: {$response}");
            return false;
        }
    } catch (Exception $e) {
        secure_log("Exception sending staff application status email: " . $e->getMessage());
        return false;
    }
}

/**
 * Get all appeals
 *
 * @return array Appeals
 */
function get_appeals() {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }


        $options = [
            'sort' => ['created_at' => -1],
            'limit' => 1000 // Limit to prevent memory issues
        ];

        $cursor = $db->appeals->find([], $options);

        $appeals = [];
        foreach ($cursor as $appeal) {

            $appeal_array = (array)$appeal;


            if (isset($appeal_array['_id'])) {
                $appeal_array['_id'] = (string)$appeal_array['_id'];


                if (!isset($appeal_array['id'])) {
                    $appeal_array['id'] = $appeal_array['_id'];
                }
            }


            if (isset($appeal_array['created_at']) && $appeal_array['created_at'] instanceof MongoDB\BSON\UTCDateTime) {
                $date = $appeal_array['created_at']->toDateTime();
                $date->setTimezone(new DateTimeZone('America/New_York'));
                $appeal_array['created_at'] = $date->format('Y-m-d H:i:s') . ' ET';
            }


            if (!isset($appeal_array['player_name'])) {
                $appeal_array['player_name'] = 'Unknown';
            }

            if (!isset($appeal_array['status'])) {
                $appeal_array['status'] = 'Pending';
            }

            if (!isset($appeal_array['punishment_id'])) {
                $appeal_array['punishment_id'] = 'Unknown';
            }


            $required_fields = ['id', 'player_name', 'status', 'created_at', 'punishment_id'];
            foreach ($required_fields as $field) {
                if (!isset($appeal_array[$field])) {
                    $appeal_array[$field] = 'Unknown';
                }
            }

            $appeals[] = $appeal_array;
        }

        return $appeals;
    } catch (Exception $e) {
        secure_log("Error getting appeals: " . $e->getMessage());
        return [];
    }
}

/**
 * Handle appeal action (approve/deny)
 *
 * @param string $appeal_id Appeal ID
 * @param string $action Action (approve/deny)
 * @param string $staff_id Staff ID
 * @param string $staff_name Staff name
 * @param string $staff_notes Optional notes from staff
 * @return array Result with success status and message
 */
function handle_appeal_action($appeal_id, $action, $staff_id, $staff_name, $staff_notes = '') {
    try {
        if (!in_array($action, ['approve', 'deny'])) {
            return ['success' => false, 'message' => 'Invalid action'];
        }

        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }


        $appeal = $db->appeals->findOne(['id' => $appeal_id]);


        if (!$appeal && preg_match('/^[a-f\d]{24}$/i', $appeal_id)) {
            $objId = new MongoDB\BSON\ObjectId($appeal_id);
            $appeal = $db->appeals->findOne(['_id' => $objId]);
        }

        if (!$appeal) {
            return ['success' => false, 'message' => 'Appeal not found'];
        }


        if ($appeal['status'] !== 'Pending') {
            return ['success' => false, 'message' => 'Appeal is already processed'];
        }


        $new_status = ($action === 'approve') ? 'Approved' : 'Denied';


        $processed_at = new MongoDB\BSON\UTCDateTime(time() * 1000);
        $processed_date = $processed_at->toDateTime();
        $processed_date->setTimezone(new DateTimeZone('America/New_York'));
        $processed_date_formatted = $processed_date->format('c'); // ISO 8601 format


        $et_formatted = $processed_date->format('F d, Y \a\t h:i A') . ' ET';


        $update_data = [
            'status' => $new_status,
            'processed_by' => $staff_id,
            'processed_by_name' => $staff_name,
            'processed_at' => $processed_at,
            'processed_at_formatted' => $processed_date_formatted,
            'processed_at_et' => $et_formatted,

            'resolved_by' => $staff_name,
            'resolved_at' => $processed_at,
            'resolved_at_formatted' => $processed_date_formatted,
            'resolved_at_et' => $et_formatted
        ];


        if (!empty($staff_notes)) {
            $update_data['staff_notes'] = $staff_notes;
        }


        $history_entry = [
            'action' => $action === 'approve' ? 'approved' : 'denied', // Ensure correct spelling
            'timestamp' => $processed_at,
            'staff_name' => $staff_name,
            'details' => 'Appeal ' . ($action === 'approve' ? 'approved' : 'denied') . ' by staff member'
        ];


        $db->appeals->updateOne(
            ['_id' => $appeal['_id']],
            [
                '$set' => $update_data,
                '$push' => [
                    'history' => $history_entry
                ]
            ]
        );


        if ($action === 'approve' && isset($appeal['punishment_id'])) {
            $punishment_id = $appeal['punishment_id'];

            try {

                $punishment = $db->punishments->findOne(['punishment_id' => $punishment_id]);


                if (!$punishment && isset($appeal['punishment_id'])) {
                    $punishment = $db->punishments->findOne(['id' => $appeal['punishment_id']]);
                }

                if ($punishment) {

                    $player_name = $punishment['player_name'] ?? 'Unknown';
                    $punishment_type = $punishment['punishment_type'] ?? 'unknown';


                    log_punishment_activity(
                        $staff_id,
                        $staff_name,
                        $player_name,
                        'remove',
                        $punishment_type,
                        'Appeal approved',
                        $punishment['duration'] ?? 'permanent'
                    );


                    $result = $db->punishments->deleteOne(['_id' => $punishment['_id']]);

                    if ($result->getDeletedCount() === 0) {
                        secure_log("Failed to delete punishment {$punishment_id} for appeal {$appeal_id}");


                        $result = $db->punishments->deleteOne(['punishment_id' => $punishment_id]);

                        if ($result->getDeletedCount() === 0) {
                            secure_log("Second attempt to delete punishment {$punishment_id} failed");
                        }
                    } else {

                        $db->appeals->updateOne(
                            ['_id' => $appeal['_id']],
                            [
                                '$push' => [
                                    'history' => [
                                        'action' => 'punishment_removed',
                                        'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
                                        'staff_name' => $staff_name,
                                        'details' => "Punishment {$punishment_id} was removed"
                                    ]
                                ]
                            ]
                        );
                    }
                } else {

                    $punishment_type = isset($appeal['punishment_type']) ? $appeal['punishment_type'] : 'ban';


                    $collection_name = '';
                    switch ($punishment_type) {
                        case 'ban':
                            $collection_name = 'bans';
                            break;
                        case 'mute':
                            $collection_name = 'mutes';
                            break;
                        case 'warning':
                            $collection_name = 'warnings';
                            break;
                        default:
                            secure_log("Unknown punishment type {$punishment_type} in appeal {$appeal_id}");
                            break;
                    }

                    if ($collection_name) {
                        try {

                            log_punishment_activity(
                                $staff_id,
                                $staff_name,
                                $appeal['player_name'] ?? 'Unknown',
                                'remove',
                                $punishment_type,
                                'Appeal approved',
                                'unknown'
                            );


                            $result = $db->$collection_name->deleteOne(['id' => $punishment_id]);

                            if ($result->getDeletedCount() === 0) {
                                secure_log("Failed to delete punishment from legacy collection {$collection_name} with id {$punishment_id}. Trying with punishment_id.");


                                $result = $db->$collection_name->deleteOne(['punishment_id' => $punishment_id]);

                                if ($result->getDeletedCount() === 0) {
                                    secure_log("Failed to delete punishment from legacy collection {$collection_name} with punishment_id {$punishment_id}.");
                                }
                            } else {

                                $db->appeals->updateOne(
                                    ['_id' => $appeal['_id']],
                                    [
                                        '$push' => [
                                            'history' => [
                                                'action' => 'punishment_removed',
                                                'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
                                                'staff_name' => $staff_name,
                                                'details' => "Punishment {$punishment_id} was removed from the legacy {$collection_name} collection"
                                            ]
                                        ]
                                    ]
                                );
                            }
                        } catch (Exception $e) {
                            secure_log("Error deleting punishment from legacy collection: " . $e->getMessage());
                        }
                    }
                }
            } catch (Exception $e) {
                secure_log("Error processing punishment deletion for appeal {$appeal_id}: " . $e->getMessage());

            }
        }


        $email_sent = false;
        if (isset($appeal['email']) && !empty($appeal['email'])) {
            $email = $appeal['email'];
            $player_name = $appeal['player_name'] ?? 'Player';
            $punishment_id = $appeal['punishment_id'] ?? '';

            $email_sent = send_appeal_status_notification_email(
                $email,
                $player_name,
                $new_status,
                $appeal_id,
                $punishment_id,
                $staff_notes
            );

            if ($email_sent) {
                secure_log("Appeal status notification email sent to {$email} for appeal {$appeal_id}");


                $email_history_entry = [
                    'action' => 'notification',
                    'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
                    'details' => "Email notification sent to player"
                ];

                $db->appeals->updateOne(
                    ['_id' => $appeal['_id']],
                    [
                        '$push' => [
                            'history' => $email_history_entry
                        ]
                    ]
                );
            } else {
                secure_log("Failed to send appeal status notification email to {$email} for appeal {$appeal_id}");


                $email_history_entry = [
                    'action' => 'notification_failed',
                    'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
                    'details' => "Failed to send email notification to player"
                ];

                $db->appeals->updateOne(
                    ['_id' => $appeal['_id']],
                    [
                        '$push' => [
                            'history' => $email_history_entry
                        ]
                    ]
                );
            }
        }


        $player_name = $appeal['player_name'] ?? 'Unknown';
        $log_action = $action === 'approve' ? 'accept' : 'deny';
        $log_reason = !empty($staff_notes) ? $staff_notes : ($action === 'approve' ? 'Appeal approved' : 'Appeal denied');


        $logged = log_appeal_activity($staff_id, $staff_name, $player_name, $log_action, $appeal_id, $log_reason);

        if (!$logged) {
            secure_log("Failed to log appeal activity for appeal {$appeal_id}");
        }


        $message = "Appeal has been " . ($action === 'approve' ? 'approved' : 'denied') . " successfully";


        if ($action === 'approve' && isset($appeal['punishment_id'])) {
            $message .= ". The punishment has been removed.";
        }


        if (isset($appeal['email']) && !empty($appeal['email'])) {
            if ($email_sent) {
                $message .= " Email notification sent successfully.";
            } else {
                $message .= " However, the email notification could not be sent.";
            }
        }

        return [
            'success' => true,
            'message' => $message,
            'notification_sent' => $email_sent
        ];
    } catch (Exception $e) {
        secure_log("Error processing appeal {$appeal_id}: " . $e->getMessage());
        return [
            'success' => false,
            'message' => "Failed to {$action} appeal: " . $e->getMessage()
        ];
    }
}

/**
 * Get predefined punishment types with their durations based on offense count
 *
 * @return array Punishment types with durations
 */
function get_predefined_punishments() {
    return [
        'hacking_cheating' => [
            'label' => 'Hacking/Cheating',
            'durations' => [
                1 => '10d',
                2 => 'permanent'
            ],
            'type' => 'ban'
        ],
        'kill_farming' => [
            'label' => 'Kill Farming',
            'durations' => [
                1 => '1d',
                2 => '1w',
                3 => '1mo',
                4 => 'permanent'
            ],
            'type' => 'ban'
        ],
        'punishment_evading' => [
            'label' => 'Punishment Evading',
            'durations' => [
                1 => 'permanent'
            ],
            'type' => 'ban'
        ],
        'inappropriate_language' => [
            'label' => 'Inappropriate Language',
            'durations' => [
                1 => '7h',
                2 => '14d',
                3 => '28d',
                4 => 'permanent'
            ],
            'type' => 'mute'
        ],
        'harassment' => [
            'label' => 'Harassment of Players',
            'durations' => [
                1 => '1d',
                2 => '7d',
                3 => '30d',
                4 => 'permanent'
            ],
            'type' => 'mute'
        ],
        'exploiting' => [
            'label' => 'Exploiting Game Mechanics',
            'durations' => [
                1 => '7d',
                2 => '14d',
                3 => '3mo',
            ],
            'type' => 'ban'
        ],
        'advertising' => [
            'label' => 'Advertising Other Servers',
            'durations' => [
                1 => '3d',
                2 => 'permanent'
            ],
            'type' => 'mute'
        ],
        'inappropriate_username' => [
            'label' => 'Inappropriate Username',
            'durations' => [
                1 => 'permanent'
            ],
            'type' => 'ban'
        ],
        'spamming' => [
            'label' => 'Spamming',
            'durations' => [
                1 => '1d',
                2 => 'permanent'
            ],
            'type' => 'mute'
        ],
        'impersonating_staff' => [
            'label' => 'Impersonating Staff',
            'durations' => [
                1 => '7d',
                2 => 'permanent'
            ],
            'type' => 'ban'
        ],
        'inappropriate_skin' => [
            'label' => 'Inappropriate Skin',
            'durations' => [
                1 => '1d',
                2 => 'permanent'
            ],
            'type' => 'ban'
        ],
        'hate_speech' => [
            'label' => 'Hate Speech',
            'durations' => [
                1 => '1d',
                2 => '30d',
                3 => 'permanent'
            ],
            'type' => 'mute'
        ]
    ];
}

/**
 * Count previous offenses of a specific type for a player
 *
 * @param MongoDB\Database $db Database instance
 * @param string $player Player name
 * @param string $offense_type Offense type key
 * @return int Number of previous offenses
 */
function count_previous_offenses($db, $player, $offense_type) {
    try {

        $cursor = $db->punishments->find([
            'player_name' => $player,
            'offense_type' => $offense_type
        ]);

        return iterator_count($cursor) + 1; // +1 for the current offense
    } catch (Exception $e) {
        secure_log("Error counting previous offenses: " . $e->getMessage());
        return 1; // Default to first offense if there's an error
    }
}

/**
 * Add a punishment to the unified punishments collection
 *
 * @param string $type Punishment type (warning, ban, mute)
 * @param string $player Player name
 * @param string $reason Reason for punishment
 * @param string $staff_id Staff ID
 * @param string $staff_name Staff name
 * @param string $duration Optional duration (e.g., "30d", "12h", "30m")
 * @param string $player_xuid Optional player XUID
 * @param string $offense_type Optional predefined offense type
 * @param string $evidence Optional evidence (e.g., screenshot URLs)
 * @param string $staff_notes Optional staff notes (not visible to player)
 * @return array Result with success status and message
 */
function add_punishment($type, $player, $reason, $staff_id, $staff_name, $duration = null, $player_xuid = '', $offense_type = '', $evidence = '', $staff_notes = '') {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('punishments', $collections)) {
            $db->createCollection('punishments');

            $db->punishments->createIndex(['player_name' => 1]);
            $db->punishments->createIndex(['punishment_type' => 1]);
            $db->punishments->createIndex(['active' => 1]);
            $db->punishments->createIndex(['issued_at' => -1]);
            $db->punishments->createIndex(['offense_type' => 1]);
        }


        $punishment_type = rtrim($type, 's');


        $offense_count = 1;
        $predefined_punishment = null;

        if (!empty($offense_type)) {

            $predefined_punishments = get_predefined_punishments();

            if (isset($predefined_punishments[$offense_type])) {
                $predefined_punishment = $predefined_punishments[$offense_type];
                $punishment_type = $predefined_punishment['type']; // Override with predefined type


                $offense_count = count_previous_offenses($db, $player, $offense_type);


                $max_offense = max(array_keys($predefined_punishment['durations']));
                $offense_level = min($offense_count, $max_offense);
                $duration = $predefined_punishment['durations'][$offense_level];
            }
        }


        $punishment_id = 'PUN-' . bin2hex(random_bytes(4));


        $issued_at = new MongoDB\BSON\UTCDateTime(time() * 1000);


        $expires_at = null;
        $duration_value = null;
        if ($duration && $duration !== 'permanent') {
            if (preg_match('/^(\d+)([dhm])$/', $duration, $matches)) {
                $value = (int)$matches[1];
                $unit = $matches[2];
                $duration_value = $duration; // Store the original duration string

                $seconds = 0;
                switch ($unit) {
                    case 'm': $seconds = $value * 60; break;
                    case 'h': $seconds = $value * 3600; break;
                    case 'd': $seconds = $value * 86400; break;
                }

                if ($seconds > 0) {
                    $expires_at = new MongoDB\BSON\UTCDateTime((time() + $seconds) * 1000);
                }
            }
        } else if ($duration === 'permanent') {
            $duration_value = 'permanent';
        }


        if (empty($evidence)) {

            if (isset($_POST['evidence'])) {
                $evidence = $_POST['evidence'];
            }

            else {
                $json_data = file_get_contents('php://input');
                if (!empty($json_data)) {
                    $data = json_decode($json_data, true);
                    if (isset($data['evidence'])) {
                        $evidence = $data['evidence'];
                    }
                }
            }
        }


        if (empty($staff_notes)) {

            if (isset($_POST['staff_notes'])) {
                $staff_notes = $_POST['staff_notes'];
            }

            else {
                $json_data = file_get_contents('php://input');
                if (!empty($json_data)) {
                    $data = json_decode($json_data, true);
                    if (isset($data['staff_notes'])) {
                        $staff_notes = $data['staff_notes'];
                    }

                    else if (isset($data['notes'])) {
                        $staff_notes = $data['notes'];
                    }
                }
            }
        }


        $punishment = [
            'punishment_id' => $punishment_id,
            'punishment_type' => $punishment_type,
            'player_name' => $player,
            'player_xuid' => $player_xuid,
            'reason' => $reason,
            'staff_id' => $staff_id,
            'staff_name' => $staff_name,
            'active' => true,
            'issued_at' => $issued_at,
            'expires_at' => $expires_at,
            'duration' => $duration_value ?? 'permanent',
            'removed_at' => null,
            'removed_by' => null,
            'removed_by_name' => null,
            'removed_reason' => null,
            'offense_type' => $offense_type,
            'offense_count' => $offense_count,
            'evidence' => $evidence,
            'staff_notes' => $staff_notes,
            'public' => true, // Always make punishments public
        ];


        $result = $db->punishments->insertOne($punishment);


        $activity = [
            'user_id' => $staff_id,
            'username' => $staff_name,
            'action' => "Added " . $punishment_type . " for $player",
            'type' => 'punishment',
            'target' => $player,
            'details' => "Type: " . ucfirst($punishment_type) .
                         "\nReason: $reason" .
                         ($offense_type ? "\nOffense: " . ($predefined_punishment ? $predefined_punishment['label'] : $offense_type) : "") .
                         ($offense_count > 1 ? "\nOffense Count: " . $offense_count : "") .
                         ($duration ? "\nDuration: $duration" : "\nDuration: Permanent"),
            'timestamp' => $issued_at
        ];


        if (!in_array('staff_activity_log', $collections)) {
            $db->createCollection('staff_activity_log');
            $db->staff_activity_log->createIndex(['timestamp' => -1]);
        }

        $db->staff_activity_log->insertOne($activity);

        return [
            'success' => true,
            'message' => ucfirst($punishment_type) . " added successfully" .
                        ($offense_count > 1 ? " (Offense #" . $offense_count . ")" : ""),
            'punishment_id' => $punishment_id
        ];
    } catch (Exception $e) {
        secure_log("Error adding punishment: " . $e->getMessage());
        return [
            'success' => false,
            'message' => "Failed to add " . rtrim($type, 's') . ": " . $e->getMessage()
        ];
    }
}

/**
 * Check if a punishment has naturally expired based on its expiration date
 * This specifically checks for time-based expiration, not manual removal
 *
 * @param array $punishment Punishment document
 * @return bool Whether the punishment has naturally expired due to time
 */
function has_punishment_expired($punishment) {

    if (!isset($punishment['expires_at']) || $punishment['expires_at'] === null) {
        return false;
    }


    $now = time();


    $expires_at = null;
    if ($punishment['expires_at'] instanceof MongoDB\BSON\UTCDateTime) {
        $expires_at = $punishment['expires_at']->toDateTime()->getTimestamp();
    } elseif (is_string($punishment['expires_at'])) {
        $expires_at = strtotime($punishment['expires_at']);
    }


    if ($expires_at === null || $expires_at === false) {
        return false;
    }


    return $now > $expires_at;
}

/**
 * Check if a punishment is inactive (either expired or manually removed)
 *
 * @param array $punishment Punishment document
 * @return bool Whether the punishment is inactive
 */
function is_punishment_inactive($punishment) {

    if (isset($punishment['removed_at']) && $punishment['removed_at'] !== null) {
        return true;
    }


    return has_punishment_expired($punishment);
}

/**
 * Remove a punishment from the unified punishments collection
 *
 * This function marks the punishment as inactive in the database
 * and logs the removal action in the audit logs.
 *
 * @param string $punishment_id Punishment ID
 * @param string $staff_id Staff ID
 * @param string $staff_name Staff name
 * @param string $reason Optional reason for removal
 * @return array Result with success status and message
 */
function remove_punishment($punishment_id, $staff_id, $staff_name, $reason = 'Manually removed by staff') {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }


        $punishment = $db->punishments->findOne(['punishment_id' => $punishment_id]);


        if (!$punishment) {
            $punishment = $db->punishments->findOne(['id' => $punishment_id]);
        }

        if (!$punishment) {
            throw new Exception("Punishment not found");
        }


        $player_name = $punishment['player_name'] ?? 'Unknown Player';
        $punishment_type = $punishment['punishment_type'] ?? 'unknown';
        $punishment_reason = $punishment['reason'] ?? 'No reason provided';
        $punishment_issued_at = $punishment['issued_at'] ?? null;
        $punishment_expires_at = $punishment['expires_at'] ?? null;
        $punishment_staff_name = $punishment['staff_name'] ?? 'Unknown Staff';


        $removed_at = new MongoDB\BSON\UTCDateTime(time() * 1000);


        $punishment_details = "Punishment ID: $punishment_id\n" .
                             "Type: " . ucfirst($punishment_type) . "\n" .
                             "Player: $player_name\n" .
                             "Reason: $punishment_reason\n" .
                             "Issued by: $punishment_staff_name\n" .
                             "Removal reason: $reason";

        if ($punishment_issued_at) {
            $issued_date = $punishment_issued_at instanceof MongoDB\BSON\UTCDateTime ?
                          date('Y-m-d H:i:s', $punishment_issued_at->toDateTime()->getTimestamp()) :
                          (is_string($punishment_issued_at) ? $punishment_issued_at : 'Unknown');
            $punishment_details .= "\nIssued at: $issued_date";
        }

        if ($punishment_expires_at) {
            $expires_date = $punishment_expires_at instanceof MongoDB\BSON\UTCDateTime ?
                           date('Y-m-d H:i:s', $punishment_expires_at->toDateTime()->getTimestamp()) :
                           (is_string($punishment_expires_at) ? $punishment_expires_at : 'Never');
            $punishment_details .= "\nExpires at: $expires_date";
        }


        $result = $db->punishments->deleteOne(['_id' => $punishment['_id']]);

        if ($result->getDeletedCount() === 0) {
            throw new Exception("Failed to delete punishment");
        }


        $activity = [
            'user_id' => $staff_id,
            'username' => $staff_name,
            'action' => "Removed " . $punishment_type . " for $player_name",
            'type' => 'punishment_removal',
            'target' => $player_name,
            'details' => $punishment_details,
            'timestamp' => $removed_at,
            'punishment_id' => $punishment_id,
            'punishment_type' => $punishment_type,
            'removed_reason' => $reason
        ];


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('staff_activity_log', $collections)) {
            $db->createCollection('staff_activity_log');
            $db->staff_activity_log->createIndex(['timestamp' => -1]);
        }

        $db->staff_activity_log->insertOne($activity);


        if (!in_array('punishment_audit_log', $collections)) {
            $db->createCollection('punishment_audit_log');
            $db->punishment_audit_log->createIndex(['timestamp' => -1]);
            $db->punishment_audit_log->createIndex(['punishment_id' => 1]);
            $db->punishment_audit_log->createIndex(['player_name' => 1]);
        }


        $audit_entry = [
            'action' => 'removed',
            'punishment_id' => $punishment_id,
            'punishment_type' => $punishment_type,
            'player_name' => $player_name,
            'removed_by' => $staff_id,
            'removed_by_name' => $staff_name,
            'removed_reason' => $reason,
            'timestamp' => $removed_at,
            'punishment_snapshot' => $punishment // Store the complete punishment document
        ];

        $db->punishment_audit_log->insertOne($audit_entry);

        return [
            'success' => true,
            'message' => ucfirst($punishment_type) . " removed successfully"
        ];
    } catch (Exception $e) {
        secure_log("Error removing punishment: " . $e->getMessage());
        return [
            'success' => false,
            'message' => "Failed to remove punishment: " . $e->getMessage()
        ];
    }
}

/**
 * Get linked Discord account for a player
 *
 * @param string $username Player username
 * @return array|null Discord account info or null if not found
 */
function get_player_discord_account($username) {
    try {
        // Use the main website's DatabaseAccess class for consistency
        require_once __DIR__ . '/../../includes/db_access.php';
        $dbAccess = new DatabaseAccess();

        if (!$dbAccess || !$dbAccess->db) {
            throw new Exception("Database connection failed");
        }

        // Search for linked account by Xbox username
        $linkedAccount = $dbAccess->db->linked_accounts->findOne([
            'platform' => 'xbox',
            'username' => new MongoDB\BSON\Regex('^' . preg_quote($username) . '$', 'i'),
            'status' => 'verified'
        ]);

        if (!$linkedAccount) {
            return null;
        }

        // Get Discord user data from player_accounts
        $discordId = $linkedAccount['discord_id'];
        $playerData = $dbAccess->db->player_accounts->findOne([
            'discord_id' => $discordId
        ]);

        if ($playerData) {
            return [
                'discord_id' => $discordId,
                'discord_username' => $playerData['discord_username'] ?? 'Unknown',
                'discord_email' => $playerData['discord_email'] ?? null,
                'xbox_username' => $linkedAccount['username'],
                'linked_at' => $linkedAccount['verified_at'] ?? null
            ];
        }

        return null;
    } catch (Exception $e) {
        secure_log("Error fetching player Discord account: " . $e->getMessage());
        return null;
    }
}

/**
 * Unlink Discord account for a player (admin function)
 *
 * @param string $username Player username
 * @param string $staff_id Staff member performing the action
 * @return bool Success status
 */
function unlink_player_discord_account($username, $staff_id) {
    try {
        // Use the main website's DatabaseAccess class for consistency
        require_once __DIR__ . '/../../includes/db_access.php';
        $dbAccess = new DatabaseAccess();

        if (!$dbAccess || !$dbAccess->db) {
            throw new Exception("Database connection failed");
        }

        // Get the linked account info before deleting for logging
        $linkedAccount = $dbAccess->db->linked_accounts->findOne([
            'platform' => 'xbox',
            'username' => new MongoDB\BSON\Regex('^' . preg_quote($username) . '$', 'i'),
            'status' => 'verified'
        ]);

        if (!$linkedAccount) {
            return false;
        }

        // Remove the linked account
        $result = $dbAccess->db->linked_accounts->deleteOne([
            '_id' => $linkedAccount['_id']
        ]);

        if ($result->getDeletedCount() > 0) {
            // Log the action in staff activity
            update_staff_activity($staff_id, '', '', '', 'unlink_discord_account',
                "Unlinked Discord account for player: {$username} (Discord ID: {$linkedAccount['discord_id']})");

            return true;
        }

        return false;
    } catch (Exception $e) {
        secure_log("Error unlinking player Discord account: " . $e->getMessage());
        return false;
    }
}

/**
 * Get player punishments from the unified punishments collection
 *
 * @param string $username Player username
 * @return array Punishments
 */
function get_player_punishments($username) {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        if (!in_array('punishments', $collections)) {
            return []; // Return empty array if collection doesn't exist
        }


        $cursor = $db->punishments->find(['player_name' => $username], [
            'sort' => ['issued_at' => -1], // Sort by issued date, newest first
            'limit' => 100 // Limit to prevent memory issues
        ]);

        $all_punishments = [];
        foreach ($cursor as $doc) {
            $punishment = (array)$doc;


            if (isset($punishment['_id'])) {
                unset($punishment['_id']);
            }


            if (isset($punishment['active']) && $punishment['active'] === true && has_punishment_expired($punishment)) {

                try {
                    $db->punishments->updateOne(
                        ['punishment_id' => $punishment['punishment_id']],
                        [
                            '$set' => [
                                'active' => false,
                                'expired' => true,
                                'expired_at' => new MongoDB\BSON\UTCDateTime(time() * 1000)
                            ],
                            '$push' => [
                                'history' => [
                                    'action' => 'expired',
                                    'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
                                    'reason' => 'Punishment duration has elapsed'
                                ]
                            ]
                        ]
                    );


                    $punishment['active'] = false;
                    $punishment['expired'] = true;
                    $punishment['expired_at'] = new MongoDB\BSON\UTCDateTime(time() * 1000);


                    if (!isset($punishment['history'])) {
                        $punishment['history'] = [];
                    }

                    $punishment['history'][] = [
                        'action' => 'expired',
                        'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
                        'reason' => 'Punishment duration has elapsed'
                    ];

                    secure_log("Marked punishment " . $punishment['punishment_id'] . " as expired");
                } catch (Exception $e) {
                    secure_log("Error updating expired punishment status: " . $e->getMessage());

                }
            }

            $all_punishments[] = $punishment;
        }


        usort($all_punishments, function($a, $b) {
            return $b['issued_at'] <=> $a['issued_at'];
        });

        return $all_punishments;
    } catch (Exception $e) {
        secure_log("Error getting player punishments: " . $e->getMessage());
        return [];
    }
}

/**
 * Get dashboard stats
 *
 * @return array Dashboard stats
 */
function get_dashboard_stats() {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            secure_log("Dashboard stats: No database connection");
            return [
                'pendingReports' => 0,
                'pendingAppeals' => 0,
                'activeBans' => 0,
                'activeMutes' => 0,
                'onlineStaff' => []
            ];
        }


        try {
            $pending_reports = $db->reports->countDocuments(['status' => 'Pending']);


            $high_priority_reports = $db->reports->countDocuments([
                'status' => 'Pending',
                'priority' => ['$in' => [1, 2]] // High and Urgent priority
            ]);
        } catch (Exception $e) {
            secure_log("Error counting reports: " . $e->getMessage());
            $pending_reports = 0;
            $high_priority_reports = 0;
        }

        try {
            $pending_appeals = $db->appeals->countDocuments(['status' => 'Pending']);
        } catch (Exception $e) {
            secure_log("Error counting appeals: " . $e->getMessage());
            $pending_appeals = 0;
        }

        try {

            $collections = [];
            foreach ($db->listCollections() as $collectionInfo) {
                $collections[] = $collectionInfo->getName();
            }

            if (in_array('punishments', $collections)) {

                $active_bans = $db->punishments->countDocuments([
                    'punishment_type' => 'ban',
                    'active' => true
                ]);


                $active_mutes = $db->punishments->countDocuments([
                    'punishment_type' => 'mute',
                    'active' => true
                ]);

                secure_log("Counted active punishments: {$active_bans} bans, {$active_mutes} mutes");
            } else {

                try {
                    $active_bans = $db->bans->countDocuments(['active' => true]);
                } catch (Exception $e) {
                    secure_log("Error counting bans from bans collection: " . $e->getMessage());
                    $active_bans = 0;
                }

                try {
                    $active_mutes = $db->mutes->countDocuments(['active' => true]);
                } catch (Exception $e) {
                    secure_log("Error counting mutes from mutes collection: " . $e->getMessage());
                    $active_mutes = 0;
                }
            }
        } catch (Exception $e) {
            secure_log("Error counting active punishments: " . $e->getMessage());
            $active_bans = 0;
            $active_mutes = 0;
        }


        $online_staff = [];
        try {

            $collections = [];
            foreach ($db->listCollections() as $collectionInfo) {
                $collections[] = $collectionInfo->getName();
            }


            $cutoff_time = new MongoDB\BSON\UTCDateTime((time() - 900) * 1000); // 15 minutes ago


            $staff_cursor = $db->staff_activity->find(
                [
                    'last_active' => ['$gt' => $cutoff_time],
                    'role' => ['$ne' => 'UNAUTHORIZED'] // Exclude unauthorized users
                ],
                [
                    'projection' => ['_id' => 0],
                    'sort' => ['last_active' => -1],
                    'limit' => 100 // Limit online staff to prevent memory issues
                ]
            );

            if (!in_array('staff_activity_log', $collections)) {
                secure_log("staff_activity_log collection does not exist. Creating it now.");
                $db->createCollection('staff_activity_log');
                $db->staff_activity_log->createIndex(['timestamp' => -1]);
                $db->staff_activity_log->createIndex(['user_id' => 1]);


                $db->staff_activity_log->insertOne([
                    'user_id' => 'SYSTEM',
                    'username' => 'System',
                    'action' => 'Created',
                    'type' => 'system',
                    'target' => 'staff_activity_log',
                    'details' => 'Created the staff activity log collection',
                    'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000),
                    'staff' => 'System'
                ]);
            }


            $activity_cursor = $db->staff_activity_log->find(
                [], // query
                [
                    'projection' => ['_id' => 0],
                    'sort' => ['timestamp' => -1],
                    'limit' => 20 // Get more than we need to filter for unique activities
                ]
            );


            $all_activities = [];

            foreach ($activity_cursor as $activity) {
                $activity_array = (array)$activity;


                if (isset($activity_array['timestamp']) && $activity_array['timestamp'] instanceof MongoDB\BSON\UTCDateTime) {
                    $date = $activity_array['timestamp']->toDateTime();


                    $date->setTimezone(new DateTimeZone('America/New_York'));


                    $activity_array['formatted_date'] = $date->format('M j, Y g:i A') . ' ET';



                    $activity_array['timestamp'] = $date->format('c'); // ISO 8601 format
                }

                $all_activities[] = $activity_array;
            }


            $seen_activities = [];
            $unique_count = 0;

            foreach ($all_activities as $activity) {

                $action = $activity['action'] ?? '';
                $type = $activity['type'] ?? '';
                $target = $activity['target'] ?? '';

                $activity_key = "{$action}_{$type}_{$target}";


                if (!isset($seen_activities[$activity_key])) {
                    $seen_activities[$activity_key] = true;
                    $recent_activity[] = $activity;
                    $unique_count++;


                    if ($unique_count >= 5) {
                        break;
                    }
                }
            }


            if (empty($recent_activity)) {
                secure_log("No activities found in staff_activity_log. Checking for data to migrate.");


                if (in_array('reports', $collections)) {
                    $reports_cursor = $db->reports->find(
                        ['status' => ['$in' => ['Accepted', 'Denied']]],
                        [
                            'sort' => ['processed_at' => -1],
                            'limit' => 5
                        ]
                    );


                    $migrated_activities = [];

                    foreach ($reports_cursor as $report) {
                        if (isset($report->processed_by) && isset($report->processed_by_name)) {
                            $activity = [
                                'user_id' => $report->processed_by,
                                'username' => $report->processed_by_name,
                                'action' => $report->status == 'Accepted' ? 'Accepted' : 'Denied',
                                'type' => 'report',
                                'target' => $report->offender_name ?? 'Unknown Player',
                                'details' => "Report ID: " . ($report->report_id ?? 'Unknown'),
                                'timestamp' => $report->processed_at ?? new MongoDB\BSON\UTCDateTime(time() * 1000),
                                'staff' => $report->processed_by_name
                            ];


                            $db->staff_activity_log->insertOne($activity);


                            if (isset($activity['timestamp']) && $activity['timestamp'] instanceof MongoDB\BSON\UTCDateTime) {
                                $date = $activity['timestamp']->toDateTime();


                                $date->setTimezone(new DateTimeZone('America/New_York'));


                                $activity['formatted_date'] = $date->format('M j, Y g:i A') . ' ET';
                                $activity['timestamp'] = $date->format('c'); // ISO 8601 format
                            }

                            $migrated_activities[] = $activity;
                        }
                    }


                    $seen_activities = [];
                    $unique_count = 0;

                    foreach ($migrated_activities as $activity) {

                        $action = $activity['action'] ?? '';
                        $type = $activity['type'] ?? '';
                        $target = $activity['target'] ?? '';

                        $activity_key = "{$action}_{$type}_{$target}";


                        if (!isset($seen_activities[$activity_key])) {
                            $seen_activities[$activity_key] = true;
                            $recent_activity[] = $activity;
                            $unique_count++;


                            if ($unique_count >= 5) {
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception $e) {
            secure_log("Error getting activity log: " . $e->getMessage());
        }


        $online_staff = [];
        try {

            $cutoff_time = new MongoDB\BSON\UTCDateTime((time() - 900) * 1000); // 15 minutes ago


            $staff_cursor = $db->staff_activity->find(
                [
                    'last_active' => ['$gt' => $cutoff_time],
                    'role' => ['$ne' => 'UNAUTHORIZED'] // Exclude unauthorized users
                ],
                [
                    'projection' => ['_id' => 0],
                    'sort' => ['last_active' => -1]
                ]
            );

            foreach ($staff_cursor as $staff) {
                $staff_array = (array)$staff;


                if (!empty($staff_array['avatar_hash'])) {
                    $user_id = $staff_array['user_id'];
                    $avatar_hash = $staff_array['avatar_hash'];
                    $is_animated = strpos($avatar_hash, 'a_') === 0;
                    $extension = $is_animated ? 'gif' : 'png';
                    $staff_array['avatar_url'] = "https://cdn.discordapp.com/avatars/{$user_id}/{$avatar_hash}.{$extension}?size=128";
                } else if (!empty($staff_array['user_id'])) {

                    $default_avatar_id = (int)$staff_array['user_id'] % 6;
                    $staff_array['avatar_url'] = "https://cdn.discordapp.com/embed/avatars/{$default_avatar_id}.png";
                }

                $online_staff[] = $staff_array;
            }
        } catch (Exception $e) {
            secure_log("Error getting online staff: " . $e->getMessage());
        }

        return [
            'pendingReports' => $pending_reports,
            'highPriorityReports' => $high_priority_reports ?? 0,
            'pendingAppeals' => $pending_appeals,
            'activeBans' => $active_bans,
            'activeMutes' => $active_mutes,
            'onlineStaff' => $online_staff
        ];
    } catch (Exception $e) {
        secure_log("Error in get_dashboard_stats: " . $e->getMessage());
        return [
            'pendingReports' => 0,
            'highPriorityReports' => 0,
            'pendingAppeals' => 0,
            'activeBans' => 0,
            'activeMutes' => 0,
            'onlineStaff' => []
        ];
    }
}

/**
 * Sanitize MongoDB query to prevent NoSQL injection
 *
 * @param mixed $value Value to sanitize
 * @return mixed Sanitized value
 */
if (!function_exists('sanitize_mongo_query')) {
    function sanitize_mongo_query($value) {
        if (is_array($value)) {

            $sanitized = [];
            foreach ($value as $k => $v) {

                if (is_string($k) && strpos($k, '$') === 0) {
                    continue;
                }
                $sanitized[sanitize_mongo_query($k)] = sanitize_mongo_query($v);
            }
            return $sanitized;
        } elseif (is_string($value)) {

            if (strpos($value, '$') === 0) {
                return '';
            }
            return $value;
        } else {

            return $value;
        }
    }
}

/**
 * Add a note to a player
 *
 * @param string $xuid Player XUID
 * @param string $username Player username
 * @param string $content Note content
 * @param string $type Note type (behavior, support, payment, bug, other)
 * @param bool $important Whether the note is important
 * @param string $staff_id Staff ID
 * @param string $staff_name Staff name
 * @return array Result with success status and message
 */
function add_player_note($xuid, $username, $content, $type, $important, $staff_id, $staff_name) {
    secure_log("add_player_note called with: xuid=$xuid, username=$username, type=$type, important=" . ($important ? 'true' : 'false'));

    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            secure_log("Database connection failed in add_player_note");
            throw new Exception("Database connection failed");
        }


        $dbName = $db->getDatabaseName();
        secure_log("Using database: $dbName");

        if ($dbName !== 'mmc') {
            secure_log("WARNING: Not using the 'mmc' database. Current database: $dbName");

            try {
                $client = $connection['client'];
                $db = $client->selectDatabase('mmc');
                secure_log("Switched to 'mmc' database");
            } catch (Exception $e) {
                secure_log("Failed to switch to 'mmc' database: " . $e->getMessage());
                throw new Exception("Failed to connect to the required database");
            }
        }


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        secure_log("Available collections in database: " . implode(', ', $collections));

        if (!in_array('player_notes', $collections)) {
            secure_log("player_notes collection does not exist. Creating it now.");
            $db->createCollection('player_notes');
            $db->player_notes->createIndex(['xuid' => 1]);
            $db->player_notes->createIndex(['username' => 1]);
            $db->player_notes->createIndex(['created_at' => -1]);
            $db->player_notes->createIndex(['important' => 1]);
            secure_log("player_notes collection created successfully with indexes");
        } else {
            secure_log("player_notes collection already exists");
        }


        $valid_types = ['behavior', 'support', 'payment', 'bug', 'other'];
        if (!in_array($type, $valid_types)) {
            $type = 'other';
        }


        $note = [
            'xuid' => $xuid,
            'username' => $username,
            'content' => $content,
            'type' => $type,
            'important' => (bool)$important,
            'staff_id' => $staff_id,
            'staff_member' => $staff_name,
            'created_at' => new MongoDB\BSON\UTCDateTime(time() * 1000),
            'note_id' => uniqid('note_')
        ];


        secure_log("Inserting note into player_notes collection: " . json_encode($note));

        try {

            secure_log("MongoDB connection details: " . json_encode([
                'database' => $db->getDatabaseName(),
                'collections' => array_map(function($collectionInfo) {
                    return $collectionInfo->getName();
                }, iterator_to_array($db->listCollections()))
            ]));


            try {
                $testResult = $db->test_collection->insertOne([
                    'test' => true,
                    'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000)
                ]);
                secure_log("Test insert result: " . json_encode([
                    'insertedCount' => $testResult->getInsertedCount(),
                    'insertedId' => (string)$testResult->getInsertedId(),
                    'acknowledged' => $testResult->isAcknowledged()
                ]));
            } catch (Exception $e) {
                secure_log("Test insert failed: " . $e->getMessage());
            }


            $result = $db->player_notes->insertOne($note);

            secure_log("Note insert result: " . json_encode([
                'insertedCount' => $result->getInsertedCount(),
                'insertedId' => (string)$result->getInsertedId(),
                'acknowledged' => $result->isAcknowledged()
            ]));

            if ($result->getInsertedCount() === 0) {
                secure_log("Failed to add note - insertedCount is 0");
                throw new Exception("Failed to add note - no documents inserted");
            }


            $insertedNote = $db->player_notes->findOne(['note_id' => $note['note_id']]);
            if (!$insertedNote) {
                secure_log("Failed to verify note insertion - could not find note with ID: " . $note['note_id']);


                $anyNotes = $db->player_notes->find([], ['limit' => 5])->toArray();
                secure_log("Sample notes in collection: " . json_encode($anyNotes));

                throw new Exception("Failed to verify note insertion");
            } else {
                secure_log("Successfully verified note insertion with ID: " . $note['note_id']);
                secure_log("Inserted note data: " . json_encode($insertedNote));
            }
        } catch (Exception $e) {
            secure_log("Exception during note insertion: " . $e->getMessage());
            secure_log("Stack trace: " . $e->getTraceAsString());
            throw $e;
        }


        $activity = [
            'user_id' => $staff_id,
            'username' => $staff_name,
            'action' => "Added note to $username",
            'type' => 'note',
            'target' => $username,
            'details' => "Note type: $type" . ($important ? ' (Important)' : ''),
            'timestamp' => new MongoDB\BSON\UTCDateTime(time() * 1000)
        ];

        $db->staff_activity_log->insertOne($activity);

        return [
            'success' => true,
            'message' => "Note added successfully"
        ];
    } catch (Exception $e) {
        secure_log("Error adding player note: " . $e->getMessage());
        return [
            'success' => false,
            'message' => "Failed to add note: " . $e->getMessage()
        ];
    }
}

/**
 * Get notes for a player
 *
 * @param string $username Player username (optional if xuid is provided)
 * @param string $xuid Player XUID (optional if username is provided)
 * @return array Player notes
 */
function get_player_notes($username = null, $xuid = null) {
    secure_log("get_player_notes called with: username=" . ($username ?? 'null') . ", xuid=" . ($xuid ?? 'null'));

    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            secure_log("Database connection failed in get_player_notes");
            throw new Exception("Database connection failed");
        }


        $dbName = $db->getDatabaseName();
        secure_log("Using database: $dbName");

        if ($dbName !== 'mmc') {
            secure_log("WARNING: Not using the 'mmc' database. Current database: $dbName");

            try {
                $client = $connection['client'];
                $db = $client->selectDatabase('mmc');
                secure_log("Switched to 'mmc' database");
            } catch (Exception $e) {
                secure_log("Failed to switch to 'mmc' database: " . $e->getMessage());
                throw new Exception("Failed to connect to the required database");
            }
        }


        $collections = [];
        foreach ($db->listCollections() as $collectionInfo) {
            $collections[] = $collectionInfo->getName();
        }

        secure_log("Available collections in database: " . implode(', ', $collections));

        if (!in_array('player_notes', $collections)) {
            secure_log("player_notes collection does not exist. Returning empty array.");

            return [];
        } else {
            secure_log("player_notes collection exists");
        }


        $query = [];
        if ($username) {
            $query['username'] = $username;
        } elseif ($xuid) {
            $query['xuid'] = $xuid;
        } else {
            throw new Exception("Either username or xuid must be provided");
        }


        secure_log("Querying player_notes with: " . json_encode($query));


        secure_log("MongoDB connection details for get_player_notes: " . json_encode([
            'database' => $db->getDatabaseName(),
            'collections' => array_map(function($collectionInfo) {
                return $collectionInfo->getName();
            }, iterator_to_array($db->listCollections()))
        ]));

        try {

            $collectionExists = false;
            foreach ($db->listCollections() as $collectionInfo) {
                if ($collectionInfo->getName() === 'player_notes') {
                    $collectionExists = true;
                    break;
                }
            }

            if (!$collectionExists) {
                secure_log("player_notes collection does not exist in the database");

                $db->createCollection('player_notes');
                secure_log("Created player_notes collection");


                $db->player_notes->createIndex(['xuid' => 1]);
                $db->player_notes->createIndex(['username' => 1]);
                $db->player_notes->createIndex(['created_at' => -1]);
                secure_log("Created indexes on player_notes collection");

                return [];
            }


            $totalCount = $db->player_notes->countDocuments([]);
            secure_log("Total documents in player_notes collection: $totalCount");


            $matchingCount = $db->player_notes->countDocuments($query);
            secure_log("Documents matching query: $matchingCount");


            if ($totalCount > 0 && $matchingCount === 0) {
                $allNotes = $db->player_notes->find([], ['limit' => 10])->toArray();
                secure_log("Sample notes in collection: " . json_encode($allNotes));
            }

            $cursor = $db->player_notes->find($query, [
                'sort' => ['created_at' => -1],
                'limit' => 1000 // Prevent memory issues with large note collections
            ]);

            $notes = [];
            $count = 0;
            foreach ($cursor as $note) {
                $count++;
                $note_array = (array)$note;


            if (isset($note_array['created_at']) && $note_array['created_at'] instanceof MongoDB\BSON\UTCDateTime) {
                $date = $note_array['created_at']->toDateTime();


                $date->setTimezone(new DateTimeZone('America/New_York'));


                $note_array['formatted_date'] = $date->format('M j, Y g:i A') . ' ET';



                $note_array['created_at'] = $date->format('c'); // ISO 8601 format
            }


            if (isset($note_array['_id'])) {
                unset($note_array['_id']);
            }


            if (isset($note_array['staff_id'])) {
                try {

                    $staff = $db->staff_activity->findOne(['user_id' => $note_array['staff_id']]);
                    if ($staff && isset($staff['avatar_hash']) && !empty($staff['avatar_hash'])) {

                        if (strpos($staff['avatar_hash'], 'https://') === 0) {
                            $note_array['staff_avatar_url'] = $staff['avatar_hash'];
                        } else {

                            $avatar_hash = $staff['avatar_hash'];
                            $user_id = $note_array['staff_id'];
                            $is_animated = strpos($avatar_hash, 'a_') === 0;
                            $extension = $is_animated ? 'gif' : 'png';
                            $note_array['staff_avatar_url'] = "https://cdn.discordapp.com/avatars/{$user_id}/{$avatar_hash}.{$extension}?size=128";
                        }
                    }
                } catch (Exception $e) {
                    secure_log("Error fetching staff avatar: " . $e->getMessage());

                }
            }

                $notes[] = $note_array;
            }

            secure_log("Found $count notes for the query");
            return $notes;
        } catch (Exception $e) {
            secure_log("Error querying player_notes: " . $e->getMessage());
            secure_log("Stack trace: " . $e->getTraceAsString());
            throw $e;
        }
    } catch (Exception $e) {
        secure_log("Error getting player notes: " . $e->getMessage());
        secure_log("Stack trace: " . $e->getTraceAsString());
        return [];
    }
}

/**
 * Search player or faction information
 *
 * @param string $query Search query
 * @param string $type Search type (player/faction)
 * @return array|null Search result
 */
/**
 * Get search suggestions for players or factions
 *
 * @param string $query Search query
 * @param string $type Search type (player/faction)
 * @return array Search suggestions
 */
function get_search_suggestions($query, $type) {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }


        $query = sanitize_mongo_query($query);
        $type = sanitize_mongo_query($type);


        if (!in_array($type, ['player', 'faction'])) {
            throw new Exception("Invalid search type");
        }


        $regex_pattern = preg_quote($query, '/');
        $suggestions = [];

        if ($type === 'player') {

            $collections = [];
            foreach ($db->listCollections() as $collectionInfo) {
                $collections[] = $collectionInfo->getName();
            }

            if (in_array('player_data', $collections)) {

                $exactMatches = $db->player_data->find(
                    ['playernames' => ['$regex' => "^{$regex_pattern}$", '$options' => 'i']],
                    ['projection' => ['playernames' => 1, '_id' => 0], 'limit' => 50]
                )->toArray();


                $startsWithMatches = $db->player_data->find(
                    ['playernames' => ['$regex' => "^{$regex_pattern}", '$options' => 'i']],
                    ['projection' => ['playernames' => 1, '_id' => 0], 'limit' => 50]
                )->toArray();


                $containsMatches = $db->player_data->find(
                    ['playernames' => ['$regex' => "{$regex_pattern}", '$options' => 'i']],
                    ['projection' => ['playernames' => 1, '_id' => 0], 'limit' => 50]
                )->toArray();


                $allNames = [];


                foreach ($exactMatches as $player) {
                    if (isset($player['playernames']) && is_array($player['playernames'])) {
                        foreach ($player['playernames'] as $name) {
                            if (!in_array($name, $allNames)) {
                                $allNames[] = $name;
                            }
                        }
                    }
                }


                foreach ($startsWithMatches as $player) {
                    if (isset($player['playernames']) && is_array($player['playernames'])) {
                        foreach ($player['playernames'] as $name) {
                            if (!in_array($name, $allNames)) {
                                $allNames[] = $name;
                            }
                        }
                    }
                }


                foreach ($containsMatches as $player) {
                    if (isset($player['playernames']) && is_array($player['playernames'])) {
                        foreach ($player['playernames'] as $name) {
                            if (!in_array($name, $allNames)) {
                                $allNames[] = $name;
                            }
                        }
                    }
                }


                sort($allNames);


                $suggestions = array_slice($allNames, 0, 10);
            }
        } elseif ($type === 'faction') {

            if (in_array('faction_data', $collections)) {

                $exactMatches = $db->faction_data->find(
                    ['name' => ['$regex' => "^{$regex_pattern}$", '$options' => 'i']],
                    ['projection' => ['name' => 1, '_id' => 0], 'limit' => 50]
                )->toArray();


                $startsWithMatches = $db->faction_data->find(
                    ['name' => ['$regex' => "^{$regex_pattern}", '$options' => 'i']],
                    ['projection' => ['name' => 1, '_id' => 0], 'limit' => 50]
                )->toArray();


                $containsMatches = $db->faction_data->find(
                    ['name' => ['$regex' => "{$regex_pattern}", '$options' => 'i']],
                    ['projection' => ['name' => 1, '_id' => 0], 'limit' => 50]
                )->toArray();


                $allNames = [];


                foreach ([$exactMatches, $startsWithMatches, $containsMatches] as $matches) {
                    foreach ($matches as $faction) {
                        if (isset($faction['name']) && !in_array($faction['name'], $allNames)) {
                            $allNames[] = $faction['name'];
                        }
                    }
                }


                sort($allNames);


                $suggestions = array_slice($allNames, 0, 10);
            }
        }

        return $suggestions;
    } catch (Exception $e) {
        secure_log("Error getting search suggestions: " . $e->getMessage());
        return [];
    }
}

/**
 * Search player or faction information
 *
 * @param string $query Search query
 * @param string $type Search type (player/faction)
 * @return array|null Search result
 */
function search_info($query, $type) {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }


        $query = sanitize_mongo_query($query);
        $type = sanitize_mongo_query($type);


        if (!in_array($type, ['player', 'faction'])) {
            throw new Exception("Invalid search type");
        }

        $result = null;


        $rank_colors = [
            1 => "#FF5555",
            2 => "#00AA00",
            3 => "#55FF55",
            4 => "#00AAAA",
            5 => "#55FFFF",
            6 => "#FF55FF",
            7 => "#AA0000",
            8 => "#FF5555",
            9 => "#AA00AA",
            10 => "#AA0000",
        ];

        $rank_names = [
            1 => "Member",
            2 => "VIP",
            3 => "VIP+",
            4 => "MVP",
            5 => "MVP+",
            6 => "Builder",
            7 => "Helper",
            8 => "Mod",
            9 => "Admin",
            10 => "Owner"
        ];

        if ($type === 'player') {

            secure_log("Searching for player in player_data collection: " . $query);


            $regex_pattern = preg_quote($query, '/');

            try {

                $collections = [];
                foreach ($db->listCollections() as $collectionInfo) {
                    $collections[] = $collectionInfo->getName();
                }

                if (in_array('player_data', $collections)) {

                    $projection = [
                        'projection' => [
                            'xuid' => 1, //
                            'playernames' => 1,
                            'online' => 1,
                            'firstlogin' => 1,
                            'lastlogin' => 1,
                            'groupsettings.rankid' => 1,
                            'games.factions.stats.doubloons' => 1,
                            'games.factions.stats.strength' => 1,
                            'games.factions.stats.kills' => 1,
                            'games.factions.stats.killstreak' => 1,
                            'games.factions.stats.deaths' => 1,
                            'games.factions.bounties' => 1,
                            'games.factions.request' => 1,
                            'games.factions.name' => 1,
                            'games.factions.role' => 1,
                            'games.factions.firstjoined' => 1,                       
                            '_id' => 0 
                        ]
                    ];


                    secure_log("Trying exact match for player: " . $query);
                    $exactQuery = ['playernames' => ['$regex' => "^{$regex_pattern}$", '$options' => 'i']];
                    $result = $db->player_data->findOne($exactQuery, $projection);


                    if (!$result) {
                        secure_log("No exact match found, trying starts with match for: " . $query);
                        $startsWithQuery = ['playernames' => ['$regex' => "^{$regex_pattern}", '$options' => 'i']];
                        $result = $db->player_data->findOne($startsWithQuery, $projection);
                    }


                    if (!$result) {
                        secure_log("No starts with match found, trying contains match for: " . $query);
                        $containsQuery = ['playernames' => ['$regex' => "{$regex_pattern}", '$options' => 'i']];
                        $result = $db->player_data->findOne($containsQuery, $projection);
                    }

                    if ($result) {
                        secure_log("Found player in player_data collection");
                        $result = (array)$result;

                        try {


                            $formatted_result = [
                                'xuid' => $result['xuid'] ?? '',
                                'playernames' => $result['playernames'] ?? [$query],
                                'online' => $result['online'] ?? false,
                                'firstlogin' => $result['firstlogin'] ?? null,
                                'lastlogin' => $result['lastlogin'] ?? null,
                                'groupsettings' => [
                                    'rankid' => isset($result['groupsettings']) && isset($result['groupsettings']['rankid']) ?
                                        $result['groupsettings']['rankid'] : 'player'
                                ]
                            ];


                            if (isset($result['games']) && isset($result['games']['factions'])) {
                                $factions = $result['games']['factions'];

                                $formatted_result['games'] = [
                                    'factions' => [
                                        'name' => $factions['name'] ?? 'None',
                                        'role' => $factions['role'] ?? 0,
                                        'request' => $factions['request'] ?? null,
                                        'firstjoined' => $factions['firstjoined'] ?? null
                                    ]
                                ];


                                if (isset($factions['stats'])) {
                                    $formatted_result['games']['factions']['stats'] = [
                                        'doubloons' => $factions['stats']['doubloons'] ?? 0,
                                        'strength' => $factions['stats']['strength'] ?? 0,
                                        'kills' => $factions['stats']['kills'] ?? 0,
                                        'deaths' => $factions['stats']['deaths'] ?? 0,
                                        'killstreak' => $factions['stats']['killstreak'] ?? 0
                                    ];
                                }


                                if (isset($factions['bounties'])) {
                                    $formatted_result['games']['factions']['bounties'] = $factions['bounties'];
                                }
                            } else {

                                $formatted_result['games'] = [
                                    'factions' => [
                                        'stats' => [
                                            'doubloons' => 0,
                                            'strength' => 0,
                                            'kills' => 0,
                                            'deaths' => 0,
                                            'killstreak' => 0
                                        ],
                                        'bounties' => [],
                                        'request' => null,
                                        'name' => 'None',
                                        'role' => 0,
                                        'firstjoined' => null
                                    ]
                                ];
                            }




                            $result = $formatted_result;
                        } catch (Exception $e) {
                            secure_log("Error formatting player data: " . $e->getMessage());
                            secure_log("Player data structure: " . json_encode($result));

                            $result = null;
                        }
                    } else {
                        secure_log("Player not found in player_data collection");
                    }
                } else {
                    secure_log("player_data collection does not exist in database");
                }
            } catch (Exception $e) {
                secure_log("Error searching player_data collection: " . $e->getMessage());

            }


            if (!$result) {
                secure_log("Falling back to players collection");
                try {
                    $result = $db->players->findOne([
                        'username' => ['$regex' => "^{$regex_pattern}$", '$options' => 'i']
                    ]);

                    if ($result) {
                        secure_log("Found player in players collection");
                        $result = (array)$result;
                        $rank_id = isset($result['rankId']) ? $result['rankId'] : null;
                        $result['rankColor'] = isset($rank_colors[$rank_id]) ? $rank_colors[$rank_id] : "#AAAAAA";
                        $result['rankName'] = isset($rank_names[$rank_id]) ? $rank_names[$rank_id] : "Unknown";


                        if (isset($result['_id'])) {
                            unset($result['_id']);
                        }
                    } else {
                        secure_log("Player not found in players collection either");
                    }
                } catch (Exception $e) {
                    secure_log("Error searching players collection: " . $e->getMessage());
                }
            }
        } elseif ($type === 'faction') {

            $regex_pattern = preg_quote($query, '/');


            $collections = [];
            foreach ($db->listCollections() as $collectionInfo) {
                $collections[] = $collectionInfo->getName();
            }


            if (in_array('faction_data', $collections)) {
                secure_log("Searching for faction in faction_data collection: " . $query);


                $factionQuery = ['name' => ['$regex' => "^{$regex_pattern}$", '$options' => 'i']];
                secure_log("Faction query: " . json_encode($factionQuery));

                $faction = $db->faction_data->findOne($factionQuery, [
                    '_id' => 0,
                    'name' => 1,
                    'allies' => 1,
                    'bankdoubloons' => 1,
                    'strength' => 1,
                    'members' => 1,
                    'requests' => 1
                ]);


                if (!$faction) {
                    secure_log("No exact match found, trying starts with match for faction: " . $query);
                    $factionQuery = ['name' => ['$regex' => "^{$regex_pattern}", '$options' => 'i']];
                    $faction = $db->faction_data->findOne($factionQuery, [
                        '_id' => 0,
                        'name' => 1,
                        'allies' => 1,
                        'bankdoubloons' => 1,
                        'strength' => 1,
                        'members' => 1,
                        'requests' => 1
                    ]);
                }


                if (!$faction) {
                    secure_log("No starts with match found, trying contains match for faction: " . $query);
                    $factionQuery = ['name' => ['$regex' => "{$regex_pattern}", '$options' => 'i']];
                    $faction = $db->faction_data->findOne($factionQuery, [
                        '_id' => 0,
                        'name' => 1,
                        'allies' => 1,
                        'bankdoubloons' => 1,
                        'strength' => 1,
                        'members' => 1,
                        'requests' => 1
                    ]);
                }

                if ($faction) {
                    secure_log("Faction found: " . ($faction['name'] ?? 'unnamed'));


                    $members = $faction['members'] ?? [];
                    $resolved_members = [];
                    $owner = null;
                    $officers = [];
                    $regular_members = [];

                    foreach ($members as $uuid_binary) {
                        if (empty($uuid_binary)) {
                            continue; // Skip empty UUIDs
                        }

                        // Query using the binary UUID directly (MongoDB will handle the comparison)
                        $player = $db->player_data->findOne(['uuid' => $uuid_binary]);

                        if ($player) {
                            $player_name = $player['username'] ?? "Unknown"; // Use the username field from new structure
                            $faction_data = $player['faction'] ?? []; // Use new faction structure
                            $faction_role = $faction_data['role'] ?? 3; // Default to regular member (role 3)

                            $resolved_members[] = $player_name;

                            // Role mapping: 0 = owner, 1 = officer, 2+ = member
                            if ($faction_role === 0) {
                                $owner = $player_name;
                            } elseif ($faction_role === 1) {
                                $officers[] = $player_name;
                            } else {
                                $regular_members[] = $player_name;
                            }
                        } else {
                            // Convert binary to hex for logging
                            $uuid_hex = $uuid_binary instanceof \MongoDB\BSON\Binary ? bin2hex($uuid_binary->getData()) : 'invalid';
                            secure_log("Player not found for UUID: " . $uuid_hex);
                        }
                    }


                    $result = [
                        'name' => $faction['name'] ?? 'Unknown',
                        'allies' => $faction['allies'] ?? [],
                        'bankdoubloons' => (int)($faction['bankdoubloons'] ?? 0),
                        'strength' => (float)($faction['strength'] ?? 0),
                        'members' => $regular_members, // Only regular members
                        'memberCount' => count($resolved_members), // Total count of all members
                        'requests' => $faction['requests'] ?? [],
                        'owner' => $owner,
                        'officers' => $officers
                    ];

                    secure_log("Formatted faction data: " . json_encode($result));
                } else {
                    secure_log("Faction not found in faction_data collection");
                }
            } else {
                secure_log("faction_data collection does not exist, falling back to factions collection");
            }


            if (!$result && in_array('factions', $collections)) {
                secure_log("Searching for faction in factions collection: " . $query);
                $faction = $db->factions->findOne([
                    'name' => ['$regex' => "^{$regex_pattern}$", '$options' => 'i']
                ]);

                if ($faction) {
                    secure_log("Found faction in factions collection");
                    $faction = (array)$faction;


                    if (isset($faction['_id'])) {
                        unset($faction['_id']);
                    }


                    $result = [
                        'name' => $faction['name'] ?? 'Unknown',
                        'allies' => $faction['allies'] ?? [],
                        'bankdoubloons' => (int)($faction['bankdoubloons'] ?? 0),
                        'strength' => (float)($faction['strength'] ?? 0),
                        'members' => $faction['members'] ?? [], // Regular members
                        'memberCount' => count($faction['members'] ?? []), // Total count
                        'requests' => $faction['requests'] ?? [],
                        'owner' => $faction['owner'] ?? null,
                        'officers' => $faction['officers'] ?? []
                    ];
                } else {
                    secure_log("Faction not found in factions collection either");
                }
            }
        }

        return $result;
    } catch (Exception $e) {
        secure_log("Error searching for information: " . $e->getMessage());
        return null;
    }
}

/**
 * Delete a faction (admin function)
 * Removes faction from database and updates all affected members and allies
 *
 * @param string $factionName Name of the faction to delete
 * @param string $staffId ID of the staff member performing the deletion
 * @param string $staffName Name of the staff member performing the deletion
 * @param string $reason Reason for deletion
 * @param array|null $factionData Pre-fetched faction data (optional, for better coordination)
 * @return array Result with success status and details
 */
function delete_faction_admin($factionName, $staffId, $staffName, $reason, $factionData = null) {
    try {
        $connection = get_db_connection();
        $db = $connection['db'];

        if (!$db) {
            throw new Exception("Database connection failed");
        }

        // Sanitize inputs
        $factionName = sanitize_mongo_query($factionName);
        $staffId = sanitize_mongo_query($staffId);
        $staffName = sanitize_mongo_query($staffName);
        $reason = sanitize_mongo_query($reason);

        // Use pre-fetched faction data if available, otherwise fetch from database
        $faction = null;
        if ($factionData && isset($factionData['Name'])) {
            secure_log("Using pre-fetched faction data for enhanced deletion", "info", [
                'faction_name' => $factionName,
                'member_count' => isset($factionData['Members']) ? count($factionData['Members']) : 0,
                'ally_count' => isset($factionData['Allies']) ? count($factionData['Allies']) : 0
            ]);

            // Convert API format to database format for consistency
            $faction = [
                'name' => $factionData['Name'],
                'members' => [],
                'allies' => []
            ];

            // Convert Members array to the format expected by the deletion logic
            if (isset($factionData['Members']) && is_array($factionData['Members'])) {
                foreach ($factionData['Members'] as $member) {
                    if (isset($member['UUID'])) {
                        $faction['members'][] = $member['UUID'];
                    }
                }
            }

            // Convert Allies to the format expected by the deletion logic
            if (isset($factionData['Allies']) && is_array($factionData['Allies'])) {
                $faction['allies'] = $factionData['Allies'];
            }
        } else {
            // Fallback to database lookup
            secure_log("Fetching faction data from database for deletion", "info", ['faction_name' => $factionName]);
            $faction = $db->faction_data->findOne(['name' => $factionName]);

            if (!$faction) {
                // Try the fallback collection
                $faction = $db->factions->findOne(['name' => $factionName]);
                if (!$faction) {
                    return [
                        'success' => false,
                        'error' => 'Faction not found',
                        'details' => "No faction found with name: {$factionName}"
                    ];
                }
            }
        }

        $membersAffected = 0;
        $alliesUpdated = 0;

        // Step 1: Remove faction from all allies
        if (isset($faction['allies']) && is_array($faction['allies'])) {
            foreach ($faction['allies'] as $allyName => $joinTime) {
                // Find the ally faction
                $allyFaction = $db->faction_data->findOne(['name' => $allyName]);
                if (!$allyFaction) {
                    $allyFaction = $db->factions->findOne(['name' => $allyName]);
                }

                if ($allyFaction) {
                    // Remove this faction from the ally's allies list
                    $newAllies = [];
                    if (isset($allyFaction['allies']) && is_array($allyFaction['allies'])) {
                        foreach ($allyFaction['allies'] as $ally => $time) {
                            if ($ally !== $factionName) {
                                $newAllies[$ally] = $time;
                            }
                        }
                    }

                    // Update the ally faction
                    $updateResult = $db->faction_data->updateOne(
                        ['name' => $allyName],
                        ['$set' => ['allies' => $newAllies]]
                    );

                    if ($updateResult->getModifiedCount() === 0) {
                        // Try fallback collection
                        $db->factions->updateOne(
                            ['name' => $allyName],
                            ['$set' => ['allies' => $newAllies]]
                        );
                    }

                    $alliesUpdated++;
                }
            }
        }

        // Step 2: Remove faction from all members
        $allMemberXuids = [];

        // Collect all member XUIDs (the faction stores XUIDs, not names)
        if (isset($faction['members']) && is_array($faction['members'])) {
            $allMemberXuids = array_merge($allMemberXuids, $faction['members']);
        }

        // Remove duplicates
        $allMemberXuids = array_unique($allMemberXuids);

        // Update each member to remove faction data using XUID
        foreach ($allMemberXuids as $memberXuid) {
            if (empty($memberXuid)) continue;

            // Find the player by XUID
            $updateResult = $db->player_data->updateOne(
                ['xuid' => (string)$memberXuid],
                [
                    '$unset' => [
                        'games.factions.name' => '',
                        'games.factions.role' => '',
                        'games.factions.firstjoined' => ''
                    ]
                ]
            );

            if ($updateResult->getModifiedCount() > 0) {
                $membersAffected++;
            }
        }

        // Also remove faction data from any players who have this faction name set
        // (in case there are any inconsistencies)
        $additionalUpdateResult = $db->player_data->updateMany(
            ['games.factions.name' => $factionName],
            [
                '$unset' => [
                    'games.factions.name' => '',
                    'games.factions.role' => '',
                    'games.factions.firstjoined' => ''
                ]
            ]
        );

        if ($additionalUpdateResult->getModifiedCount() > 0) {
            $membersAffected += $additionalUpdateResult->getModifiedCount();
        }

        // Step 3: Delete the faction itself
        $deleteResult = $db->faction_data->deleteOne(['name' => $factionName]);

        if ($deleteResult->getDeletedCount() === 0) {
            // Try fallback collection
            $deleteResult = $db->factions->deleteOne(['name' => $factionName]);
        }

        if ($deleteResult->getDeletedCount() === 0) {
            return [
                'success' => false,
                'error' => 'Failed to delete faction from database',
                'details' => "Faction {$factionName} could not be removed from database"
            ];
        }

        // Send Discord webhook notification
        send_faction_deletion_webhook($factionName, $staffId, $staffName, $reason, $membersAffected, $alliesUpdated);

        // Notify Go API about faction deletion to clear any cache
        try {
            require_once __DIR__ . '/../../includes/api_client.php';
            $apiClient = new ApiClient();
            $apiResult = $apiClient->notifyFactionDeletion($factionName);

            if (isset($apiResult['warning'])) {
                secure_log("Warning during faction deletion API notification: " . $apiResult['warning']);
            }
        } catch (Exception $e) {
            secure_log("Failed to notify Go API about faction deletion: " . $e->getMessage());
            // Don't fail the deletion if API notification fails
        }

        return [
            'success' => true,
            'members_affected' => $membersAffected,
            'allies_updated' => $alliesUpdated,
            'faction_name' => $factionName
        ];

    } catch (Exception $e) {
        secure_log("Error deleting faction: " . $e->getMessage());
        return [
            'success' => false,
            'error' => 'Database error occurred',
            'details' => $e->getMessage()
        ];
    }
}

/**
 * Send Discord webhook notification for faction deletion
 *
 * @param string $factionName Name of the deleted faction
 * @param string $staffId ID of the staff member
 * @param string $staffName Name of the staff member
 * @param string $reason Reason for deletion
 * @param int $membersAffected Number of members affected
 * @param int $alliesUpdated Number of allies updated
 */
function send_faction_deletion_webhook($factionName, $staffId, $staffName, $reason, $membersAffected, $alliesUpdated) {
    secure_log("send_faction_deletion_webhook called with: faction=$factionName, staff=$staffName, reason=$reason");

    // Load environment configuration
    global $ENV;
    if (empty($ENV)) {
        require_once __DIR__ . '/../../includes/config_parser.php';
        try {
            $ENV = load_env_config([
                '/etc/massacremc/config/admin.env',
                __DIR__ . '/../.env'
            ]);
            secure_log("Environment config loaded successfully for webhook");
        } catch (Exception $e) {
            secure_log("Failed to load environment config: " . $e->getMessage());
            $ENV = [];
        }
    }

    // Discord webhook URL
    $webhookUrl = $ENV['DISCORD_FACTION_WEBHOOK_URL'] ?? getenv('DISCORD_FACTION_WEBHOOK_URL') ?? null;

    if (empty($webhookUrl)) {
        secure_log("Discord faction webhook URL not configured");
        return;
    }

    secure_log("Webhook URL found: " . substr($webhookUrl, 0, 50) . "...");

    $timestamp = date('c'); // ISO 8601 format

    $embed = [
        'title' => '🗑️ Faction Deleted',
        'color' => 15158332, // Red color
        'fields' => [
            [
                'name' => 'Faction Name',
                'value' => $factionName,
                'inline' => true
            ],
            [
                'name' => 'Deleted By',
                'value' => $staffName,
                'inline' => true
            ],
            [
                'name' => 'Staff ID',
                'value' => $staffId,
                'inline' => true
            ],
            [
                'name' => 'Reason',
                'value' => $reason,
                'inline' => false
            ],
            [
                'name' => 'Members Affected',
                'value' => (string)$membersAffected,
                'inline' => true
            ],
            [
                'name' => 'Allies Updated',
                'value' => (string)$alliesUpdated,
                'inline' => true
            ]
        ],
        'timestamp' => $timestamp,
        'footer' => [
            'text' => 'MassacreMC Staff Portal'
        ]
    ];

    $payload = [
        'embeds' => [$embed]
    ];

    // Send webhook
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $webhookUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'User-Agent: MassacreMC-Admin/1.0'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 3);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    if (!empty($curlError)) {
        secure_log("Faction deletion webhook cURL error: $curlError");
    } else {
        secure_log("Webhook cURL executed without errors");
    }

    if ($httpCode !== 204 && $httpCode !== 200) {
        secure_log("Failed to send faction deletion webhook. HTTP Code: $httpCode, Response: $response");
    } else {
        secure_log("Faction deletion webhook sent successfully. HTTP Code: $httpCode");
    }
}
?>