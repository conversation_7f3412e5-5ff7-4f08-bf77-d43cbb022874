#!/bin/bash

# Security Monitor Script for MassacreMC
# Monitors for malicious attacks and automatically blocks IPs

LOG_FILE="/var/log/security_monitor.log"
ACCESS_LOG="/var/log/apache2/access.log"
BLOCKED_IPS_FILE="/tmp/blocked_ips.txt"

# Create log file if it doesn't exist
touch "$LOG_FILE"
touch "$BLOCKED_IPS_FILE"

log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

block_ip() {
    local ip="$1"
    local reason="$2"
    
    # Check if IP is already blocked
    if grep -q "$ip" "$BLOCKED_IPS_FILE"; then
        log_message "IP $ip already blocked"
        return
    fi
    
    # Add to blocked IPs file
    echo "$ip" >> "$BLOCKED_IPS_FILE"
    
    # Block using iptables (if available)
    if command -v iptables >/dev/null 2>&1; then
        iptables -A INPUT -s "$ip" -j DROP 2>/dev/null
        log_message "BLOCKED IP: $ip - Reason: $reason"
    else
        log_message "WARNING: iptables not available, cannot block IP $ip"
    fi
    
    # Send alert (you can customize this)
    log_message "SECURITY ALERT: Blocked malicious IP $ip for: $reason"
}

check_malicious_patterns() {
    # Define malicious patterns
    local patterns=(
        "device\.rsp"
        "setup\.cgi"
        "urbotnet"
        "botnet"
        "malware"
        "wget.*http"
        "chmod.*777"
        "rm.*arm7"
        "\.\./"
        "base64_decode"
        "eval\("
        "exec\("
        "system\("
    )
    
    # Check recent access log entries (last 100 lines)
    tail -n 100 "$ACCESS_LOG" 2>/dev/null | while read -r line; do
        # Extract IP address (first field)
        ip=$(echo "$line" | awk '{print $1}')
        
        # Skip if not a valid IP
        if [[ ! "$ip" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
            continue
        fi
        
        # Check for malicious patterns
        for pattern in "${patterns[@]}"; do
            if echo "$line" | grep -qiE "$pattern"; then
                block_ip "$ip" "Malicious pattern detected: $pattern"
                break
            fi
        done
        
        # Check for suspicious user agents
        if echo "$line" | grep -qE '"(Mozila/5\.0|)"\s*$'; then
            block_ip "$ip" "Suspicious user agent"
        fi
        
        # Check for excessive 404s (potential scanning)
        if echo "$line" | grep -q '" 404 '; then
            # Count 404s from this IP in last 10 minutes
            recent_404s=$(grep "$ip" "$ACCESS_LOG" | grep '" 404 ' | tail -n 20 | wc -l)
            if [ "$recent_404s" -gt 10 ]; then
                block_ip "$ip" "Excessive 404 errors (potential scanning)"
            fi
        fi
    done
}

# Main monitoring function
monitor_security() {
    log_message "Starting security monitoring..."
    
    while true; do
        check_malicious_patterns
        sleep 30  # Check every 30 seconds
    done
}

# Handle script termination
cleanup() {
    log_message "Security monitor stopped"
    exit 0
}

trap cleanup SIGINT SIGTERM

# Check if running as root (needed for iptables)
if [ "$EUID" -ne 0 ]; then
    log_message "WARNING: Not running as root, IP blocking may not work"
fi

# Start monitoring
case "${1:-monitor}" in
    "monitor")
        monitor_security
        ;;
    "check")
        check_malicious_patterns
        ;;
    "status")
        echo "Blocked IPs:"
        cat "$BLOCKED_IPS_FILE" 2>/dev/null || echo "No IPs blocked"
        ;;
    "unblock")
        if [ -n "$2" ]; then
            sed -i "/$2/d" "$BLOCKED_IPS_FILE"
            iptables -D INPUT -s "$2" -j DROP 2>/dev/null
            log_message "Unblocked IP: $2"
        else
            echo "Usage: $0 unblock <ip_address>"
        fi
        ;;
    *)
        echo "Usage: $0 {monitor|check|status|unblock <ip>}"
        exit 1
        ;;
esac
