package blocks

import (
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"image"
	"image/png"
	"os"
	"server/server/utils"
)

var specialItems = make(map[ItemAction]world.Item)

func RegisterSpecialItem(action ItemAction, it world.Item) {
	specialItems[action] = it
}

func SpecialItem(action ItemAction) world.Item {
	return specialItems[action]
}

type ClickType uint8

const (
	OnStartBreak ClickType = iota
	OnPunchAir
	OnItemUse
	OnItemUseOnBlock
	OnItemUseOnEntity
)

type ItemAction int

const (
	Wand ItemAction = iota
	ClaimShovel
	ThrowableTNT
	ThrowableNuke
	FakePearl
	PlayerHead
	MobHead
	Kit
	Backpack
	BankNote
	BagOfExperience
)

func Texture(file string) image.Image {
	texture, err := os.OpenFile(file, os.O_RDONLY, os.ModePerm)
	if err != nil {
		panic(err)
	}
	defer func(texture *os.File) {
		err := texture.Close()
		if err != nil {

		}
	}(texture)
	img, err := png.Decode(texture)
	if err != nil {
		panic(err)
	}
	return img
}

func AddEnchantmentLore(i item.Stack) item.Stack {
	var lore []string

	for _, e := range i.Enchantments() {
		enchant := e.Type()
		if enchant.Rarity().Weight() == 0 {
			lore = append(lore, text.Colourf("<dark-purple>%s %s</dark-purple>", enchant.Name(), utils.IntToRoman(e.Level())))

			// Only add descriptions and instructions for enchanted books
			if _, isEnchantedBook := i.Item().(item.EnchantedBook); isEnchantedBook {
				// Add description for custom enchantments
				description := getEnchantmentDescription(enchant.Name())
				if description != "" {
					lore = append(lore, text.Colourf("<grey>%s</grey>", description))
				}
			}
		}
	}

	// Only add instruction for enchanted books
	if len(lore) > 0 {
		if _, isEnchantedBook := i.Item().(item.EnchantedBook); isEnchantedBook {
			lore = append(lore, "")
			lore = append(lore, text.Colourf("<yellow>Use /enchant to apply this enchantment</yellow>"))
		}
	}

	return i.WithLore(lore...)
}

// getEnchantmentDescription returns a description for custom enchantments
func getEnchantmentDescription(enchantName string) string {
	descriptions := map[string]string{
		// Sword Enchantments
		"Decapitation":  "Chance to get mob heads when killing",
		"Frost Bite":    "Slows enemies when you hit them",
		"Home Run":      "Chance to create explosion on hit",
		"Thor Wrath":    "Chance to strike lightning on enemies",

		// Tool Enchantments (Pickaxe)
		"Drill":         "Mines blocks in a larger area",
		"Dynamite":      "Creates explosion when mining",
		"Golden":        "Earn doubloons when mining blocks",
		"Master Key":    "Higher chance to get crate keys",
		"Ore XP":        "Get extra XP when mining ores",

		// Bow Enchantments
		"Frost Shot":    "Arrows slow down targets",
		"Subsonic Sound": "Special arrow effects",
		"Tar Shot":      "Arrows immobilize targets",

		// Special
		"Glitter":       "Makes items sparkle",
	}

	return descriptions[enchantName]
}
