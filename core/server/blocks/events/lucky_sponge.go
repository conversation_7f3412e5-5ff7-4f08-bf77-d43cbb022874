package events

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/block/model"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"math/rand"
	"server/server/utils"
	"time"
)

// LuckySponge is a special sponge block that appears during lucky block events
// When players interact with it, they receive random rewards
// Note: Dynamic data (EventID, SpawnTime, PlayerUUID) is stored separately to avoid registration conflicts
type LuckySponge struct{}

// LuckySpongeData holds the dynamic data for lucky sponges
type LuckySpongeData struct {
	EventID    string
	SpawnTime  time.Time
	PlayerUUID string
}

// Global map to track sponge data by position
var spongeDataMap = make(map[cube.Pos]LuckySpongeData)

// BreakInfo defines how the lucky sponge can be broken
func (s LuckySponge) BreakInfo() block.BreakInfo {
	return block.BreakInfo{
		Hardness:        0.6,
		BlastResistance: 0.6,
		Harvestable: func(t item.Tool) bool {
			return true
		},
		Effective: func(t item.Tool) bool {
			// Any tool is effective, but hands work too
			return true
		},
		Drops: func(t item.Tool, enchantments []item.Enchantment) []item.Stack {
			// Lucky sponges don't drop themselves - they give rewards instead
			return []item.Stack{}
		},
	}
}

// Activate handles when a player right-clicks the lucky sponge
func (s LuckySponge) Activate(pos cube.Pos, _ cube.Face, tx *world.Tx, u item.User, _ *item.UseContext) bool {
	if pl, ok := u.(*player.Player); ok {
		s.handleInteraction(pl, pos, tx)
		return true
	}
	return false
}

// UseOnBlock handles when a player uses an item on the lucky sponge
func (s LuckySponge) UseOnBlock(pos cube.Pos, _ cube.Face, _ mgl64.Vec3, tx *world.Tx, u item.User, _ *item.UseContext) bool {
	if pl, ok := u.(*player.Player); ok {
		s.handleInteraction(pl, pos, tx)
		return true
	}
	return false
}

// SpongeInteractionHandler is an interface for handling sponge interactions
// This avoids circular imports by allowing the events package to register a handler
type SpongeInteractionHandler interface {
	HandleSpongeInteraction(pl *player.Player, pos cube.Pos, eventID string, spawnTime time.Time)
}

var spongeHandler SpongeInteractionHandler

// RegisterSpongeHandler registers the handler for sponge interactions
func RegisterSpongeHandler(handler SpongeInteractionHandler) {
	spongeHandler = handler
}

// handleInteraction processes the player's interaction with the lucky sponge
func (s LuckySponge) handleInteraction(pl *player.Player, pos cube.Pos, tx *world.Tx) {
	// Check if we have a registered handler
	if spongeHandler == nil {
		pl.Message(text.Colourf("<red>Event system is not available.</red>"))
		return
	}

	// Get sponge data
	data, exists := spongeDataMap[pos]
	if !exists {
		pl.Message(text.Colourf("<red>This sponge has lost its connection to the event system.</red>"))
		tx.SetBlock(pos, block.Air{}, nil)
		return
	}

	// Check if this sponge is too old (optional: prevent hoarding)
	if time.Since(data.SpawnTime) > 5*time.Minute {
		pl.Message(text.Colourf("<yellow>This sponge has dried up and lost its magic...</yellow>"))
		tx.SetBlock(pos, block.Air{}, nil)
		delete(spongeDataMap, pos)
		return
	}

	// Add some visual effects before giving the reward
	s.addInteractionEffects(pl, pos)

	// Handle the sponge interaction through the registered handler
	spongeHandler.HandleSpongeInteraction(pl, pos, data.EventID, data.SpawnTime)

	// Clean up the sponge data after interaction
	delete(spongeDataMap, pos)
}

// addInteractionEffects adds visual and audio effects when interacting with the sponge
func (s LuckySponge) addInteractionEffects(pl *player.Player, pos cube.Pos) {
	spongePosVec := pos.Vec3()
	
	// Add sparkle particles around the sponge
	for i := 0; i < 15; i++ {
		particlePos := spongePosVec.Add(mgl64.Vec3{
			(rand.Float64() - 0.5) * 2,
			rand.Float64() * 2,
			(rand.Float64() - 0.5) * 2,
		})
		utils.SpawnParticle(pl, particlePos, "minecraft:enchant")
	}

	// Add some more dramatic effects
	utils.SpawnParticle(pl, spongePosVec.Add(mgl64.Vec3{0, 1, 0}), "minecraft:totem")
}

// LightEmissionLevel makes the lucky sponge glow slightly
func (s LuckySponge) LightEmissionLevel() uint8 {
	return 3
}

// Model returns the block model for the lucky sponge
func (s LuckySponge) Model() world.BlockModel {
	return model.Solid{}
}

// EncodeItem returns the item encoding for the lucky sponge
func (s LuckySponge) EncodeItem() (name string, meta int16) {
	return "mmc:lucky_sponge", 0 // Use custom namespace to avoid conflicts
}

// EncodeBlock returns the block encoding for the lucky sponge
func (s LuckySponge) EncodeBlock() (name string, properties map[string]any) {
	return "mmc:lucky_sponge", nil // Use custom namespace to avoid conflicts
}

// Hash returns the hash for the lucky sponge block
var hashLuckySponge = block.NextHash()

func (s LuckySponge) Hash() (uint64, uint64) {
	// All LuckySponge blocks have the same hash since they're functionally identical
	// The dynamic fields (EventID, SpawnTime, PlayerUUID) are for internal tracking only
	// and don't affect the block's behavior or appearance
	return hashLuckySponge, 0
}

// RandomTick handles random ticking for the lucky sponge
func (s LuckySponge) RandomTick(pos cube.Pos, tx *world.Tx, _ *rand.Rand) {
	// Get sponge data
	data, exists := spongeDataMap[pos]
	if !exists {
		// Remove orphaned sponges
		tx.SetBlock(pos, block.Air{}, nil)
		return
	}

	// Check if the sponge should expire
	if time.Since(data.SpawnTime) > 10*time.Minute {
		// Remove expired sponges
		tx.SetBlock(pos, block.Air{}, nil)
		delete(spongeDataMap, pos)

		// Add some disappearing effects
		for i := 0; i < 5; i++ {
			_ = pos.Vec3().Add(mgl64.Vec3{
				(rand.Float64() - 0.5),
				rand.Float64(),
				(rand.Float64() - 0.5),
			})
			// Note: We can't spawn particles here since we don't have a player reference
			// This would need to be handled differently in a real implementation
		}
	}
}

// ScheduledTick handles scheduled ticking for the lucky sponge
func (s LuckySponge) ScheduledTick(pos cube.Pos, tx *world.Tx, _ *rand.Rand) {
	// Add ambient particle effects occasionally
	if utils.RandChance(10) { // 10% chance per tick
		// Add subtle sparkle effects
		_ = pos.Vec3().Add(mgl64.Vec3{0, 1, 0})
		// Note: We can't spawn particles here since we don't have a player reference
		// In a real implementation, you'd need to find nearby players and spawn particles for them
	}
}

// NewLuckySponge creates a new lucky sponge block and stores its data
func NewLuckySponge(eventID string, playerUUID string) LuckySponge {
	return LuckySponge{}
}

// PlaceLuckySponge places a lucky sponge at the specified position with the given data
func PlaceLuckySponge(pos cube.Pos, eventID string, playerUUID string) {
	spongeDataMap[pos] = LuckySpongeData{
		EventID:    eventID,
		SpawnTime:  time.Now(),
		PlayerUUID: playerUUID,
	}
}

// GetSpongeData returns the data for a sponge at the given position
func GetSpongeData(pos cube.Pos) (LuckySpongeData, bool) {
	data, exists := spongeDataMap[pos]
	return data, exists
}

// RemoveSpongeData removes the data for a sponge at the given position
func RemoveSpongeData(pos cube.Pos) {
	delete(spongeDataMap, pos)
}
