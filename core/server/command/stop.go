package command

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/world"
	"log/slog"
	core "server/server"
	"server/server/database"
	"server/server/user"
	"server/server/utils"
	"time"
)

type StopCommand struct{}

func (StopCommand) Allow(src cmd.Source) bool {
	return Stop.Test(src)
}

func (StopCommand) PermissionMessage(src cmd.Source) string {
	return Stop.PermissionMessage(src)
}

func (StopCommand) Run(_ cmd.Source, _ *cmd.Output, tx *world.Tx) {
	// Save world data first
	tx.World().Save()

	// CRITICAL: Comprehensive graceful shutdown via stop command
	slog.Default().Info("Initiating graceful shutdown via stop command...")


		// Enter shutdown mode: block damage and risky actions
		core.SetShutdownMode(true)

	// Stop checkpoint system first
	if database.CheckpointSystemInstance != nil {
		slog.Default().Info("Stopping checkpoint system...")
		database.CheckpointSystemInstance.Stop()
	}

	// Save all player data (both Dragonfly and custom data) before closing
	for pl := range core.MCServer.Players(nil) {
		// Move player to safe zone and sanitize state to prevent late deaths
		pl.Teleport(core.Config.Hub.SpawnPoint)
		pl.Heal(pl.MaxHealth(), nil)
		pl.SetFood(20)
		for _, e := range pl.Effects() { pl.RemoveEffect(e.Type()) }
		pl.Extinguish()

		// Cancel combat/teleport tags to avoid false combat log/death on shutdown
		if u := user.GetUserByUUID(pl.UUID()); u != nil {
			u.ClearCombatTag()
			u.CancelTeleportTimer()
		}
		// Save Dragonfly player data (inventory, position, health, etc.)
		if err := core.DragonflyConfig.PlayerProvider.Save(pl.UUID(), pl.Data(), core.MCServer.World()); err != nil {
			errorCode := utils.RandString(6)
			slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save Dragonfly player data: " + err.Error())
		}

		// Queue critical save for custom player data
		if database.SaveQueueInstance != nil {
			u := user.GetUserByUUID(pl.UUID())
			if u != nil {
				op := &database.SaveOperation{
					ID:       "stop_cmd_player_" + pl.UUID().String(),
					Type:     database.SaveTypePlayer,
					Data:     u.Data,
					Priority: database.PriorityCritical,
				}
				if err := database.SaveQueueInstance.QueueSave(op); err != nil {
					// Fallback to synchronous save
					if err := user.Save(pl); err != nil {
						errorCode := utils.RandString(6)
						slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data: " + err.Error())
					}
				}
			}
		} else {
			// Fallback to synchronous save
			if err := user.Save(pl); err != nil {
				errorCode := utils.RandString(6)
				slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data: " + err.Error())
			}
		}
	}

	// Queue final batch save
	if database.SaveQueueInstance != nil {
		op := &database.SaveOperation{
			ID:       "stop_cmd_batch_save",
			Type:     database.SaveTypeBatch,
			Data:     nil,
			Priority: database.PriorityCritical,
		}
		if err := database.SaveQueueInstance.QueueSave(op); err != nil {
			slog.Default().Error("Failed to queue stop command batch save", "error", err)
		}

		// Wait for save queue to complete with timeout
		slog.Default().Info("Waiting for save queue to complete...")
		if err := database.SaveQueueInstance.Shutdown(30 * time.Second); err != nil {
			slog.Default().Error("Save queue shutdown failed", "error", err)
		}
	}

	// Final fallback: save all cached data synchronously
	slog.Default().Info("Performing final synchronous save...")
	for identifier, err := range database.DB.SaveAll() {
		errorCode := utils.RandString(6)
		slog.Default().With("code", errorCode).With("identifier", identifier).Error("Failed to save cached data: " + err.Error())
	}

	// Perform final recovery state save
	if database.RecoverySystemInstance != nil {
		slog.Default().Info("Saving final recovery state...")
		if err := database.RecoverySystemInstance.PerformRecoveryCheck(); err != nil {
			slog.Default().Error("Failed to save final recovery state", "error", err)
		}
	}

	// Stop crash protection system
	if database.CrashProtectionInstance != nil {
		slog.Default().Info("Stopping crash protection...")
		database.CrashProtectionInstance.Shutdown()
	}

	// Now it's safe to close the server
	slog.Default().Info("Closing server...")
	utils.Panic(core.MCServer.Close())
}
