package base

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/command"
	"server/server/command/factions"
	"server/server/command/worldedit/wecmd"
)

func RegisterLobbyCommands() {
	srvPrefix := text.Colourf(server.Config.Prefix)
	cmd.Register(cmd.New("gamemode", text.Colourf("%v <dark-green>To change a specific player's gamemode</dark-green>", srvPrefix), nil, command.GameModeCommand{}))
	cmd.Register(cmd.New("rank", text.Colourf("%v <dark-green>To give a rank to a player</dark-green>", srvPrefix), nil, command.RankCommand{}))
	cmd.Register(cmd.New("stop", text.Colourf("%v <dark-green>To stop the server.</dark-green>", srvPrefix), nil, command.StopCommand{}))
	cmd.Register(cmd.New("ping", text.Colourf("%v <dark-green>To check a specific player's ping</dark-green>", srvPrefix), nil, command.PingCommand{}))
	cmd.Register(cmd.New("vote", text.Colourf("%v <dark-green>To vote for the server and receive rewards</dark-green>", srvPrefix), nil, command.VoteCommand{}))
}

func RegisterFactionCommands() {
	srvPrefix := text.Colourf(server.Config.Prefix)
	cmd.Register(cmd.New("bank", text.Colourf("%v <dark-green>To manage your faction's bank</dark-green>", srvPrefix), nil, factions.FactionDepositCommand{}, factions.FactionBalanceCommand{}, factions.FactionWithdrawCommand{}))
	cmd.Register(cmd.New("home", text.Colourf("%v <dark-green>To manage your homes</dark-green>", srvPrefix), nil, factions.HomeSetCommand{}, factions.HomeDeleteCommand{}, factions.HomeTeleportCommand{}))
	cmd.Register(cmd.New("duel", text.Colourf("%v <dark-green>To handle duel requests</dark-green>", srvPrefix), nil, factions.DuelInviteCommand{}, factions.DuelAcceptCommand{}))
	cmd.Register(cmd.New("repair", text.Colourf("%v <dark-green>To repair any items from breaking</dark-green>", srvPrefix), nil, factions.RepairHandCommand{}, factions.RepairAllCommand{}))
	cmd.Register(cmd.New("sell", text.Colourf("%v <dark-green>To sell items to gain doubloons</dark-green>", srvPrefix), nil, factions.SellHandCommand{}, factions.SellAllCommand{}))

	cmd.Register(cmd.New("faction", text.Colourf("%v <dark-green>To manage faction</dark-green>", srvPrefix), []string{"f"}, factions.FactionManageCommand{}))
	cmd.Register(cmd.New("fly", text.Colourf("%v <dark-green>To enable/disable flight</dark-green>", srvPrefix), nil, factions.FlyCommand{}))
	cmd.Register(cmd.New("coords", text.Colourf("%v <dark-green>To toggle coordinate display on/off</dark-green>", srvPrefix), []string{"coordinates"}, factions.CoordsCommand{}))
	cmd.Register(cmd.New("bounty", text.Colourf("%v <dark-green>To add a bounty on a player's head</dark-green>", srvPrefix), nil, factions.BountyCommand{}))
	cmd.Register(cmd.New("shop", text.Colourf("%v <dark-green>To open the shop menu</dark-green>", srvPrefix), nil, factions.ShopCommand{}))
	cmd.Register(cmd.New("enchant", text.Colourf("%v <dark-green>To combine enchantments of 2 items</dark-green>", srvPrefix), nil, factions.EnchantCommand{}))
	cmd.Register(cmd.New("kit", text.Colourf("%v <dark-green>To open the kit menu</dark-green>", srvPrefix), nil, factions.KitCommand{}))
	cmd.Register(cmd.New("pv", text.Colourf("%v <dark-green>To open your virtual vault</dark-green>", srvPrefix), nil, factions.PlayerVaultCommand{}))
	cmd.Register(cmd.New("withdraw", text.Colourf("%v <dark-green>To withdraw a bank note from your doubloons balance</dark-green>", srvPrefix), nil, factions.WithdrawCommand{}))
	cmd.Register(cmd.New("pay", text.Colourf("%v <dark-green>To pay a specific amount of money to a certain player</dark-green>", srvPrefix), nil, factions.PayCommand{}))
	cmd.Register(cmd.New("hub", text.Colourf("%v <dark-green>To teleport to hub</dark-green>", srvPrefix), nil, factions.HubCommand{}))
	cmd.Register(cmd.New("wild", text.Colourf("%v <dark-green>To teleport to the wild</dark-green>", srvPrefix), nil, factions.WildCommand{}))
	cmd.Register(cmd.New("tpa", text.Colourf("%v <dark-green>To send a request to teleport to a player</dark-green>", srvPrefix), nil, factions.TPACommand{}))
	cmd.Register(cmd.New("tpahere", text.Colourf("%v <dark-green>To send a request to get teleported to the player</dark-green>", srvPrefix), nil, factions.TPAHereCommand{}))
	cmd.Register(cmd.New("tpaaccept", text.Colourf("%v <dark-green>To accept a teleport request of player</dark-green>", srvPrefix), nil, factions.TPAAcceptCommand{}))
	cmd.Register(cmd.New("tp", text.Colourf("%v <dark-green>To teleport to a certain XYZ position.</dark-green>", srvPrefix), nil, factions.TPCommand{}))


}

func RegisterBuildCommands() {
	wePrefix := text.Colourf(server.Config.WTPrefix)
	//Selection:
	cmd.Register(cmd.New("wand", text.Colourf("%v <dark-aqua>To give you a wand that can set positions to form a volume selection</dark-aqua>", wePrefix), []string{"/wand"}, wecmd.Wand{}))
	cmd.Register(cmd.New("pos1", text.Colourf("%v <dark-aqua>To set the first position of a volume selection</dark-aqua>", wePrefix), []string{"/1"}, wecmd.Pos1{}))
	cmd.Register(cmd.New("pos2", text.Colourf("%v <dark-aqua>To set the second position to complete a volume selection</dark-aqua>", wePrefix), []string{"/2"}, wecmd.Pos2{}))

	//Block manipulation:
	cmd.Register(cmd.New("up", text.Colourf("%v <dark-aqua>To place a block below you</dark-aqua>", wePrefix), []string{"/up"}, wecmd.Up{}))
	cmd.Register(cmd.New("fill", text.Colourf("%v <dark-aqua>To fill the selected volume with the specified block</dark-aqua>", wePrefix), []string{"/fill", "/set"}, wecmd.Fill{}))
	cmd.Register(cmd.New("walls", text.Colourf("%v <dark-aqua>To create a closed wall with the specified block</dark-aqua>", wePrefix), []string{"/walls"}, wecmd.Walls{}))
	cmd.Register(cmd.New("paste", text.Colourf("%v <dark-aqua>To Paste the selected volume</dark-aqua>", wePrefix), []string{"/paste"}, wecmd.Paste{}))
	cmd.Register(cmd.New("rotate", text.Colourf("%v <dark-aqua>To rotate all blocks from the selected volume in a specific angle</dark-aqua>", wePrefix), []string{"/rotate"}, wecmd.Rotate{}))
	cmd.Register(cmd.New("mirror", text.Colourf("%v <dark-aqua>To mirror all blocks from the selected volume with you acting as the mirror bisector</dark-aqua>", wePrefix), []string{"/mirror"}, wecmd.Mirror{}))
	cmd.Register(cmd.New("replace", text.Colourf("%v <dark-aqua>To replace all blocks from the selected volume with other specified blocks</dark-aqua>", wePrefix), []string{"/replace"}, wecmd.Replace{}))
	cmd.Register(cmd.New("undo", text.Colourf("%v <dark-aqua>To undo any block manipulations</dark-aqua>", wePrefix), []string{"/undo"}, wecmd.Undo{}))
	cmd.Register(cmd.New("redo", text.Colourf("%v <dark-aqua>To redo any undoes</dark-aqua>", wePrefix), []string{"/redo"}, wecmd.Redo{}))
	cmd.Register(cmd.New("sphere", text.Colourf("%v <dark-aqua>To create a sphere with a specific radius</dark-aqua>", wePrefix), []string{"/sphere"}, wecmd.Sphere{}))
	cmd.Register(cmd.New("cylinder", text.Colourf("%v <dark-aqua>To create a cylinder with a specific radius & height", wePrefix), []string{"/cylinder"}, wecmd.Cylinder{}))
}
