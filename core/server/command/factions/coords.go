package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/language"
	"server/server/user"
	"strings"
)

type CoordsCommand struct {
	Action CoordsAction `cmd:"action,optional"`
}

type CoordsAction string

func (CoordsAction) Type() string {
	return "CoordsAction"
}

func (CoordsAction) Options(cmd.Source) []string {
	return []string{"on", "off", "toggle"}
}

func (c CoordsCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		
		action := string(c.Action)
		if action == "" {
			action = "toggle" // Default to toggle if no action specified
		}
		
		var newState bool
		var actionText string
		
		switch strings.ToLower(action) {
		case "on":
			newState = true
			actionText = "enabled"
		case "off":
			newState = false
			actionText = "disabled"
		case "toggle":
			newState = !u.Data.Faction.ShowCoordinates
			if newState {
				actionText = "enabled"
			} else {
				actionText = "disabled"
			}
		default:
			o.Error(text.Colourf("<red>Invalid action. Use: on, off, or toggle</red>"))
			return
		}
		
		// Update the user's coordinate display preference
		u.Data.Faction.ShowCoordinates = newState
		
		// Save the user data
		user.DebouncedSave(pl)
		
		// Send confirmation message
		o.Print(text.Colourf(language.Translate(pl).Commands.Coords.Success, server.Config.Prefix, actionText))
		
		// If coordinates are enabled, start displaying them
		if newState {
			go u.StartCoordinateDisplay()
		}
		
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}
