package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/title"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"math/rand"
	"server/server/language"
	"server/server/user"
	"time"
)

type WildCommand struct{}

func (w WildCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)

		// Check if player is in combat
		if u.IsCoolDownActive(user.Combat, 15*time.Second, false, false, true) {
			return
		}

		// Check if player already has an active teleport timer
		if u.HasActiveTeleportTimer() {
			pl.Message(text.Colourf("<red>You already have a teleport in progress!</red>"))
			return
		}

		// Generate random wild location
		x := -4500 + rand.Intn(9000)
		z := -4500 + rand.Intn(9000)
		y := tx.HighestBlock(x, z) + 4
		targetPos := mgl64.Vec3{float64(x), float64(y), float64(z)}

		// CRITICAL: Save player data BEFORE starting teleport to prevent item loss
		user.SavePlayerDataImmediate(pl)

		// Start the teleport timer
		pl.SendTitle(title.New(text.Colourf("<green>5</green>")).WithSubtitle(text.Colourf("<grey>Don't move!</grey>")))
		u.StartTeleportTimer(targetPos, func() {
			o.Printf(text.Colourf(language.Translate(pl).Whoosh))
		})
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}
