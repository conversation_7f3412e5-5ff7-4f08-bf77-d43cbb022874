package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/language"
	"server/server/user"
	"time"
)

type HubCommand struct{}

func (HubCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		if u.IsCoolDownActive(user.Combat, 15*time.Second, false, false, true) {
			return
		}

		// CRITICAL: Save player data BEFORE teleporting to prevent item loss
		// This ensures if server crashes after /hub, items are preserved
		user.SavePlayerDataImmediate(pl)

		pl.Teleport(server.Config.Hub.SpawnPoint)
		o.Print(text.Colourf(language.Translate(pl).Commands.Hub.Success))

	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}
