package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server"
	"server/server/language"
	"server/server/user"
	"time"
)

type TPACommand struct {
	Targets []cmd.Target `cmd:"target"`
}

func (tc TPACommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		if len(tc.Targets) != 1 {
			o.Error(text.Colourf(language.Translate(pl).Commands.Target))
			return
		}
		t := tc.Targets[0].(*player.Player)
		ut := user.GetUser(t)
		ut.FactionInfo.TPARequest.Target = pl.H()
		ut.FactionInfo.TPARequest.To = t.H()
		go func() {
			time.Sleep(60 * time.Second)
			if (ut.FactionInfo.TPARequest != user.TPARequest{}) {
				ut.FactionInfo.TPARequest = user.TPARequest{}
				t.Message(text.Colourf(language.Translate(t).Commands.TPA.TimeoutTarget, server.Config.Prefix, pl.NameTag()))
				pl.Message(text.Colourf(language.Translate(pl).Commands.TPA.TimeoutSender, server.Config.Prefix, t.NameTag()))
			}
		}()
		t.Message(text.Colourf(language.Translate(t).Commands.TPA.RequestReceive, server.Config.Prefix, pl.NameTag()))
		o.Print(text.Colourf(language.Translate(pl).Commands.TPA.Request, server.Config.Prefix, t.NameTag()))
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type TPAHereCommand struct {
	Targets []cmd.Target `cmd:"target"`
}

func (tc TPAHereCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		if len(tc.Targets) != 1 {
			o.Error(text.Colourf(language.Translate(pl).Commands.Target))
			return
		}
		t := tc.Targets[0].(*player.Player)
		ut := user.GetUser(t)
		ut.FactionInfo.TPARequest.Target = t.H()
		ut.FactionInfo.TPARequest.To = pl.H()
		go func() {
			time.Sleep(60 * time.Second)
			if (ut.FactionInfo.TPARequest != user.TPARequest{}) {
				ut.FactionInfo.TPARequest = user.TPARequest{}
				t.Message(text.Colourf(language.Translate(t).Commands.TPA.TimeoutTarget, server.Config.Prefix, pl.NameTag()))
				pl.Message(text.Colourf(language.Translate(pl).Commands.TPA.TimeoutSender, server.Config.Prefix, t.NameTag()))
			}
		}()
		t.Message(text.Colourf(language.Translate(t).Commands.TPA.HereRequest, server.Config.Prefix, pl.NameTag()))
		o.Print(text.Colourf(language.Translate(pl).Commands.TPA.Request, server.Config.Prefix, t.NameTag()))
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type TPAAcceptCommand struct {
}

func (TPAAcceptCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		tr := u.FactionInfo.TPARequest
		if e, ok := tr.Target.Entity(tx); ok {
			if eto, ok := tr.To.Entity(tx); ok {
				// CRITICAL: Save both players' data BEFORE teleporting
				targetPlayer := e.(*player.Player)
				user.SavePlayerDataImmediate(targetPlayer)
				user.SavePlayerDataImmediate(pl)

				targetPlayer.Teleport(eto.Position())
			}
		}
		u.FactionInfo.TPARequest = user.TPARequest{}
		o.Print(text.Colourf(language.Translate(pl).Commands.TPA.Accept, server.Config.Prefix))
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}
