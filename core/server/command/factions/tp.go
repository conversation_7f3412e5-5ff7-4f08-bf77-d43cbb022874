package factions

import (
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server/command"
	"server/server/language"
	"server/server/user"
)

type TPCommand struct {
	X float64 `cmd:"X"`
	Y float64 `cmd:"Y"`
	Z float64 `cmd:"Z"`
}

func (TPCommand) Allow(src cmd.Source) bool {
	return command.TP.Test(src)
}

func (TPCommand) PermissionMessage(src cmd.Source) string {
	return command.Fly.PermissionMessage(src)
}

func (t TPCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		// CRITICAL: Save player data BEFORE teleporting to prevent item loss
		user.SavePlayerDataImmediate(pl)

		pl.Teleport(mgl64.Vec3{t.X, t.Y, t.Z})
		o.Printf(text.Colourf(language.Translate(pl).Whoosh))
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}
