package factions

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"golang.org/x/exp/maps"
	"server/server"
	"server/server/language"
	"server/server/user"
	"time"
)

type HomeSetCommand struct {
	Set  cmd.SubCommand `cmd:"set"`
	Name string         `cmd:"name"`
}

func (h HomeSetCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		u.Data.Faction.Home[h.Name] = pl.Position()
		o.Print(text.Colourf(language.Translate(pl).Commands.Home.SetSuccess, server.Config.Prefix, h.Name, cube.PosFromVec3(pl.Position())))
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type HomeDeleteCommand struct {
	Delete cmd.SubCommand `cmd:"delete"`
	Name   Homes          `cmd:"name"`
}

func (h HomeDeleteCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		defer delete(u.Data.Faction.Home, string(h.Name))
		o.Print(text.Colourf(language.Translate(pl).Commands.Home.DeleteSuccess, server.Config.Prefix, h.Name, cube.PosFromVec3(u.Data.Faction.Home[string(h.Name)])))
	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type HomeTeleportCommand struct {
	Teleport cmd.SubCommand `cmd:"teleport"`
	Name     Homes          `cmd:"name"`
}

func (h HomeTeleportCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		if u.IsCoolDownActive(user.Combat, 15*time.Second, false, false, true) {
			return
		}

		// CRITICAL: Save player data BEFORE teleporting to prevent item loss
		user.SavePlayerDataImmediate(pl)

		pl.Teleport(u.Data.Faction.Home[string(h.Name)])
		o.Print(text.Colourf(language.Translate(pl).Whoosh))

	} else {
		o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
	}
}

type Homes string

func (Homes) Type() string {
	return "home"
}

func (Homes) Options(src cmd.Source) []string {
	if pl, ok := src.(*player.Player); ok {
		u := user.GetUser(pl)
		if u != nil {
			return maps.Keys(u.Data.Faction.Home)
		}
	}
	return nil
}
