package factions

import (
    "github.com/bedrock-gophers/inv/inv"
    "github.com/df-mc/dragonfly/server/block"
    "github.com/df-mc/dragonfly/server/cmd"
    "github.com/df-mc/dragonfly/server/event"
    "github.com/df-mc/dragonfly/server/item"
    "github.com/df-mc/dragonfly/server/item/inventory"
    "github.com/df-mc/dragonfly/server/player"
    "github.com/df-mc/dragonfly/server/world"
    "github.com/df-mc/dragonfly/server/world/sound"
    "github.com/sandertv/gophertunnel/minecraft/text"
    "server/server/blocks"
    "server/server/factions/enchants"
    "server/server/ui"
    "server/server/utils"
    "slices"
)

// Enhanced slot layout for better UX
const (
    InputSlot1  = 11 // Left input slot
    InputSlot2  = 13 // Right input slot
    OutputSlot  = 15 // Center output slot
    AnvilSlot   = 22 // Bottom center anvil
    InfoSlot    = 4  // Top center info
)

type EnchantCommand struct{}

// Run executes the enchant command, opening a custom enchanting UI
func (e EnchantCommand) Run(src cmd.Source, o *cmd.Output, _ *world.Tx) {
    if pl, ok := src.(*player.Player); ok {
        sess := utils.Session(pl)
        if sess == nil {
            o.Error(text.Colourf("<red>✗ Failed to initialize enchanting session. Please try again."))
            return
        }

        chestInv := inventory.New(inv.ContainerChest{}.Size(), func(slot int, before, after item.Stack) {
            sess.ViewSlotChange(slot, after)
        })

        // Enhanced menu with better title
        menu := inv.NewCustomMenu(text.Colourf("<dark-purple>✦ Enchantment Forge ✦"), inv.ContainerChest{}, chestInv, nil)

        // Function to update UI elements synchronously
        var updateUI func()
        updateUI = func() {
            it1, _ := chestInv.Item(InputSlot1)
            it2, _ := chestInv.Item(InputSlot2)

            // Update info and anvil slots
            updateInfoSlot(pl, chestInv, it1, it2)
            updateAnvilSlot(pl, chestInv)

            // Update output slot with preview
            if output, ok := mergeEnchantments([]item.Stack{it1, it2}); ok {
                if err := chestInv.SetItem(OutputSlot, *output); err != nil {
                    pl.Message(text.Colourf("<red>✗ Inventory error: %v", err))
                }
            } else {
                if err := chestInv.SetItem(OutputSlot, item.Stack{}); err != nil {
                    pl.Message(text.Colourf("<red>✗ Inventory error: %v", err))
                }
            }

        }

        // Enchant function with centralized XP check and error handling
        enchant := func() {
            it1, _ := chestInv.Item(InputSlot1)
            it2, _ := chestInv.Item(InputSlot2)

            out, ok := mergeEnchantments([]item.Stack{it1, it2})
            if !ok {
                pl.PlaySound(sound.Deny{})
                pl.Message(text.Colourf("<red>✗ Items are not compatible for enchanting!"))
                return
            }
            if pl.ExperienceLevel() < 50 {
                pl.PlaySound(sound.Deny{})
                pl.Message(text.Colourf("<red>✗ You need 50 XP levels to enchant! (Current: %d)", pl.ExperienceLevel()))
                return
            }

            // Deduct XP and update inventory
            pl.SetExperienceLevel(pl.ExperienceLevel() - 50)
            pl.PlaySound(sound.LevelUp{})
            if err := chestInv.SetItem(InputSlot1, it1.Grow(-1)); err != nil {
                pl.Message(text.Colourf("<red>✗ Inventory error: %v", err))
                return
            }
            if err := chestInv.SetItem(InputSlot2, it2.Grow(-1)); err != nil {
                pl.Message(text.Colourf("<red>✗ Inventory error: %v", err))
                return
            }
            if err := chestInv.SetItem(OutputSlot, item.Stack{}); err != nil {
                pl.Message(text.Colourf("<red>✗ Inventory error: %v", err))
                return
            }
            if _, err := pl.Inventory().AddItem(*out); err != nil {
                pl.Message(text.Colourf("<red>✗ Failed to add enchanted item to inventory: %v", err))
                return
            }
            updateUI()
            pl.Message(text.Colourf("<green>✦ Enchantment successful! ✦"))
        }

        chestInv.Handle(ui.ChestUIHandler{
            Inventory: chestInv,
            Functions: []func(ctx *event.Context[inventory.Holder], slot int, stack item.Stack, inv *inventory.Inventory){
                // Take handler
                func(ctx *event.Context[inventory.Holder], slot int, stack item.Stack, inv *inventory.Inventory) {
                    if stackValue, ok := stack.Value("enchanter"); ok {
                        v := stackValue.(string)
                        if v == "glass" || v == "info" {
                            ctx.Cancel()
                        } else if sufficient, ok := stack.Value("sufficient"); ok && v == "anvil" && sufficient.(bool) {
                            ctx.Cancel()
                            enchant()
                        }
                    } else if slot == OutputSlot {
                        ctx.Cancel()
                        enchant()
                    } else if slot == InputSlot1 || slot == InputSlot2 {
                        updateUI()
                    }
                },
                // Place handler
                func(ctx *event.Context[inventory.Holder], slot int, stack item.Stack, inv *inventory.Inventory) {
                    if slot == InputSlot1 || slot == InputSlot2 {
                        updateUI()
                        pl.PlaySound(sound.Click{})
                    } else {
                        ctx.Cancel()
                    }
                },
                // Drop handler
                func(ctx *event.Context[inventory.Holder], slot int, stack item.Stack, inv *inventory.Inventory) {
                    ctx.Cancel()
                },
            },
        })

        // Initialize UI and send to player
        setupEnhancedUI(pl, chestInv)
        updateUI()
        inv.SendMenu(pl, menu)
    } else {
        o.Error(text.Colourf("<red>You cannot use this command in console. Please execute it in-game.</red>"))
    }
}

// setupEnhancedUI initializes the enchanting interface with decorative slots
func setupEnhancedUI(pl *player.Player, inv *inventory.Inventory) {
    // Fill all slots with decorative glass
    for i := 0; i < 27; i++ {
        if i == InputSlot1 || i == InputSlot2 || i == OutputSlot || i == AnvilSlot || i == InfoSlot {
            continue // Skip functional slots
        }

        var glassColor item.Colour
        if i < 9 {
            glassColor = item.ColourLightBlue() // Top row - info area
        } else if i >= 18 {
            glassColor = item.ColourGreen() // Bottom row - action area
        } else {
            glassColor = item.ColourPurple() // Middle row - main area
        }

        if err := inv.SetItem(i, item.NewStack(block.StainedGlassPane{Colour: glassColor}, 1).
            WithCustomName(" ").
            WithValue("enchanter", "glass")); err != nil {
            pl.Message(text.Colourf("<red>✗ Inventory setup error: %v", err))
        }
    }

    // Add input slot indicators
    if err := inv.SetItem(InputSlot1-9, item.NewStack(block.StainedGlassPane{Colour: item.ColourYellow()}, 1).
        WithCustomName(text.Colourf("<yellow>⬇ Place First Item ⬇")).
        WithValue("enchanter", "glass")); err != nil {
        pl.Message(text.Colourf("<red>✗ Inventory setup error: %v", err))
    }

    if err := inv.SetItem(InputSlot2-9, item.NewStack(block.StainedGlassPane{Colour: item.ColourYellow()}, 1).
        WithCustomName(text.Colourf("<yellow>⬇ Place Second Item ⬇")).
        WithValue("enchanter", "glass")); err != nil {
        pl.Message(text.Colourf("<red>✗ Inventory setup error: %v", err))
    }

    // Add output slot indicator
    if err := inv.SetItem(OutputSlot-9, item.NewStack(block.StainedGlassPane{Colour: item.ColourLime()}, 1).
        WithCustomName(text.Colourf("<green>⬇ Enchanted Result ⬇")).
        WithValue("enchanter", "glass")); err != nil {
        pl.Message(text.Colourf("<red>✗ Inventory setup error: %v", err))
    }
}

// updateInfoSlot updates the information display at the top
func updateInfoSlot(pl *player.Player, inv *inventory.Inventory, it1, it2 item.Stack) {
    var infoText string
    var infoColor string = "<aqua>"

    if it1.Count() == 0 && it2.Count() == 0 {
        infoText = "Place two items to combine their enchantments"
        infoColor = "<grey>"
    } else if it1.Count() == 0 || it2.Count() == 0 {
        infoText = "Add a second item to continue"
        infoColor = "<yellow>"
    } else if _, ok := mergeEnchantments([]item.Stack{it1, it2}); ok {
        infoText = "Items are compatible! Ready to enchant"
        infoColor = "<green>"
    } else {
        infoText = "Items are not compatible for merging"
        infoColor = "<red>"
    }

    infoItem := item.NewStack(block.Sign{Wood: block.OakWood()}, 1).
        WithCustomName(text.Colourf("%s✦ %s ✦", infoColor, infoText)).
        WithLore(text.Colourf(
            "<dark-grey>• Items must be the same type and tier</dark-grey>\n"+
            "<dark-grey>• Enchantments will be combined</dark-grey>\n"+
            "<dark-grey>• Requires 50 XP levels</dark-grey>")).
        WithValue("enchanter", "info")

    if err := inv.SetItem(InfoSlot, infoItem); err != nil {
        pl.Message(text.Colourf("<red>✗ Inventory update error: %v", err))
    }
}

// updateAnvilSlot updates the anvil button based on current state
func updateAnvilSlot(pl *player.Player, inv *inventory.Inventory) {
    anvil := item.NewStack(block.Anvil{Type: block.UndamagedAnvil()}, 1).WithValue("enchanter", "anvil")

    it1, _ := inv.Item(InputSlot1)
    it2, _ := inv.Item(InputSlot2)
    hasValidItems := it1.Count() > 0 && it2.Count() > 0
    _, canMerge := mergeEnchantments([]item.Stack{it1, it2})
    hasEnoughXP := pl.ExperienceLevel() >= 50

    if hasValidItems && canMerge && hasEnoughXP {
        anvil = anvil.
            WithCustomName(text.Colourf("<dark-green>✦ Click to Enchant ✦")).
            WithLore(text.Colourf(
                "<green>✓ Items are compatible</green>\n"+
                "<green>✓ You have enough XP (%d/50)</green>\n"+
                "<yellow>Click to combine enchantments!</yellow>", pl.ExperienceLevel())).
            WithValue("sufficient", true)
    } else {
        var issueText string
        if !hasValidItems {
            issueText += "<red>✗ Need two items</red>\n"
        } else if !canMerge {
            issueText += "<red>✗ Items not compatible</red>\n"
        }
        if !hasEnoughXP {
            issueText += text.Colourf("<red>✗ Need 50 XP (have %d)</red>", pl.ExperienceLevel())
        }

        anvil = anvil.
            WithCustomName(text.Colourf("<red>✗ Cannot Enchant ✗")).
            WithLore(text.Colourf(issueText)).
            WithValue("sufficient", false)
    }

    if err := inv.SetItem(AnvilSlot, anvil); err != nil {
        pl.Message(text.Colourf("<red>✗ Inventory update error: %v", err))
    }
}

// mergeEnchantments combines enchantments from input items
func mergeEnchantments(its []item.Stack) (*item.Stack, bool) {
    var toolType *item.ToolType
    toolTier := -1
    mergedEnchantments := make(map[item.EnchantmentType]int)

    for _, it := range its {
        if it.Count() == 0 {
            continue // Skip empty stacks
        }

        if _, isEbook := it.Item().(item.EnchantedBook); !isEbook {
            if t, ok := it.Item().(item.Tool); ok {
                if toolType == nil {
                    temp := t.ToolType()
                    toolType = &temp
                } else if t.ToolType() != *toolType {
                    return nil, false
                }
            }

            if toolTier == -1 {
                toolTier = TierId(it.Item())
            } else if TierId(it.Item()) != toolTier {
                return nil, false
            }
        }

        for _, enchant := range it.Enchantments() {
            if _, ok := enchant.Type().(enchants.Glitter); ok {
                return nil, false // Explicitly reject Glitter enchantments
            }
            if mergedEnchantments[enchant.Type()] != 0 {
                if mergedEnchantments[enchant.Type()] == enchant.Level() {
                    mergedEnchantments[enchant.Type()] = enchant.Level() + 1
                } else {
                    return nil, false // Incompatible enchantment levels
                }
            } else {
                mergedEnchantments[enchant.Type()] = enchant.Level()
            }
        }
    }

    var outputIt item.Stack
    if itOutput, ok := ToolTypeToItem(toolType, toolTier); ok {
        outputIt = item.NewStack(itOutput, 1)
    } else {
        return nil, false
    }

    for t, lvl := range mergedEnchantments {
        for _, it := range its {
            if _, ok := it.Item().(item.EnchantedBook); !ok && !t.CompatibleWithItem(it.Item()) {
                return nil, false
            }
        }
        outputIt = blocks.AddEnchantmentLore(outputIt.WithEnchantments(item.NewEnchantment(t, lvl)))
    }

    if len(mergedEnchantments) > 0 {
        return &outputIt, true
    }
    return nil, false
}

// TierId returns the tier ID of an item
func TierId(it world.Item) int {
    if _, ok := it.(item.Bow); ok {
        return -2
    }

    if i, ok := it.(item.Sword); ok {
        return slices.Index(item.ToolTiers(), i.Tier)
    }

    if i, ok := it.(item.Axe); ok {
        return slices.Index(item.ToolTiers(), i.Tier)
    }

    if i, ok := it.(item.Pickaxe); ok {
        return slices.Index(item.ToolTiers(), i.Tier)
    }

    if i, ok := it.(item.Shovel); ok {
        return slices.Index(item.ToolTiers(), i.Tier)
    }

    if i, ok := it.(item.Hoe); ok {
        return slices.Index(item.ToolTiers(), i.Tier)
    }

    return -1
}

// ToolTypeToItem converts a tool type and tier to an item
func ToolTypeToItem(toolType *item.ToolType, tierId int) (world.Item, bool) {
    if toolType == nil {
        if tierId == -2 {
            return item.Bow{}, true
        }
        return item.EnchantedBook{}, true // Allow enchanted books
    }

    if tierId == -1 {
        return item.EnchantedBook{}, true
    }

    tid := item.ToolTiers()[tierId]

    if *toolType == item.TypeSword {
        return item.Sword{Tier: tid}, true
    } else if *toolType == item.TypeAxe {
        return item.Axe{Tier: tid}, true
    } else if *toolType == item.TypePickaxe {
        return item.Pickaxe{Tier: tid}, true
    } else if *toolType == item.TypeShovel {
        return item.Shovel{Tier: tid}, true
    } else if *toolType == item.TypeHoe {
        return item.Hoe{Tier: tid}, true
    }

    return item.Bow{}, false
}