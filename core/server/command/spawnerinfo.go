package command

import (
	"server/server/factions/spawner"

	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/sandertv/gophertunnel/minecraft/text"
)

// SpawnerInfoCommand provides information about spawner system performance
type SpawnerInfoCommand struct{}

// Run executes the spawnerinfo command
func (c SpawnerInfoCommand) Run(src cmd.Source, o *cmd.Output, tx *world.Tx) {
	pl, ok := src.(*player.Player)
	if !ok {
		o.Error("This command can only be used by players")
		return
	}

	activeSpawners, totalTicks, totalSpawns := spawner.GetPerformanceStats()
	maxRecommended := spawner.GetRecommendedMaxSpawners()
	isSafe := spawner.IsSpawnerCountSafe(int(activeSpawners))

	// Performance status
	var statusColor, statusText string
	if isSafe {
		statusColor = "green"
		statusText = "OPTIMAL"
	} else if activeSpawners <= int64(float64(maxRecommended)*1.5) {
		statusColor = "yellow"
		statusText = "WARNING"
	} else {
		statusColor = "red"
		statusText = "CRITICAL"
	}

	pl.Message(text.Colourf("<bold><blue>===== Spawner Performance Info =====</blue></bold>"))
	pl.Message(text.Colourf("<white>Active Spawners: <yellow>%d</yellow></white>", activeSpawners))
	pl.Message(text.Colourf("<white>Recommended Max: <yellow>%d</yellow></white>", maxRecommended))
	pl.Message(text.Colourf("<white>Performance Status: <%s><bold>%s</bold></%s></white>", statusColor, statusText, statusColor))
	pl.Message(text.Colourf("<white>Total Ticks (last 30s): <yellow>%d</yellow></white>", totalTicks))
	pl.Message(text.Colourf("<white>Total Spawns (last 30s): <yellow>%d</yellow></white>", totalSpawns))

	// Performance recommendations
	if !isSafe {
		pl.Message(text.Colourf("<red><bold>⚠ Performance Warning:</bold></red>"))
		if activeSpawners > int64(maxRecommended) {
			pl.Message(text.Colourf("<red>• Too many spawners may cause server lag</red>"))
			pl.Message(text.Colourf("<red>• Consider reducing spawner count to %d or fewer</red>", maxRecommended))
		}
		
		if totalTicks > 1000 {
			pl.Message(text.Colourf("<red>• High tick rate detected - spawners are working overtime</red>"))
		}
	} else {
		pl.Message(text.Colourf("<green>✓ Spawner performance is within optimal range</green>"))
	}

	// Usage efficiency
	if activeSpawners > 0 && totalTicks > 0 {
		efficiency := float64(totalSpawns) / float64(totalTicks) * 100
		pl.Message(text.Colourf("<white>Spawn Efficiency: <yellow>%.1f%%</yellow></white>", efficiency))
	}

	pl.Message(text.Colourf("<bold><blue>=====================================</blue></bold>"))
}

// Allow allows the command to be executed by players with appropriate permissions
func (c SpawnerInfoCommand) Allow(src cmd.Source) bool {
	if pl, ok := src.(*player.Player); ok {
		// Allow all players to check spawner performance
		// You can add permission checks here if needed
		_ = pl
		return true
	}
	return false
}
