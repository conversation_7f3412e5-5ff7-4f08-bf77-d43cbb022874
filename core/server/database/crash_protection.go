package database

import (
	"context"
	"fmt"
	"log/slog"
	"math/rand"
	"os"
	"os/signal"
	"runtime"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"github.com/df-mc/dragonfly/server"
	"github.com/df-mc/dragonfly/server/player"
)

// CrashProtection handles emergency saves during crashes and deadlocks
type CrashProtection struct {
	emergencySaveInProgress int32
	lastEmergencySave       time.Time
	mu                      sync.RWMutex
	shutdownChan           chan struct{}
	emergencyTimeout       time.Duration
	startupTime            time.Time
}

var (
	CrashProtectionInstance *CrashProtection
	emergencySaveMutex      sync.Mutex
	ServerInstance          *server.Server // Reference to the server for accessing online players
	DragonflyConfigInstance *server.Config // Reference to the dragonfly config
)

// randString generates a random string of specified length (local implementation to avoid import cycles)
func randString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// InitCrashProtection initializes the crash protection system
func InitCrashProtection() {
	CrashProtectionInstance = &CrashProtection{
		shutdownChan:     make(chan struct{}),
		emergencyTimeout: 10 * time.Second, // Fast emergency saves
		startupTime:      time.Now(),
	}

	// Set up signal handlers
	CrashProtectionInstance.setupSignalHandlers()

	// Set up panic recovery
	CrashProtectionInstance.setupPanicRecovery()

	// Start deadlock detection
	go CrashProtectionInstance.deadlockDetector()

	slog.Default().Info("Crash protection system initialized")
}

// setupSignalHandlers configures signal handlers for graceful emergency shutdown
func (cp *CrashProtection) setupSignalHandlers() {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, 
		syscall.SIGINT,  // Ctrl+C
		syscall.SIGTERM, // Termination request
		syscall.SIGQUIT, // Quit signal
		syscall.SIGHUP,  // Hangup
	)

	go func() {
		sig := <-sigChan
		slog.Default().Error("Received signal, performing emergency save", "signal", sig.String())
		
		// Perform emergency save
		if err := cp.EmergencySave("signal_" + sig.String()); err != nil {
			slog.Default().Error("Emergency save failed", "error", err, "signal", sig.String())
		}

		// Exit after emergency save
		os.Exit(1)
	}()
}

// setupPanicRecovery sets up global panic recovery
func (cp *CrashProtection) setupPanicRecovery() {
	// This will be called by modified panic handlers
	runtime.SetFinalizer(cp, func(cp *CrashProtection) {
		if r := recover(); r != nil {
			slog.Default().Error("Panic detected, performing emergency save", "panic", r)
			
			if err := cp.EmergencySave("panic"); err != nil {
				slog.Default().Error("Emergency save during panic failed", "error", err)
			}
			
			// Re-panic to maintain original behavior
			panic(r)
		}
	})
}

// EmergencySave performs a fast, synchronous save bypassing the queue system
func (cp *CrashProtection) EmergencySave(reason string) error {
	// Prevent multiple emergency saves from running simultaneously
	if !atomic.CompareAndSwapInt32(&cp.emergencySaveInProgress, 0, 1) {
		return fmt.Errorf("emergency save already in progress")
	}
	defer atomic.StoreInt32(&cp.emergencySaveInProgress, 0)

	emergencySaveMutex.Lock()
	defer emergencySaveMutex.Unlock()

	start := time.Now()
	slog.Default().Warn("Starting emergency save", "reason", reason)

	// Create emergency context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), cp.emergencyTimeout)
	defer cancel()

	var errors []error

	// 1. Save all online players immediately (most critical)
	if err := cp.emergencySavePlayers(ctx); err != nil {
		errors = append(errors, fmt.Errorf("emergency player save failed: %w", err))
	}

	// 2. Save all cached data immediately
	if err := cp.emergencySaveCachedData(ctx); err != nil {
		errors = append(errors, fmt.Errorf("emergency cached data save failed: %w", err))
	}

	// 3. Force database sync if MongoDB
	if mongoDb, ok := DB.(*MongoDBDatabase); ok {
		if err := cp.emergencyDatabaseSync(ctx, mongoDb); err != nil {
			errors = append(errors, fmt.Errorf("emergency database sync failed: %w", err))
		}
	}

	duration := time.Since(start)
	cp.lastEmergencySave = time.Now()

	if len(errors) > 0 {
		slog.Default().Error("Emergency save completed with errors", 
			"reason", reason, 
			"duration", duration, 
			"errors", len(errors))
		for _, err := range errors {
			slog.Default().Error("Emergency save error", "error", err)
		}
		return fmt.Errorf("emergency save had %d errors", len(errors))
	}

	slog.Default().Warn("Emergency save completed successfully", 
		"reason", reason, 
		"duration", duration)
	return nil
}

// emergencySavePlayers saves all online players immediately
func (cp *CrashProtection) emergencySavePlayers(ctx context.Context) error {
	// Try to get online players from the server if available
	if ServerInstance != nil {
		var players []*player.Player

		// Collect all online players
		for pl := range ServerInstance.Players(nil) {
			players = append(players, pl)
		}

		if len(players) == 0 {
			return nil
		}

		slog.Default().Info("Emergency saving online players", "count", len(players))

		// Save each player with emergency procedure
		var errors []error
		for _, pl := range players {
			if err := cp.emergencySavePlayer(pl); err != nil {
				errors = append(errors, err)
			}
		}

		if len(errors) > 0 {
			slog.Default().Error("Emergency player saves had errors", "error_count", len(errors))
			// Return first error for context
			return errors[0]
		}

		return nil
	}

	// Fallback: save all cached player data if server instance not available
	if localDb, ok := DB.(*MongoDBDatabase); ok && localDb.cache != nil {
		localDb.cache.playerMu.RLock()
		playerCount := len(localDb.cache.playerMap)
		localDb.cache.playerMu.RUnlock()

		if playerCount == 0 {
			return nil
		}

		slog.Default().Info("Emergency saving cached players", "count", playerCount)

		// Save each player individually for maximum reliability
		localDb.cache.playerMu.RLock()
		defer localDb.cache.playerMu.RUnlock()

		for uuid, playerData := range localDb.cache.playerMap {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
				if err := DB.SavePlayer(playerData); err != nil {
					slog.Default().Error("Emergency cached player save failed",
						"uuid", uuid.String(),
						"username", playerData.Username,
						"error", err)
				}
			}
		}
	}

	return nil
}

// emergencySavePlayer saves a single player's data immediately
func (cp *CrashProtection) emergencySavePlayer(pl *player.Player) error {
	if pl == nil {
		return fmt.Errorf("player is nil")
	}

	// This is a simplified version that doesn't depend on the user package
	// to avoid circular imports. We'll save the player data directly.

	// Save Dragonfly player data if we have access to the config
	if DragonflyConfigInstance != nil && ServerInstance != nil {
		if err := DragonflyConfigInstance.PlayerProvider.Save(pl.UUID(), pl.Data(), ServerInstance.World()); err != nil {
			slog.Default().Error("Emergency save of Dragonfly player data failed",
				"player", pl.Name(),
				"uuid", pl.UUID().String(),
				"error", err)
			return fmt.Errorf("dragonfly save failed: %w", err)
		}
	}

	// Save custom player data from cache if available
	if mongoDb, ok := DB.(*MongoDBDatabase); ok && mongoDb.cache != nil {
		mongoDb.cache.playerMu.RLock()
		playerData, exists := mongoDb.cache.playerMap[pl.UUID()]
		mongoDb.cache.playerMu.RUnlock()

		if exists {
			if err := DB.SavePlayer(playerData); err != nil {
				slog.Default().Error("Emergency save of custom player data failed",
					"player", pl.Name(),
					"uuid", pl.UUID().String(),
					"error", err)
				return fmt.Errorf("custom data save failed: %w", err)
			}
		}
	}

	return nil
}

// emergencySaveCachedData saves all cached faction and other data
func (cp *CrashProtection) emergencySaveCachedData(ctx context.Context) error {
	if localDb, ok := DB.(*MongoDBDatabase); ok && localDb.cache != nil {
		// Save factions
		localDb.cache.factionMu.RLock()
		factionCount := len(localDb.cache.factionMap)
		localDb.cache.factionMu.RUnlock()

		if factionCount > 0 {
			slog.Default().Info("Emergency saving factions", "count", factionCount)

			localDb.cache.factionMu.RLock()
			defer localDb.cache.factionMu.RUnlock()

			for name, factionData := range localDb.cache.factionMap {
				select {
				case <-ctx.Done():
					return ctx.Err()
				default:
					if err := DB.SaveFaction(factionData); err != nil {
						slog.Default().Error("Emergency faction save failed", 
							"name", name, 
							"error", err)
					}
				}
			}
		}

		// Save jackpot data
		localDb.cache.jackpotMu.RLock()
		jackpotData := localDb.cache.jackpotData
		localDb.cache.jackpotMu.RUnlock()

		if jackpotData != nil {
			if err := DB.SaveJackpot(jackpotData); err != nil {
				slog.Default().Error("Emergency jackpot save failed", "error", err)
			}
		}
	}

	return nil
}

// emergencyDatabaseSync forces database synchronization
func (cp *CrashProtection) emergencyDatabaseSync(ctx context.Context, mongoDb *MongoDBDatabase) error {
	// Force MongoDB to sync to disk
	adminDb := mongoDb.client.Database("admin")
	
	// Run fsync command to force write to disk
	result := adminDb.RunCommand(ctx, map[string]interface{}{
		"fsync": 1,
		"lock":  false, // Don't lock the database
	})
	
	if result.Err() != nil {
		return fmt.Errorf("database fsync failed: %w", result.Err())
	}

	return nil
}

// deadlockDetector monitors for potential deadlocks and system issues
func (cp *CrashProtection) deadlockDetector() {
	ticker := time.NewTicker(15 * time.Second) // More frequent checks
	defer ticker.Stop()

	var lastGoroutineCount int
	var stuckCount int
	var lastMemStats runtime.MemStats
	var highMemoryCount int

	for {
		select {
		case <-ticker.C:
			cp.performDeadlockCheck(&lastGoroutineCount, &stuckCount, &lastMemStats, &highMemoryCount)

		case <-cp.shutdownChan:
			return
		}
	}
}

// performDeadlockCheck performs comprehensive system health checks
func (cp *CrashProtection) performDeadlockCheck(lastGoroutineCount *int, stuckCount *int, lastMemStats *runtime.MemStats, highMemoryCount *int) {
	currentGoroutines := runtime.NumGoroutine()

	// 1. Check for goroutine leaks (only after startup grace period)
	startupGracePeriod := 2 * time.Minute
	isStartupPhase := time.Since(cp.startupTime) < startupGracePeriod

	if !isStartupPhase && *lastGoroutineCount > 0 && currentGoroutines > *lastGoroutineCount+100 {
		slog.Default().Warn("Potential goroutine leak detected",
			"current", currentGoroutines,
			"previous", *lastGoroutineCount,
			"increase", currentGoroutines-*lastGoroutineCount)

		// If goroutines are growing very rapidly, trigger emergency save
		if currentGoroutines > *lastGoroutineCount+200 {
			slog.Default().Error("Severe goroutine leak detected, triggering emergency save")
			go cp.EmergencySave("goroutine_leak")
		}
	} else if isStartupPhase && currentGoroutines > 200 {
		// During startup, only warn if goroutine count is extremely high
		slog.Default().Debug("High goroutine count during startup (normal)",
			"current", currentGoroutines,
			"startup_phase", true)
	}

	// 2. Check memory usage
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	// Check if memory usage is growing rapidly (more than 100MB increase)
	if memStats.Alloc > lastMemStats.Alloc+100*1024*1024 {
		*highMemoryCount++
		slog.Default().Warn("High memory usage detected",
			"current_mb", memStats.Alloc/(1024*1024),
			"previous_mb", lastMemStats.Alloc/(1024*1024),
			"high_memory_count", *highMemoryCount)

		// If memory keeps growing, trigger emergency save and GC
		if *highMemoryCount >= 3 {
			slog.Default().Error("Memory leak detected, triggering emergency save and GC")
			runtime.GC() // Force garbage collection
			go cp.EmergencySave("memory_leak")
			*highMemoryCount = 0
		}
	} else {
		*highMemoryCount = 0 // Reset if memory is stable
	}

	// 3. Check save queue health
	if SaveQueueInstance != nil {
		metrics := SaveQueueInstance.GetMetrics()

		// Multiple conditions for detecting stuck queue
		queueStuck := false

		// Condition 1: High queue depth with no recent saves
		if metrics.QueueDepth > 100 && time.Since(metrics.LastSaveTime) > time.Minute {
			queueStuck = true
		}

		// Condition 2: Queue depth growing rapidly
		if metrics.QueueDepth > 5000 {
			queueStuck = true
		}

		// Condition 3: Very low success rate
		if metrics.TotalSaves > 100 && metrics.SuccessfulSaves > 0 {
			successRate := float64(metrics.SuccessfulSaves) / float64(metrics.TotalSaves) * 100
			if successRate < 50 {
				queueStuck = true
			}
		}

		if queueStuck {
			*stuckCount++
			slog.Default().Warn("Save queue health check failed",
				"queue_depth", metrics.QueueDepth,
				"last_save", metrics.LastSaveTime,
				"total_saves", metrics.TotalSaves,
				"successful_saves", metrics.SuccessfulSaves,
				"stuck_count", *stuckCount)

			// Progressive response to stuck queue
			if *stuckCount >= 2 {
				slog.Default().Error("Save queue deadlock detected, attempting recovery")
				go cp.recoverFromDeadlock()
				*stuckCount = 0 // Reset counter
			}
		} else {
			*stuckCount = 0 // Reset if queue is working
		}
	}

	// 4. Check checkpoint system health
	if CheckpointSystemInstance != nil {
		checkpointStats := CheckpointSystemInstance.GetCheckpointStats()
		if running, ok := checkpointStats["running"].(bool); ok && !running {
			slog.Default().Error("Checkpoint system is not running, attempting restart")
			CheckpointSystemInstance.Start()
		}

		if lastCheckpoint, ok := checkpointStats["last_checkpoint_time"].(time.Time); ok {
			if time.Since(lastCheckpoint) > 2*time.Minute {
				slog.Default().Warn("Checkpoint system appears stuck",
					"last_checkpoint", lastCheckpoint,
					"time_since", time.Since(lastCheckpoint))
			}
		}
	}

	*lastGoroutineCount = currentGoroutines
	*lastMemStats = memStats
}

// recoverFromDeadlock attempts to recover from a detected deadlock
func (cp *CrashProtection) recoverFromDeadlock() {
	slog.Default().Error("Attempting deadlock recovery")

	// 1. Force garbage collection
	runtime.GC()

	// 2. Try to restart the save queue if possible
	if SaveQueueInstance != nil {
		slog.Default().Info("Attempting to restart save queue")
		// Note: This would require implementing a restart method in SaveQueue
		// For now, we'll trigger an emergency save
	}

	// 3. Perform emergency save
	if err := cp.EmergencySave("deadlock_recovery"); err != nil {
		slog.Default().Error("Emergency save during deadlock recovery failed", "error", err)
	}

	// 4. Force checkpoint
	if CheckpointSystemInstance != nil {
		if err := CheckpointSystemInstance.ForceCheckpoint("deadlock_recovery"); err != nil {
			slog.Default().Error("Force checkpoint during deadlock recovery failed", "error", err)
		}
	}

	slog.Default().Info("Deadlock recovery attempt completed")
}

// GetEmergencySaveStatus returns information about the last emergency save
func (cp *CrashProtection) GetEmergencySaveStatus() map[string]interface{} {
	cp.mu.RLock()
	defer cp.mu.RUnlock()

	return map[string]interface{}{
		"emergency_save_in_progress": atomic.LoadInt32(&cp.emergencySaveInProgress) == 1,
		"last_emergency_save":        cp.lastEmergencySave,
		"emergency_timeout":          cp.emergencyTimeout,
	}
}

// Shutdown stops the crash protection system
func (cp *CrashProtection) Shutdown() {
	close(cp.shutdownChan)
}
