package database

import (
	"github.com/df-mc/dragonfly/server/item"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/google/uuid"
	"time"
)

type PlayerData struct {
	UUID     uuid.UUID
	Username string

	Online     bool
	FirstLogin time.Time
	LastLogin  time.Time
	ProtocolId string
	RankId     string


		// XP snapshot for redundancy (Dragonfly also persists XP). Used to restore if provider fails.
		XPLevel    int     `bson:"xp_level,omitempty"`
		XPProgress float64 `bson:"xp_progress,omitempty"`

	Faction PlayerFaction

	// Inventory safety snapshot (one-time recovery on next join if inventory is unexpectedly empty)
	InventorySnapshot []CustomStack `bson:"inventory_snapshot,omitempty"`
	ArmourSnapshot    []CustomStack `bson:"armour_snapshot,omitempty"`
	SnapshotAt        time.Time     `bson:"snapshot_at,omitempty"`
}

func (d *PlayerData) Rank() Rank {
	return RankFromName(d.RankId)
}

type PlayerFaction struct {
	Stats    Stats
	Home     map[string]mgl64.Vec3
	Backpack map[int]map[int]CustomStack
	Bounties map[uuid.UUID]float64
	Request  *Request

	Name        string
	Role        Role
	FirstJoined time.Time
}

func (f PlayerFaction) HasFaction() bool {
	return f.Name != ""
}

func (f PlayerFaction) Faction() *FactionData {
	if f.HasFaction() {
		faction, err := DB.FindFaction(f.Name)
		if err != nil {
			panic(err) // Maintain the same behavior as utils.Panics
		}
		return faction
	}
	return nil
}

func (f PlayerFaction) DeserializedBackpack() map[int]map[int]item.Stack {
	var dst map[int]map[int]item.Stack
	for page, m := range f.Backpack {
		for slot, cStack := range m {
			dst[page][slot] = cStack.Stack()
		}
	}
	return dst
}

func (f PlayerFaction) TotalBounties() (sum float64) {
	for _, b := range f.Bounties {
		sum += b
	}
	return sum
}

type Stats struct {
	Doubloons      float64
	Strength       float64
	Kills          int
	KillStreak     int
	BestKillStreak int
	Deaths         int
	CrateKeys      map[CrateType]int
	Kits           map[Rank]time.Time
}

func (s Stats) KDR() float64 {
	if s.Deaths == 0 {
		return 0
	}
	return float64(s.Kills) / float64(s.Deaths)
}
