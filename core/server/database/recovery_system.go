package database

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
)

// RecoverySystem handles detection and recovery from incomplete saves
type RecoverySystem struct {
	recoveryDir       string
	saveStateFile     string
	mu                sync.RWMutex
	activeSaves       map[string]*SaveState
	lastRecoveryCheck time.Time
}

// SaveState tracks the state of ongoing save operations
type SaveState struct {
	ID        string    `json:"id"`
	Type      string    `json:"type"`
	PlayerID  string    `json:"player_id,omitempty"`
	FactionID string    `json:"faction_id,omitempty"`
	StartTime time.Time `json:"start_time"`
	Status    string    `json:"status"` // "started", "completed", "failed"
	Checksum  string    `json:"checksum,omitempty"`
}

var RecoverySystemInstance *RecoverySystem

// InitRecoverySystem initializes the save state recovery system
func InitRecoverySystem() error {
	recoveryDir := filepath.Join(".", "recovery")
	if err := os.Mkdir<PERSON>ll(recoveryDir, 0755); err != nil {
		return fmt.Errorf("failed to create recovery directory: %w", err)
	}

	RecoverySystemInstance = &RecoverySystem{
		recoveryDir:   recoveryDir,
		saveStateFile: filepath.Join(recoveryDir, "save_states.json"),
		activeSaves:   make(map[string]*SaveState),
	}

	// Check for incomplete saves from previous session
	if err := RecoverySystemInstance.checkForIncompleteStates(); err != nil {
		slog.Default().Error("Failed to check for incomplete save states", "error", err)
	}

	slog.Default().Info("Recovery system initialized", "recovery_dir", recoveryDir)
	return nil
}

// checkForIncompleteStates checks for and recovers from incomplete saves
func (rs *RecoverySystem) checkForIncompleteStates() error {
	rs.mu.Lock()
	defer rs.mu.Unlock()

	// Load previous save states
	if err := rs.loadSaveStates(); err != nil {
		slog.Default().Warn("Could not load previous save states", "error", err)
		return nil // Not critical, continue
	}

	incompleteCount := 0
	recoveredCount := 0

	for id, state := range rs.activeSaves {
		if state.Status != "completed" {
			incompleteCount++
			slog.Default().Warn("Found incomplete save state", 
				"id", id,
				"type", state.Type,
				"start_time", state.StartTime,
				"status", state.Status)

			// Attempt recovery based on save type
			if err := rs.recoverSaveState(state); err != nil {
				slog.Default().Error("Failed to recover save state", 
					"id", id, 
					"error", err)
			} else {
				recoveredCount++
				state.Status = "recovered"
			}
		}
	}

	if incompleteCount > 0 {
		slog.Default().Warn("Recovery check completed", 
			"incomplete_saves", incompleteCount,
			"recovered_saves", recoveredCount)
	}

	// Clear old states and save current state
	rs.activeSaves = make(map[string]*SaveState)
	rs.lastRecoveryCheck = time.Now()
	return rs.saveSaveStates()
}

// recoverSaveState attempts to recover a specific incomplete save
func (rs *RecoverySystem) recoverSaveState(state *SaveState) error {
	switch state.Type {
	case "player":
		return rs.recoverPlayerSave(state)
	case "faction":
		return rs.recoverFactionSave(state)
	case "jackpot":
		return rs.recoverJackpotSave(state)
	case "batch":
		return rs.recoverBatchSave(state)
	default:
		return fmt.Errorf("unknown save type: %s", state.Type)
	}
}

// recoverPlayerSave recovers an incomplete player save
func (rs *RecoverySystem) recoverPlayerSave(state *SaveState) error {
	if state.PlayerID == "" {
		// Best-effort: try to extract UUID from checkpoint op ID format "checkpoint_<uuid>"
		if strings.HasPrefix(state.ID, "checkpoint_") {
			if suffix := strings.TrimPrefix(state.ID, "checkpoint_"); len(suffix) > 0 {
				state.PlayerID = suffix
			}
		}
		if state.PlayerID == "" {
			return fmt.Errorf("player ID not specified in save state")
		}
	}

	playerUUID, err := uuid.Parse(state.PlayerID)
	if err != nil {
		return fmt.Errorf("invalid player UUID: %w", err)
	}

	// Try to find player data in cache
	if mongoDb, ok := DB.(*MongoDBDatabase); ok && mongoDb.cache != nil {
		mongoDb.cache.playerMu.RLock()
		playerData, exists := mongoDb.cache.playerMap[playerUUID]
		mongoDb.cache.playerMu.RUnlock()

		if exists {
			slog.Default().Info("Recovering player save", "player_id", state.PlayerID)
			if err := DB.SavePlayer(playerData); err != nil {
				return fmt.Errorf("failed to recover player save: %w", err)
			}
			slog.Default().Info("Player save recovered successfully", "player_id", state.PlayerID)
			return nil
		}
	}

	// If not in cache, try to load from database and verify integrity
	playerData, err := DB.FindPlayer(playerUUID)
	if err != nil {
		return fmt.Errorf("failed to find player data for recovery: %w", err)
	}

	// Verify data integrity (basic check)
	if playerData.UUID != playerUUID {
		return fmt.Errorf("player data integrity check failed")
	}

	slog.Default().Info("Player data verified during recovery", "player_id", state.PlayerID)
	return nil
}

// recoverFactionSave recovers an incomplete faction save
func (rs *RecoverySystem) recoverFactionSave(state *SaveState) error {
	if state.FactionID == "" {
		return fmt.Errorf("faction ID not specified in save state")
	}

	// Try to find faction data in cache
	if mongoDb, ok := DB.(*MongoDBDatabase); ok && mongoDb.cache != nil {
		mongoDb.cache.factionMu.RLock()
		factionData, exists := mongoDb.cache.factionMap[state.FactionID]
		mongoDb.cache.factionMu.RUnlock()

		if exists {
			slog.Default().Info("Recovering faction save", "faction_id", state.FactionID)
			if err := DB.SaveFaction(factionData); err != nil {
				return fmt.Errorf("failed to recover faction save: %w", err)
			}
			slog.Default().Info("Faction save recovered successfully", "faction_id", state.FactionID)
			return nil
		}
	}

	// Verify faction data exists in database
	_, err := DB.FindFaction(state.FactionID)
	if err != nil {
		return fmt.Errorf("failed to find faction data for recovery: %w", err)
	}

	slog.Default().Info("Faction data verified during recovery", "faction_id", state.FactionID)
	return nil
}

// recoverJackpotSave recovers an incomplete jackpot save
func (rs *RecoverySystem) recoverJackpotSave(state *SaveState) error {
	// Try to find jackpot data in cache
	if mongoDb, ok := DB.(*MongoDBDatabase); ok && mongoDb.cache != nil {
		mongoDb.cache.jackpotMu.RLock()
		jackpotData := mongoDb.cache.jackpotData
		mongoDb.cache.jackpotMu.RUnlock()

		if jackpotData != nil {
			slog.Default().Info("Recovering jackpot save")
			if err := DB.SaveJackpot(jackpotData); err != nil {
				return fmt.Errorf("failed to recover jackpot save: %w", err)
			}
			slog.Default().Info("Jackpot save recovered successfully")
			return nil
		}
	}

	// Verify jackpot data exists in database
	_, err := DB.FindJackpot()
	if err != nil {
		return fmt.Errorf("failed to find jackpot data for recovery: %w", err)
	}

	slog.Default().Info("Jackpot data verified during recovery")
	return nil
}

// recoverBatchSave recovers an incomplete batch save
func (rs *RecoverySystem) recoverBatchSave(state *SaveState) error {
	slog.Default().Info("Recovering batch save")
	
	// Perform a full batch save to ensure all data is saved
	errs := DB.SaveAll()
	if len(errs) > 0 {
		slog.Default().Error("Batch save recovery had errors", "error_count", len(errs))
		// Return first error
		for _, err := range errs {
			return err
		}
	}

	slog.Default().Info("Batch save recovered successfully")
	return nil
}

// TrackSaveStart records the start of a save operation
func (rs *RecoverySystem) TrackSaveStart(saveType, id string, playerID, factionID string) {
	rs.mu.Lock()
	defer rs.mu.Unlock()

	state := &SaveState{
		ID:        id,
		Type:      saveType,
		PlayerID:  playerID,
		FactionID: factionID,
		StartTime: time.Now(),
		Status:    "started",
	}

	rs.activeSaves[id] = state
	
	// Save state to disk periodically (not every time for performance)
	if len(rs.activeSaves)%10 == 0 {
		if err := rs.saveSaveStates(); err != nil {
			slog.Default().Error("Failed to save recovery states", "error", err)
		}
	}
}

// TrackSaveComplete records the completion of a save operation
func (rs *RecoverySystem) TrackSaveComplete(id string) {
	rs.mu.Lock()
	defer rs.mu.Unlock()

	if state, exists := rs.activeSaves[id]; exists {
		state.Status = "completed"
		// Remove completed saves after a short delay to keep file size manageable
		go func() {
			time.Sleep(30 * time.Second)
			rs.mu.Lock()
			delete(rs.activeSaves, id)
			rs.mu.Unlock()
		}()
	}
}

// TrackSaveFailed records the failure of a save operation
func (rs *RecoverySystem) TrackSaveFailed(id string, err error) {
	rs.mu.Lock()
	defer rs.mu.Unlock()

	if state, exists := rs.activeSaves[id]; exists {
		state.Status = "failed"
		slog.Default().Error("Save operation failed", 
			"id", id,
			"type", state.Type,
			"error", err)
	}
}

// loadSaveStates loads save states from disk
func (rs *RecoverySystem) loadSaveStates() error {
	data, err := os.ReadFile(rs.saveStateFile)
	if err != nil {
		if os.IsNotExist(err) {
			return nil // File doesn't exist, that's okay
		}
		return err
	}

	return json.Unmarshal(data, &rs.activeSaves)
}

// saveSaveStates saves current save states to disk
func (rs *RecoverySystem) saveSaveStates() error {
	data, err := json.MarshalIndent(rs.activeSaves, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(rs.saveStateFile, data, 0644)
}

// GetRecoveryStats returns recovery system statistics
func (rs *RecoverySystem) GetRecoveryStats() map[string]interface{} {
	rs.mu.RLock()
	defer rs.mu.RUnlock()

	return map[string]interface{}{
		"active_saves":        len(rs.activeSaves),
		"last_recovery_check": rs.lastRecoveryCheck,
		"recovery_dir":        rs.recoveryDir,
	}
}

// PerformRecoveryCheck manually triggers a recovery check
func (rs *RecoverySystem) PerformRecoveryCheck() error {
	return rs.checkForIncompleteStates()
}
