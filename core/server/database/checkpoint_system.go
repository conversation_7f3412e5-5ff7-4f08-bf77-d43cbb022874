package database

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"sync/atomic"
	"time"

	"github.com/df-mc/dragonfly/server/player"
)

// CheckpointSystem manages frequent automatic saves to minimize data loss
type CheckpointSystem struct {
	running                int32
	stopChan              chan struct{}
	wg                    sync.WaitGroup
	lastCheckpointTime    time.Time
	checkpointInterval    time.Duration
	criticalSaveInterval  time.Duration
	mu                    sync.RWMutex
	
	// Statistics
	totalCheckpoints      int64
	successfulCheckpoints int64
	failedCheckpoints     int64
}

var CheckpointSystemInstance *CheckpointSystem

// InitCheckpointSystem initializes the checkpoint save system
func InitCheckpointSystem(checkpointSeconds, criticalSeconds int) {
	CheckpointSystemInstance = &CheckpointSystem{
		stopChan: make(chan struct{}),
	}

	// Configure intervals with safe defaults
	if checkpointSeconds <= 0 { checkpointSeconds = 30 }
	if criticalSeconds <= 0 { criticalSeconds = 10 }
	CheckpointSystemInstance.checkpointInterval = time.Duration(checkpointSeconds) * time.Second
	CheckpointSystemInstance.criticalSaveInterval = time.Duration(criticalSeconds) * time.Second

	CheckpointSystemInstance.Start()
	slog.Default().Info("Checkpoint system initialized",
		"checkpoint_interval", CheckpointSystemInstance.checkpointInterval,
		"critical_interval", CheckpointSystemInstance.criticalSaveInterval)
}

// Start begins the checkpoint system
func (cs *CheckpointSystem) Start() {
	if !atomic.CompareAndSwapInt32(&cs.running, 0, 1) {
		return // Already running
	}

	// Initialize the last checkpoint time to prevent "stuck" warnings
	cs.mu.Lock()
	cs.lastCheckpointTime = time.Now()
	cs.mu.Unlock()

	// Start checkpoint goroutine
	cs.wg.Add(1)
	go cs.checkpointWorker()

	// Start critical data checkpoint goroutine
	cs.wg.Add(1)
	go cs.criticalDataWorker()

	slog.Default().Info("Checkpoint system started")
}

// Stop gracefully stops the checkpoint system
func (cs *CheckpointSystem) Stop() {
	if !atomic.CompareAndSwapInt32(&cs.running, 1, 0) {
		return // Already stopped
	}

	close(cs.stopChan)
	cs.wg.Wait()
	slog.Default().Info("Checkpoint system stopped")
}

// checkpointWorker performs regular checkpoint saves
func (cs *CheckpointSystem) checkpointWorker() {
	defer cs.wg.Done()
	
	ticker := time.NewTicker(cs.checkpointInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			cs.performCheckpoint("regular")
		case <-cs.stopChan:
			// Perform final checkpoint before stopping
			cs.performCheckpoint("shutdown")
			return
		}
	}
}

// criticalDataWorker performs frequent saves of critical data
func (cs *CheckpointSystem) criticalDataWorker() {
	defer cs.wg.Done()
	
	ticker := time.NewTicker(cs.criticalSaveInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			cs.performCriticalDataSave()
		case <-cs.stopChan:
			return
		}
	}
}

// performCheckpoint executes a checkpoint save
func (cs *CheckpointSystem) performCheckpoint(reason string) {
	start := time.Now()
	atomic.AddInt64(&cs.totalCheckpoints, 1)

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var errors []error

	// Save online players if server is available
	if ServerInstance != nil {
		if err := cs.saveOnlinePlayers(ctx); err != nil {
			errors = append(errors, err)
		}
	}

	// Save cached data
	if err := cs.saveCachedData(ctx); err != nil {
		errors = append(errors, err)
	}

	duration := time.Since(start)
	cs.mu.Lock()
	cs.lastCheckpointTime = time.Now()
	cs.mu.Unlock()

	if len(errors) > 0 {
		atomic.AddInt64(&cs.failedCheckpoints, 1)
		slog.Default().Warn("Checkpoint save completed with errors", 
			"reason", reason,
			"duration", duration,
			"errors", len(errors))
	} else {
		atomic.AddInt64(&cs.successfulCheckpoints, 1)
		slog.Default().Debug("Checkpoint save completed successfully", 
			"reason", reason,
			"duration", duration)
	}
}

// saveOnlinePlayers saves all currently online players
func (cs *CheckpointSystem) saveOnlinePlayers(ctx context.Context) error {
	var players []*player.Player
	
	// Collect online players
	for pl := range ServerInstance.Players(nil) {
		players = append(players, pl)
	}

	if len(players) == 0 {
		return nil
	}

	// Save players with timeout protection
	done := make(chan error, 1)
	go func() {
		var errors []error
		for _, pl := range players {
			// TODO: Add dirty flag check here when import cycle is resolved
			// For now, save all players to maintain data safety
			if err := cs.savePlayerCheckpoint(pl); err != nil {
				errors = append(errors, err)
			}
		}

		if len(errors) > 0 {
			done <- errors[0] // Return first error
		} else {
			done <- nil
		}
	}()

	select {
	case err := <-done:
		return err
	case <-ctx.Done():
		return ctx.Err()
	}
}

// savePlayerCheckpoint saves a single player's data for checkpoint
func (cs *CheckpointSystem) savePlayerCheckpoint(pl *player.Player) error {
	// Save Dragonfly data (vanilla inventory, position, health, etc.)
	if DragonflyConfigInstance.PlayerProvider != nil {
		if err := DragonflyConfigInstance.PlayerProvider.Save(pl.UUID(), pl.Data(), ServerInstance.World()); err != nil {
			slog.Default().Debug("Checkpoint Dragonfly save failed",
				"player", pl.Name(),
				"error", err)
			return err
		}
	}

	// Save custom player data (faction data, crate keys, etc.)
	if SaveQueueInstance != nil {
		var pd *PlayerData

		// First try to get from cache
		if mongoDb, ok := DB.(*MongoDBDatabase); ok && mongoDb.cache != nil {
			mongoDb.cache.playerMu.RLock()
			pd = mongoDb.cache.playerMap[pl.UUID()]
			mongoDb.cache.playerMu.RUnlock()
		}

		// If not in cache, try to find in database
		if pd == nil {
			var err error
			pd, err = DB.FindPlayer(pl.UUID())
			if err != nil {
				slog.Default().Debug("Checkpoint: could not find player data",
					"player", pl.Name(),
					"error", err)
				return nil // Don't fail checkpoint for missing player data
			}
		}

		// Update XP snapshot if we have player data
		if pd != nil {
			pd.XPLevel = pl.ExperienceLevel()
			pd.XPProgress = pl.ExperienceProgress()

			op := &SaveOperation{
				ID:       "checkpoint_" + pl.UUID().String(),
				Type:     SaveTypePlayer,
				Data:     pd,
				Priority: PriorityHigh,
			}

			if err := SaveQueueInstance.QueueSave(op); err != nil {
				// Fallback to direct save if queue is full
				return DB.SavePlayer(pd)
			}
		}
	}

	return nil
}

// saveCachedData saves all cached data
func (cs *CheckpointSystem) saveCachedData(ctx context.Context) error {
	// Queue batch save with normal priority
	if SaveQueueInstance != nil {
		op := &SaveOperation{
			ID:       "checkpoint_batch_" + time.Now().Format("20060102150405"),
			Type:     SaveTypeBatch,
			Data:     nil,
			Priority: PriorityNormal,
		}
		
		return SaveQueueInstance.QueueSave(op)
	}

	// Fallback to direct save
	errs := DB.SaveAll()
	if len(errs) > 0 {
		// Return first error
		for _, err := range errs {
			return err
		}
	}
	
	return nil
}

// performCriticalDataSave saves only the most critical data frequently
func (cs *CheckpointSystem) performCriticalDataSave() {
	// Save jackpot data (critical for economy)
	if mongoDb, ok := DB.(*MongoDBDatabase); ok && mongoDb.cache != nil {
		mongoDb.cache.jackpotMu.RLock()
		jackpotData := mongoDb.cache.jackpotData
		mongoDb.cache.jackpotMu.RUnlock()

		if jackpotData != nil {
			if err := DB.SaveJackpot(jackpotData); err != nil {
				slog.Default().Debug("Critical jackpot save failed", "error", err)
			}
		}
	}

	// Save any players with recent activity (if we can track this)
	// This would require additional tracking of player activity
}

// GetCheckpointStats returns checkpoint system statistics
func (cs *CheckpointSystem) GetCheckpointStats() map[string]interface{} {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	total := atomic.LoadInt64(&cs.totalCheckpoints)
	successful := atomic.LoadInt64(&cs.successfulCheckpoints)
	failed := atomic.LoadInt64(&cs.failedCheckpoints)

	stats := map[string]interface{}{
		"running":                atomic.LoadInt32(&cs.running) == 1,
		"total_checkpoints":      total,
		"successful_checkpoints": successful,
		"failed_checkpoints":     failed,
		"last_checkpoint_time":   cs.lastCheckpointTime,
		"checkpoint_interval":    cs.checkpointInterval,
		"critical_save_interval": cs.criticalSaveInterval,
	}

	if total > 0 {
		stats["success_rate"] = float64(successful) / float64(total) * 100
	}

	return stats
}

// ForceCheckpoint triggers an immediate checkpoint save
func (cs *CheckpointSystem) ForceCheckpoint(reason string) error {
	if atomic.LoadInt32(&cs.running) != 1 {
		return fmt.Errorf("checkpoint system is not running")
	}

	cs.performCheckpoint("forced_" + reason)
	return nil
}
