package events

import (
	"fmt"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"math/rand"
	"sync"
	"time"
)

// BaseEvent provides a default implementation of the Event interface
// Other events can embed this to get basic functionality
type BaseEvent struct {
	id          string
	eventType   EventType
	name        string
	description string
	priority    EventPriority
	status      EventStatus
	config      EventConfig
	state       map[string]interface{}
	startTime   time.Time
	endTime     time.Time
	participants map[string]*EventParticipant
	mutex       sync.RWMutex
}

// generateShortID generates a 4-5 digit event ID
func generateShortID() string {
	// Generate a random 4-5 digit number
	id := rand.Intn(90000) + 10000 // Range: 10000-99999
	return fmt.Sprintf("%d", id)
}

// NewBaseEvent creates a new base event
func NewBaseEvent(eventType EventType, name, description string, priority EventPriority, config EventConfig) *BaseEvent {
	return &BaseEvent{
		id:           generateShortID(),
		eventType:    eventType,
		name:         name,
		description:  description,
		priority:     priority,
		status:       EventStatusInactive,
		config:       config,
		state:        make(map[string]interface{}),
		participants: make(map[string]*EventParticipant),
	}
}

// Basic event information
func (e *BaseEvent) ID() string {
	return e.id
}

func (e *BaseEvent) Type() EventType {
	return e.eventType
}

func (e *BaseEvent) Name() string {
	return e.name
}

func (e *BaseEvent) Description() string {
	return e.description
}

func (e *BaseEvent) Priority() EventPriority {
	return e.priority
}

// Event lifecycle
func (e *BaseEvent) Start() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if e.status == EventStatusActive {
		return fmt.Errorf("event %s is already active", e.id)
	}

	e.status = EventStatusActive
	e.startTime = time.Now()
	if e.config.Duration > 0 {
		e.endTime = e.startTime.Add(e.config.Duration)
	}

	return nil
}

func (e *BaseEvent) Stop() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if e.status == EventStatusInactive || e.status == EventStatusEnded {
		return fmt.Errorf("event %s is not active", e.id)
	}

	e.status = EventStatusEnded
	e.endTime = time.Now()
	return nil
}

func (e *BaseEvent) Pause() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if e.status != EventStatusActive {
		return fmt.Errorf("event %s is not active", e.id)
	}

	e.status = EventStatusPaused
	return nil
}

func (e *BaseEvent) Resume() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if e.status != EventStatusPaused {
		return fmt.Errorf("event %s is not paused", e.id)
	}

	e.status = EventStatusActive
	return nil
}

func (e *BaseEvent) Status() EventStatus {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	return e.status
}

// Event timing
func (e *BaseEvent) Duration() time.Duration {
	return e.config.Duration
}

func (e *BaseEvent) StartTime() time.Time {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	return e.startTime
}

func (e *BaseEvent) EndTime() time.Time {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	return e.endTime
}

func (e *BaseEvent) RemainingTime() time.Duration {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	if e.status != EventStatusActive || e.config.Duration == 0 {
		return 0
	}

	remaining := e.endTime.Sub(time.Now())
	if remaining < 0 {
		return 0
	}
	return remaining
}

// Default event handlers - these can be overridden by specific event implementations
func (e *BaseEvent) OnBlockBreak(ctx *player.Context, pos cube.Pos, block world.Block, drops *[]item.Stack, xp *int) bool {
	// Default implementation does nothing
	return false
}

func (e *BaseEvent) OnBlockPlace(ctx *player.Context, pos cube.Pos, block world.Block) bool {
	// Default implementation does nothing
	return false
}

func (e *BaseEvent) OnPlayerJoin(pl *player.Player) bool {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if e.status != EventStatusActive {
		return false
	}

	// Auto-add player to event if enabled
	if e.config.Enabled {
		participant := &EventParticipant{
			PlayerUUID: pl.UUID().String(),
			PlayerName: pl.Name(),
			JoinTime:   time.Now(),
			Score:      0,
			Rewards:    make([]RewardInstance, 0),
			Stats:      make(map[string]interface{}),
		}
		e.participants[pl.UUID().String()] = participant
		return true
	}

	return false
}

func (e *BaseEvent) OnPlayerLeave(pl *player.Player) bool {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if _, exists := e.participants[pl.UUID().String()]; exists {
		delete(e.participants, pl.UUID().String())
		return true
	}

	return false
}

func (e *BaseEvent) OnPlayerKill(killer, victim *player.Player) bool {
	// Default implementation does nothing
	return false
}

// Configuration and state
func (e *BaseEvent) Config() EventConfig {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	return e.config
}

func (e *BaseEvent) SetConfig(config EventConfig) error {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	e.config = config
	return nil
}

func (e *BaseEvent) State() map[string]interface{} {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	
	// Return a copy to prevent external modification
	stateCopy := make(map[string]interface{})
	for k, v := range e.state {
		stateCopy[k] = v
	}
	return stateCopy
}

func (e *BaseEvent) SetState(key string, value interface{}) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	e.state[key] = value
}

// Participant management
func (e *BaseEvent) GetParticipants() []*EventParticipant {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	participants := make([]*EventParticipant, 0, len(e.participants))
	for _, participant := range e.participants {
		participants = append(participants, participant)
	}
	return participants
}

func (e *BaseEvent) GetParticipantsMap() map[string]*EventParticipant {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	// Return a copy of the participants map
	participantsCopy := make(map[string]*EventParticipant)
	for k, v := range e.participants {
		participantsCopy[k] = v
	}
	return participantsCopy
}

func (e *BaseEvent) GetState() map[string]interface{} {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	// Return a copy of the state map
	stateCopy := make(map[string]interface{})
	for k, v := range e.state {
		stateCopy[k] = v
	}
	return stateCopy
}

func (e *BaseEvent) GetParticipant(playerUUID string) *EventParticipant {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	return e.participants[playerUUID]
}

func (e *BaseEvent) AddParticipant(participant *EventParticipant) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	e.participants[participant.PlayerUUID] = participant
}

func (e *BaseEvent) RemoveParticipant(playerUUID string) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	delete(e.participants, playerUUID)
}

// Helper method to check if event has expired
func (e *BaseEvent) HasExpired() bool {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	if e.status != EventStatusActive || e.config.Duration == 0 {
		return false
	}

	return time.Now().After(e.endTime)
}

// Helper method to add score to a participant
func (e *BaseEvent) AddScore(playerUUID string, points int) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if participant, exists := e.participants[playerUUID]; exists {
		participant.Score += points
	}
}
