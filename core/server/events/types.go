package events

import (
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"time"
)

// EventType represents the type of event
type EventType string

const (
	EventTypeLuckyBlock EventType = "lucky_block"
	EventTypeDoubleXP   EventType = "double_xp"
	EventTypeRareDrops  EventType = "rare_drops"
)

// EventStatus represents the current status of an event
type EventStatus string

const (
	EventStatusInactive EventStatus = "inactive"
	EventStatusActive   EventStatus = "active"
	EventStatusPaused   EventStatus = "paused"
	EventStatusEnded    EventStatus = "ended"
)

// EventPriority determines event execution order when multiple events are active
type EventPriority int

const (
	PriorityLow    EventPriority = 1
	PriorityNormal EventPriority = 5
	PriorityHigh   EventPriority = 10
)

// Event represents a game event that can be started, stopped, and managed
type Event interface {
	// Basic event information
	ID() string
	Type() EventType
	Name() string
	Description() string
	Priority() EventPriority

	// Event lifecycle
	Start() error
	Stop() error
	Pause() error
	Resume() error
	Status() EventStatus

	// Event timing
	Duration() time.Duration
	StartTime() time.Time
	EndTime() time.Time
	RemainingTime() time.Duration

	// Event handlers - these are called by the event manager when relevant game events occur
	OnBlockBreak(ctx *player.Context, pos cube.Pos, block world.Block, drops *[]item.Stack, xp *int) bool
	OnBlockPlace(ctx *player.Context, pos cube.Pos, block world.Block) bool
	OnPlayerJoin(pl *player.Player) bool
	OnPlayerLeave(pl *player.Player) bool
	OnPlayerKill(killer, victim *player.Player) bool

	// Configuration and state
	Config() EventConfig
	SetConfig(config EventConfig) error
	State() map[string]interface{}
	SetState(key string, value interface{})
}

// EventConfig holds configuration data for events
type EventConfig struct {
	// Basic settings
	Enabled     bool          `json:"enabled"`
	Duration    time.Duration `json:"duration"`
	Cooldown    time.Duration `json:"cooldown"`
	MaxPlayers  int           `json:"max_players"`
	MinPlayers  int           `json:"min_players"`
	AutoStart   bool          `json:"auto_start"`
	AutoRestart bool          `json:"auto_restart"`

	// Rewards and mechanics
	Rewards     []RewardConfig `json:"rewards"`
	Permissions []string       `json:"permissions"`
	Worlds      []string       `json:"worlds"`

	// Event-specific settings (stored as JSON for flexibility)
	CustomSettings map[string]interface{} `json:"custom_settings"`
}

// RewardConfig defines a reward that can be given during events
type RewardConfig struct {
	Type        RewardType             `json:"type"`
	Chance      float64                `json:"chance"`      // 0.0 to 1.0
	MinAmount   int                    `json:"min_amount"`
	MaxAmount   int                    `json:"max_amount"`
	Item        string                 `json:"item"`        // Item ID for item rewards
	Command     string                 `json:"command"`     // Command to execute for command rewards
	Message     string                 `json:"message"`     // Message to send to player
	Conditions  map[string]interface{} `json:"conditions"`  // Additional conditions
}

// RewardType represents different types of rewards
type RewardType string

const (
	RewardTypeItem     RewardType = "item"
	RewardTypeCommand  RewardType = "command"
	RewardTypeMessage  RewardType = "message"
	RewardTypeMoney    RewardType = "money"
	RewardTypeXP       RewardType = "xp"
	RewardTypeCustom   RewardType = "custom"
)

// EventParticipant represents a player participating in an event
type EventParticipant struct {
	PlayerUUID string                 `json:"player_uuid"`
	PlayerName string                 `json:"player_name"`
	JoinTime   time.Time              `json:"join_time"`
	Score      int                    `json:"score"`
	Rewards    []RewardInstance       `json:"rewards"`
	Stats      map[string]interface{} `json:"stats"`
}

// RewardInstance represents a reward that was actually given to a player
type RewardInstance struct {
	Type      RewardType `json:"type"`
	Amount    int        `json:"amount"`
	Item      string     `json:"item"`
	Timestamp time.Time  `json:"timestamp"`
	EventID   string     `json:"event_id"`
}

// EventListener allows external systems to listen for event changes
type EventListener interface {
	OnEventStart(event Event)
	OnEventStop(event Event)
	OnEventPause(event Event)
	OnEventResume(event Event)
	OnPlayerJoinEvent(event Event, participant *EventParticipant)
	OnPlayerLeaveEvent(event Event, participant *EventParticipant)
	OnRewardGiven(event Event, participant *EventParticipant, reward RewardInstance)
}

// EventManager manages all events in the server
type EventManager interface {
	// Manager lifecycle
	Start() error
	Shutdown() error

	// Event registration
	RegisterEvent(event Event) error
	UnregisterEvent(eventID string) error
	GetEvent(eventID string) Event
	GetActiveEvents() []Event
	GetAllEvents() []Event

	// Event control
	StartEvent(eventID string) error
	StopEvent(eventID string) error
	PauseEvent(eventID string) error
	ResumeEvent(eventID string) error
	RestartEvent(eventID string) error

	// Event lifecycle hooks - these are called by game handlers
	HandleBlockBreak(ctx *player.Context, pos cube.Pos, block world.Block, drops *[]item.Stack, xp *int)
	HandleBlockPlace(ctx *player.Context, pos cube.Pos, block world.Block)
	HandlePlayerJoin(pl *player.Player)
	HandlePlayerLeave(pl *player.Player)
	HandlePlayerKill(killer, victim *player.Player)

	// Participant management
	GetParticipants(eventID string) []*EventParticipant
	GetPlayerParticipation(playerUUID string) []string // Returns event IDs
	AddParticipant(eventID string, participant *EventParticipant) error
	RemoveParticipant(eventID string, playerUUID string) error

	// Listeners
	AddListener(listener EventListener)
	RemoveListener(listener EventListener)

	// Configuration
	LoadConfig(configPath string) error
	SaveConfig(configPath string) error
	ReloadConfig() error

	// Monitoring and Health
	GetHealthStatus() map[string]interface{}
	GetPersistenceStats() map[string]interface{}
}

// EventFactory creates events of specific types
type EventFactory interface {
	CreateEvent(eventType EventType, config EventConfig) (Event, error)
	GetSupportedTypes() []EventType
}
