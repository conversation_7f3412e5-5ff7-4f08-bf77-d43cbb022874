package events

import (
	"fmt"
	"log/slog"
	"sync"
)

var (
	// Global event manager instance
	Manager EventManager
	// Global event factory instance
	Factory EventFactory
	// Initialization mutex
	initMutex sync.Once
)

// Initialize initializes the global event system
func Initialize() {
	initMutex.Do(func() {
		slog.Info("Initializing event system")

		// Initialize configuration manager
		if err := InitializeConfigManager("config/events.json"); err != nil {
			slog.Error("Failed to initialize configuration manager", "error", err)
			return
		}

		// Create the event manager
		Manager = NewEventManager()

		// Start the event manager
		if err := Manager.Start(); err != nil {
			slog.Error("Failed to start event manager", "error", err)
			return
		}

		// Create the event factory
		Factory = NewEventFactory()

		// Create default events from configuration
		if err := createDefaultEvents(); err != nil {
			slog.Error("Failed to create default events", "error", err)
		}

		slog.Info("Event system initialized successfully")
	})
}

// Shutdown shuts down the global event system
func Shutdown() {
	if Manager != nil {
		if err := Manager.Shutdown(); err != nil {
			slog.Error("Failed to shutdown event manager", "error", err)
		}
	}
}

// GetManager returns the global event manager
func GetManager() EventManager {
	if Manager == nil {
		Initialize()
	}
	return Manager
}

// GetFactory returns the global event factory
func GetFactory() EventFactory {
	if Factory == nil {
		Initialize()
	}
	return Factory
}

// Convenience functions for common operations

// StartEvent starts an event by ID
func StartEvent(eventID string) error {
	return GetManager().StartEvent(eventID)
}

// StopEvent stops an event by ID
func StopEvent(eventID string) error {
	return GetManager().StopEvent(eventID)
}

// CreateAndRegisterEvent creates and registers a new event
func CreateAndRegisterEvent(eventType EventType, config EventConfig) (Event, error) {
	event, err := GetFactory().CreateEvent(eventType, config)
	if err != nil {
		return nil, err
	}
	
	if err := GetManager().RegisterEvent(event); err != nil {
		return nil, err
	}
	
	return event, nil
}

// GetActiveEvents returns all currently active events
func GetActiveEvents() []Event {
	return GetManager().GetActiveEvents()
}

// GetEvent returns an event by ID
func GetEvent(eventID string) Event {
	return GetManager().GetEvent(eventID)
}

// IsEventActive checks if an event is currently active
func IsEventActive(eventID string) bool {
	event := GetEvent(eventID)
	if event == nil {
		return false
	}
	return event.Status() == EventStatusActive
}

// GetPlayerParticipation returns all events a player is participating in
func GetPlayerParticipation(playerUUID string) []string {
	return GetManager().GetPlayerParticipation(playerUUID)
}

// AddEventListener adds a listener to the event system
func AddEventListener(listener EventListener) {
	GetManager().AddListener(listener)
}

// RemoveEventListener removes a listener from the event system
func RemoveEventListener(listener EventListener) {
	GetManager().RemoveListener(listener)
}

// createDefaultEvents creates default events from configuration
func createDefaultEvents() error {
	configManager := GetConfigManager()
	if configManager == nil {
		return fmt.Errorf("configuration manager not initialized")
	}

	configs := configManager.GetAllConfigs()
	for eventTypeStr, config := range configs {
		eventType := EventType(eventTypeStr)

		// Only create enabled events
		if !config.Enabled {
			slog.Info("Skipping disabled event", "type", eventType)
			continue
		}

		// Create the event
		event, err := GetFactory().CreateEvent(eventType, config)
		if err != nil {
			slog.Error("Failed to create event", "type", eventType, "error", err)
			continue
		}

		// Register the event
		if err := GetManager().RegisterEvent(event); err != nil {
			slog.Error("Failed to register event", "type", eventType, "error", err)
			continue
		}

		slog.Info("Created and registered event", "type", eventType, "id", event.ID())
	}

	return nil
}
