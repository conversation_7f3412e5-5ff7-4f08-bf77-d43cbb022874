package events

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"time"
)

// CrashRecoveryManager handles crash recovery for events
type CrashRecoveryManager struct {
	dataDir        string
	lockFile       string
	recoveryFile   string
	heartbeatFile  string
	processID      int
	lastHeartbeat  time.Time
	heartbeatInterval time.Duration
}

// RecoveryData contains information needed for crash recovery
type RecoveryData struct {
	ProcessID     int                    `json:"process_id"`
	StartTime     time.Time              `json:"start_time"`
	LastSave      time.Time              `json:"last_save"`
	ActiveEvents  []string               `json:"active_events"`
	ServerInfo    map[string]interface{} `json:"server_info"`
	RecoveryFlags map[string]bool        `json:"recovery_flags"`
}

// NewCrashRecoveryManager creates a new crash recovery manager
func NewCrashRecoveryManager(dataDir string) *CrashRecoveryManager {
	return &CrashRecoveryManager{
		dataDir:           dataDir,
		lockFile:          filepath.Join(dataDir, "event_manager.lock"),
		recoveryFile:      filepath.Join(dataDir, "recovery.json"),
		heartbeatFile:     filepath.Join(dataDir, "heartbeat.json"),
		processID:         os.Getpid(),
		heartbeatInterval: 5 * time.Second,
	}
}

// CheckForCrash checks if the previous instance crashed and needs recovery
func (crm *CrashRecoveryManager) CheckForCrash() (bool, *RecoveryData, error) {
	// Check if lock file exists
	if _, err := os.Stat(crm.lockFile); os.IsNotExist(err) {
		// No lock file, clean start
		return false, nil, nil
	}

	// Read lock file to get previous process info
	lockData, err := os.ReadFile(crm.lockFile)
	if err != nil {
		return false, nil, fmt.Errorf("failed to read lock file: %w", err)
	}

	var recoveryData RecoveryData
	if err := json.Unmarshal(lockData, &recoveryData); err != nil {
		// Corrupted lock file, assume crash
		slog.Warn("Corrupted lock file detected, assuming crash occurred")
		return true, nil, nil
	}

	// Check if the process is still running
	if crm.isProcessRunning(recoveryData.ProcessID) {
		return false, nil, fmt.Errorf("another event manager instance is already running (PID: %d)", recoveryData.ProcessID)
	}

	// Check heartbeat file
	crashed := crm.checkHeartbeat()
	if crashed {
		slog.Warn("Previous instance crashed, recovery needed", 
			"previous_pid", recoveryData.ProcessID,
			"last_save", recoveryData.LastSave)
		return true, &recoveryData, nil
	}

	return false, nil, nil
}

// CreateLock creates a lock file to indicate the event manager is running
func (crm *CrashRecoveryManager) CreateLock(activeEvents []string) error {
	// Ensure directory exists
	if err := os.MkdirAll(crm.dataDir, 0755); err != nil {
		return fmt.Errorf("failed to create data directory: %w", err)
	}

	recoveryData := RecoveryData{
		ProcessID:    crm.processID,
		StartTime:    time.Now(),
		LastSave:     time.Now(),
		ActiveEvents: activeEvents,
		ServerInfo: map[string]interface{}{
			"hostname": crm.getHostname(),
			"version":  "1.0.0", // TODO: Get actual version
		},
		RecoveryFlags: map[string]bool{
			"clean_shutdown": false,
		},
	}

	data, err := json.MarshalIndent(recoveryData, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal recovery data: %w", err)
	}

	if err := os.WriteFile(crm.lockFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write lock file: %w", err)
	}

	// Start heartbeat
	go crm.startHeartbeat()

	slog.Info("Event manager lock created", "pid", crm.processID, "lock_file", crm.lockFile)
	return nil
}

// UpdateLock updates the lock file with current state
func (crm *CrashRecoveryManager) UpdateLock(activeEvents []string) error {
	// Read current lock data
	lockData, err := os.ReadFile(crm.lockFile)
	if err != nil {
		return fmt.Errorf("failed to read lock file: %w", err)
	}

	var recoveryData RecoveryData
	if err := json.Unmarshal(lockData, &recoveryData); err != nil {
		return fmt.Errorf("failed to unmarshal lock data: %w", err)
	}

	// Update with current state
	recoveryData.LastSave = time.Now()
	recoveryData.ActiveEvents = activeEvents

	data, err := json.MarshalIndent(recoveryData, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal recovery data: %w", err)
	}

	if err := os.WriteFile(crm.lockFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write lock file: %w", err)
	}

	return nil
}

// RemoveLock removes the lock file on clean shutdown
func (crm *CrashRecoveryManager) RemoveLock() error {
	// Mark as clean shutdown
	if _, err := os.Stat(crm.lockFile); err == nil {
		lockData, err := os.ReadFile(crm.lockFile)
		if err == nil {
			var recoveryData RecoveryData
			if json.Unmarshal(lockData, &recoveryData) == nil {
				recoveryData.RecoveryFlags["clean_shutdown"] = true
				if data, err := json.MarshalIndent(recoveryData, "", "  "); err == nil {
					os.WriteFile(crm.lockFile, data, 0644)
				}
			}
		}
	}

	// Remove lock file
	if err := os.Remove(crm.lockFile); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to remove lock file: %w", err)
	}

	// Remove heartbeat file
	if err := os.Remove(crm.heartbeatFile); err != nil && !os.IsNotExist(err) {
		slog.Warn("Failed to remove heartbeat file", "error", err)
	}

	slog.Info("Event manager lock removed", "pid", crm.processID)
	return nil
}

// startHeartbeat starts the heartbeat mechanism
func (crm *CrashRecoveryManager) startHeartbeat() {
	ticker := time.NewTicker(crm.heartbeatInterval)
	defer ticker.Stop()

	for range ticker.C {
		crm.updateHeartbeat()
	}
}

// updateHeartbeat updates the heartbeat file
func (crm *CrashRecoveryManager) updateHeartbeat() {
	crm.lastHeartbeat = time.Now()
	
	heartbeatData := map[string]interface{}{
		"pid":       crm.processID,
		"timestamp": crm.lastHeartbeat,
		"uptime":    time.Since(crm.lastHeartbeat).Seconds(),
	}

	data, err := json.MarshalIndent(heartbeatData, "", "  ")
	if err != nil {
		slog.Warn("Failed to marshal heartbeat data", "error", err)
		return
	}

	if err := os.WriteFile(crm.heartbeatFile, data, 0644); err != nil {
		slog.Warn("Failed to write heartbeat file", "error", err)
	}
}

// checkHeartbeat checks if the previous instance's heartbeat is stale
func (crm *CrashRecoveryManager) checkHeartbeat() bool {
	if _, err := os.Stat(crm.heartbeatFile); os.IsNotExist(err) {
		// No heartbeat file, assume crash
		return true
	}

	data, err := os.ReadFile(crm.heartbeatFile)
	if err != nil {
		// Can't read heartbeat, assume crash
		return true
	}

	var heartbeatData map[string]interface{}
	if err := json.Unmarshal(data, &heartbeatData); err != nil {
		// Corrupted heartbeat, assume crash
		return true
	}

	timestampStr, ok := heartbeatData["timestamp"].(string)
	if !ok {
		return true
	}

	timestamp, err := time.Parse(time.RFC3339, timestampStr)
	if err != nil {
		return true
	}

	// If heartbeat is older than 30 seconds, consider it a crash
	return time.Since(timestamp) > 30*time.Second
}

// isProcessRunning checks if a process with the given PID is running
func (crm *CrashRecoveryManager) isProcessRunning(pid int) bool {
	// On Unix systems, we can check if the process exists
	process, err := os.FindProcess(pid)
	if err != nil {
		return false
	}

	// Try to send signal 0 to check if process exists
	err = process.Signal(os.Signal(nil))
	return err == nil
}

// getHostname returns the system hostname
func (crm *CrashRecoveryManager) getHostname() string {
	hostname, err := os.Hostname()
	if err != nil {
		return "unknown"
	}
	return hostname
}

// RecoverEvents attempts to recover events from crash
func (crm *CrashRecoveryManager) RecoverEvents(manager EventManager, factory EventFactory) error {
	slog.Info("Starting event recovery process")

	// Load recovery data
	recoveryData, err := crm.loadRecoveryData()
	if err != nil {
		return fmt.Errorf("failed to load recovery data: %w", err)
	}

	if recoveryData == nil {
		slog.Info("No recovery data found")
		return nil
	}

	// Try to recover each active event
	recovered := 0
	for _, eventID := range recoveryData.ActiveEvents {
		if err := crm.recoverSingleEvent(eventID, manager, factory); err != nil {
			slog.Warn("Failed to recover event", "event_id", eventID, "error", err)
		} else {
			recovered++
			slog.Info("Successfully recovered event", "event_id", eventID)
		}
	}

	slog.Info("Event recovery completed", 
		"total_events", len(recoveryData.ActiveEvents),
		"recovered", recovered,
		"failed", len(recoveryData.ActiveEvents)-recovered)

	return nil
}

// loadRecoveryData loads recovery data from the recovery file
func (crm *CrashRecoveryManager) loadRecoveryData() (*RecoveryData, error) {
	if _, err := os.Stat(crm.recoveryFile); os.IsNotExist(err) {
		return nil, nil
	}

	data, err := os.ReadFile(crm.recoveryFile)
	if err != nil {
		return nil, err
	}

	var recoveryData RecoveryData
	if err := json.Unmarshal(data, &recoveryData); err != nil {
		return nil, err
	}

	return &recoveryData, nil
}

// recoverSingleEvent attempts to recover a single event
func (crm *CrashRecoveryManager) recoverSingleEvent(eventID string, manager EventManager, factory EventFactory) error {
	// Check if event already exists in manager
	if event := manager.GetEvent(eventID); event != nil {
		// Event already exists, just restart it if it was active
		if event.Status() == EventStatusActive {
			return manager.StartEvent(eventID)
		}
		return nil
	}

	// Event doesn't exist, we can't recover it without more information
	// This would require storing more detailed event configuration in recovery data
	slog.Warn("Cannot recover event - insufficient recovery data", "event_id", eventID)
	return fmt.Errorf("insufficient recovery data for event %s", eventID)
}

// SaveRecoveryData saves current recovery data
func (crm *CrashRecoveryManager) SaveRecoveryData(activeEvents []string) error {
	recoveryData := RecoveryData{
		ProcessID:    crm.processID,
		StartTime:    time.Now(),
		LastSave:     time.Now(),
		ActiveEvents: activeEvents,
		ServerInfo: map[string]interface{}{
			"hostname": crm.getHostname(),
		},
		RecoveryFlags: map[string]bool{
			"clean_shutdown": false,
		},
	}

	data, err := json.MarshalIndent(recoveryData, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal recovery data: %w", err)
	}

	if err := os.WriteFile(crm.recoveryFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write recovery file: %w", err)
	}

	return nil
}
