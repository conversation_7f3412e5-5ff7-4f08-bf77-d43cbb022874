package events

import (
	"encoding/json"
	"fmt"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"log/slog"
	"os"
	"path/filepath"
	"server/server"
	"sort"
	"sync"
	"time"
)

// DefaultEventManager is the default implementation of EventManager
type DefaultEventManager struct {
	events             map[string]Event
	listeners          []EventListener
	mutex              sync.RWMutex
	ticker             *time.Ticker
	stopChan           chan bool
	running            bool
	persistPath        string
	persistenceManager *PersistenceManager
	recoveryManager    *CrashRecoveryManager
	monitoringManager  *MonitoringManager
	dataDir            string
}

// NewEventManager creates a new event manager
func NewEventManager() *DefaultEventManager {
	dataDir := "data/events"
	manager := &DefaultEventManager{
		events:             make(map[string]Event),
		listeners:          make([]EventListener, 0),
		stop<PERSON>han:           make(chan bool),
		persistPath:        filepath.Join(dataDir, "events_state.json"),
		running:            false,
		dataDir:            dataDir,
		persistenceManager: NewPersistenceManager(dataDir),
		recoveryManager:    NewCrashRecoveryManager(dataDir),
	}

	// Initialize monitoring manager
	manager.monitoringManager = NewMonitoringManager(manager, manager.persistenceManager, manager.recoveryManager)

	// Don't auto-start the manager - let it be started explicitly
	return manager
}

// Start starts the event manager
func (m *DefaultEventManager) Start() error {
	if m.running {
		return fmt.Errorf("event manager is already running")
	}

	// Check for crash and attempt recovery
	crashed, recoveryData, err := m.recoveryManager.CheckForCrash()
	if err != nil {
		return fmt.Errorf("crash check failed: %w", err)
	}

	if crashed {
		slog.Warn("Previous instance crashed, attempting recovery")
		if recoveryData != nil {
			slog.Info("Recovery data found",
				"previous_pid", recoveryData.ProcessID,
				"active_events", len(recoveryData.ActiveEvents))
		}
	}

	// Start persistence manager
	if err := m.persistenceManager.Start(); err != nil {
		return fmt.Errorf("failed to start persistence manager: %w", err)
	}

	// Start monitoring manager
	if err := m.monitoringManager.Start(); err != nil {
		return fmt.Errorf("failed to start monitoring manager: %w", err)
	}

	// Load persisted event state
	if err := m.loadEventState(); err != nil {
		slog.Warn("Failed to load event state", "error", err)
	}

	// Attempt crash recovery if needed
	if crashed {
		if err := m.recoveryManager.RecoverEvents(m, GetFactory()); err != nil {
			slog.Warn("Event recovery failed", "error", err)
		}
	}

	// Create lock file
	activeEventIDs := make([]string, 0)
	for _, event := range m.GetActiveEvents() {
		activeEventIDs = append(activeEventIDs, event.ID())
	}
	if err := m.recoveryManager.CreateLock(activeEventIDs); err != nil {
		return fmt.Errorf("failed to create lock: %w", err)
	}

	m.running = true
	m.ticker = time.NewTicker(30 * time.Second) // Check every 30 seconds

	go func() {
		for {
			select {
			case <-m.ticker.C:
				m.checkEventExpiration()
				// Save state periodically
				if err := m.saveEventState(); err != nil {
					slog.Warn("Failed to save event state", "error", err)
				}
				// Update recovery lock
				activeEventIDs := make([]string, 0)
				for _, event := range m.GetActiveEvents() {
					activeEventIDs = append(activeEventIDs, event.ID())
				}
				if err := m.recoveryManager.UpdateLock(activeEventIDs); err != nil {
					slog.Warn("Failed to update recovery lock", "error", err)
				}
			case <-m.stopChan:
				return
			}
		}
	}()

	slog.Info("Event manager started with enhanced persistence and crash recovery")
	return nil
}

// startTicker starts the background ticker for event management (legacy method)
func (m *DefaultEventManager) startTicker() {
	// This method is kept for compatibility but Start() should be used instead
	if err := m.Start(); err != nil {
		slog.Error("Failed to start event manager", "error", err)
	}
}

// checkEventExpiration checks if any active events have expired
func (m *DefaultEventManager) checkEventExpiration() {
	m.mutex.RLock()
	eventsToCheck := make([]Event, 0, len(m.events))
	for _, event := range m.events {
		eventsToCheck = append(eventsToCheck, event)
	}
	m.mutex.RUnlock()

	for _, event := range eventsToCheck {
		if baseEvent, ok := event.(*BaseEvent); ok {
			if baseEvent.HasExpired() {
				go func(e Event) {
					if err := e.Stop(); err != nil {
						slog.Error("Failed to stop expired event", "event_id", e.ID(), "error", err)
					} else {
						slog.Info("Event expired and stopped", "event_id", e.ID(), "name", e.Name())

						// Broadcast event end message
						if server.MCServer != nil {
							for pl := range server.MCServer.Players(nil) {
								pl.Message(fmt.Sprintf("§6[EVENT] §cEvent %s (ID: %s) has ended!", e.Name(), e.ID()))
								pl.RemoveBossBar() // Remove any boss bars
							}
						}

						m.notifyEventStop(e)
					}
				}(event)
			}
		}
	}
}

// Event registration
func (m *DefaultEventManager) RegisterEvent(event Event) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.events[event.ID()]; exists {
		return fmt.Errorf("event with ID %s already exists", event.ID())
	}

	m.events[event.ID()] = event
	slog.Info("Event registered", "event_id", event.ID(), "type", event.Type(), "name", event.Name())
	return nil
}

func (m *DefaultEventManager) UnregisterEvent(eventID string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	event, exists := m.events[eventID]
	if !exists {
		return fmt.Errorf("event with ID %s not found", eventID)
	}

	// Stop the event if it's active
	if event.Status() == EventStatusActive {
		if err := event.Stop(); err != nil {
			slog.Warn("Failed to stop event during unregistration", "event_id", eventID, "error", err)
		}
	}

	delete(m.events, eventID)
	slog.Info("Event unregistered", "event_id", eventID)
	return nil
}

func (m *DefaultEventManager) GetEvent(eventID string) Event {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.events[eventID]
}

func (m *DefaultEventManager) GetActiveEvents() []Event {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var activeEvents []Event
	for _, event := range m.events {
		if event.Status() == EventStatusActive {
			activeEvents = append(activeEvents, event)
		}
	}

	// Sort by priority (highest first)
	sort.Slice(activeEvents, func(i, j int) bool {
		return activeEvents[i].Priority() > activeEvents[j].Priority()
	})

	return activeEvents
}

func (m *DefaultEventManager) GetAllEvents() []Event {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var allEvents []Event
	for _, event := range m.events {
		allEvents = append(allEvents, event)
	}
	return allEvents
}

// Event control
func (m *DefaultEventManager) StartEvent(eventID string) error {
	m.mutex.RLock()
	event, exists := m.events[eventID]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("event with ID %s not found", eventID)
	}

	if err := event.Start(); err != nil {
		return err
	}

	slog.Info("Event started", "event_id", eventID, "name", event.Name())
	m.notifyEventStart(event)
	return nil
}

func (m *DefaultEventManager) StopEvent(eventID string) error {
	m.mutex.RLock()
	event, exists := m.events[eventID]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("event with ID %s not found", eventID)
	}

	if err := event.Stop(); err != nil {
		return err
	}

	slog.Info("Event stopped", "event_id", eventID, "name", event.Name())
	m.notifyEventStop(event)
	return nil
}

func (m *DefaultEventManager) PauseEvent(eventID string) error {
	m.mutex.RLock()
	event, exists := m.events[eventID]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("event with ID %s not found", eventID)
	}

	if err := event.Pause(); err != nil {
		return err
	}

	slog.Info("Event paused", "event_id", eventID, "name", event.Name())
	m.notifyEventPause(event)
	return nil
}

func (m *DefaultEventManager) ResumeEvent(eventID string) error {
	m.mutex.RLock()
	event, exists := m.events[eventID]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("event with ID %s not found", eventID)
	}

	if err := event.Resume(); err != nil {
		return err
	}

	slog.Info("Event resumed", "event_id", eventID, "name", event.Name())
	m.notifyEventResume(event)
	return nil
}

func (m *DefaultEventManager) RestartEvent(eventID string) error {
	if err := m.StopEvent(eventID); err != nil {
		return err
	}

	// Small delay to ensure clean restart
	time.Sleep(100 * time.Millisecond)

	return m.StartEvent(eventID)
}

// Event lifecycle hooks - these are called by game handlers
func (m *DefaultEventManager) HandleBlockBreak(ctx *player.Context, pos cube.Pos, block world.Block, drops *[]item.Stack, xp *int) {
	activeEvents := m.GetActiveEvents()
	for _, event := range activeEvents {
		if event.OnBlockBreak(ctx, pos, block, drops, xp) {
			// If an event handles the block break and returns true, stop processing other events
			break
		}
	}
}

func (m *DefaultEventManager) HandleBlockPlace(ctx *player.Context, pos cube.Pos, block world.Block) {
	activeEvents := m.GetActiveEvents()
	for _, event := range activeEvents {
		if event.OnBlockPlace(ctx, pos, block) {
			break
		}
	}
}

func (m *DefaultEventManager) HandlePlayerJoin(pl *player.Player) {
	activeEvents := m.GetActiveEvents()
	for _, event := range activeEvents {
		if event.OnPlayerJoin(pl) {
			if baseEvent, ok := event.(*BaseEvent); ok {
				participant := baseEvent.GetParticipant(pl.UUID().String())
				if participant != nil {
					m.notifyPlayerJoinEvent(event, participant)
				}
			}
		}
	}
}

func (m *DefaultEventManager) HandlePlayerLeave(pl *player.Player) {
	activeEvents := m.GetActiveEvents()
	for _, event := range activeEvents {
		if baseEvent, ok := event.(*BaseEvent); ok {
			participant := baseEvent.GetParticipant(pl.UUID().String())
			if participant != nil && event.OnPlayerLeave(pl) {
				m.notifyPlayerLeaveEvent(event, participant)
			}
		}
	}
}

func (m *DefaultEventManager) HandlePlayerKill(killer, victim *player.Player) {
	activeEvents := m.GetActiveEvents()
	for _, event := range activeEvents {
		if event.OnPlayerKill(killer, victim) {
			break
		}
	}
}

// Participant management
func (m *DefaultEventManager) GetParticipants(eventID string) []*EventParticipant {
	event := m.GetEvent(eventID)
	if event == nil {
		return nil
	}

	if baseEvent, ok := event.(*BaseEvent); ok {
		return baseEvent.GetParticipants()
	}

	return nil
}

func (m *DefaultEventManager) GetPlayerParticipation(playerUUID string) []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var eventIDs []string
	for _, event := range m.events {
		if baseEvent, ok := event.(*BaseEvent); ok {
			if baseEvent.GetParticipant(playerUUID) != nil {
				eventIDs = append(eventIDs, event.ID())
			}
		}
	}
	return eventIDs
}

func (m *DefaultEventManager) AddParticipant(eventID string, participant *EventParticipant) error {
	event := m.GetEvent(eventID)
	if event == nil {
		return fmt.Errorf("event with ID %s not found", eventID)
	}

	if baseEvent, ok := event.(*BaseEvent); ok {
		baseEvent.AddParticipant(participant)
		m.notifyPlayerJoinEvent(event, participant)
		return nil
	}

	return fmt.Errorf("event does not support participant management")
}

func (m *DefaultEventManager) RemoveParticipant(eventID string, playerUUID string) error {
	event := m.GetEvent(eventID)
	if event == nil {
		return fmt.Errorf("event with ID %s not found", eventID)
	}

	if baseEvent, ok := event.(*BaseEvent); ok {
		participant := baseEvent.GetParticipant(playerUUID)
		if participant != nil {
			m.notifyPlayerLeaveEvent(event, participant)
			baseEvent.RemoveParticipant(playerUUID)
		}
		return nil
	}

	return fmt.Errorf("event does not support participant management")
}

// Listeners
func (m *DefaultEventManager) AddListener(listener EventListener) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.listeners = append(m.listeners, listener)
}

func (m *DefaultEventManager) RemoveListener(listener EventListener) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for i, l := range m.listeners {
		if l == listener {
			m.listeners = append(m.listeners[:i], m.listeners[i+1:]...)
			break
		}
	}
}

// Notification methods
func (m *DefaultEventManager) notifyEventStart(event Event) {
	for _, listener := range m.listeners {
		go listener.OnEventStart(event)
	}
}

func (m *DefaultEventManager) notifyEventStop(event Event) {
	for _, listener := range m.listeners {
		go listener.OnEventStop(event)
	}
}

func (m *DefaultEventManager) notifyEventPause(event Event) {
	for _, listener := range m.listeners {
		go listener.OnEventPause(event)
	}
}

func (m *DefaultEventManager) notifyEventResume(event Event) {
	for _, listener := range m.listeners {
		go listener.OnEventResume(event)
	}
}

func (m *DefaultEventManager) notifyPlayerJoinEvent(event Event, participant *EventParticipant) {
	for _, listener := range m.listeners {
		go listener.OnPlayerJoinEvent(event, participant)
	}
}

func (m *DefaultEventManager) notifyPlayerLeaveEvent(event Event, participant *EventParticipant) {
	for _, listener := range m.listeners {
		go listener.OnPlayerLeaveEvent(event, participant)
	}
}

func (m *DefaultEventManager) notifyRewardGiven(event Event, participant *EventParticipant, reward RewardInstance) {
	for _, listener := range m.listeners {
		go listener.OnRewardGiven(event, participant, reward)
	}
}

// Configuration
func (m *DefaultEventManager) LoadConfig(configPath string) error {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return err
	}

	var configs map[string]EventConfig
	if err := json.Unmarshal(data, &configs); err != nil {
		return err
	}

	// Apply configurations to existing events
	for eventID, config := range configs {
		if event := m.GetEvent(eventID); event != nil {
			if err := event.SetConfig(config); err != nil {
				slog.Warn("Failed to apply config to event", "event_id", eventID, "error", err)
			}
		}
	}

	return nil
}

func (m *DefaultEventManager) SaveConfig(configPath string) error {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	configs := make(map[string]EventConfig)
	for eventID, event := range m.events {
		configs[eventID] = event.Config()
	}

	data, err := json.MarshalIndent(configs, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(configPath, data, 0644)
}

func (m *DefaultEventManager) ReloadConfig() error {
	return m.LoadConfig("config/events.json")
}

// EventStateData represents the persistent state of an event
type EventStateData struct {
	ID           string                 `json:"id"`
	Type         EventType              `json:"type"`
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	Status       EventStatus            `json:"status"`
	StartTime    time.Time              `json:"start_time"`
	EndTime      time.Time              `json:"end_time"`
	Config       EventConfig            `json:"config"`
	Participants map[string]*EventParticipant `json:"participants"`
	State        map[string]interface{} `json:"state"`
}

// saveEventState saves the current state of all events to disk
func (m *DefaultEventManager) saveEventState() error {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var eventStates []EventStateData
	for _, event := range m.events {
		if baseEvent, ok := event.(*BaseEvent); ok {
			stateData := EventStateData{
				ID:           baseEvent.ID(),
				Type:         baseEvent.Type(),
				Name:         baseEvent.Name(),
				Description:  baseEvent.Description(),
				Status:       baseEvent.Status(),
				StartTime:    baseEvent.StartTime(),
				EndTime:      baseEvent.EndTime(),
				Config:       baseEvent.Config(),
				Participants: baseEvent.GetParticipantsMap(),
				State:        baseEvent.GetState(),
			}
			eventStates = append(eventStates, stateData)
		}
	}

	// Use enhanced persistence manager
	if err := m.persistenceManager.SaveEventState(eventStates); err != nil {
		return fmt.Errorf("failed to save event state: %w", err)
	}

	// Also save recovery data
	activeEventIDs := make([]string, 0)
	for _, event := range m.events {
		if event.Status() == EventStatusActive {
			activeEventIDs = append(activeEventIDs, event.ID())
		}
	}
	if err := m.recoveryManager.SaveRecoveryData(activeEventIDs); err != nil {
		slog.Warn("Failed to save recovery data", "error", err)
	}

	return nil
}

// loadEventState loads the persisted event state from disk
func (m *DefaultEventManager) loadEventState() error {
	// Use enhanced persistence manager with fallback support
	eventStates, err := m.persistenceManager.LoadEventState()
	if err != nil {
		return fmt.Errorf("failed to load event state: %w", err)
	}

	if len(eventStates) == 0 {
		slog.Info("No persisted event state found, starting fresh")
		return nil
	}

	// Restore events that were active
	restoredCount := 0
	for _, stateData := range eventStates {
		if stateData.Status == EventStatusActive {
			// Only restore if the event hasn't expired
			if stateData.EndTime.After(time.Now()) {
				// Create the event using the factory
				event, err := GetFactory().CreateEvent(stateData.Type, stateData.Config)
				if err != nil {
					slog.Warn("Failed to restore event", "id", stateData.ID, "type", stateData.Type, "error", err)
					continue
				}

				// Restore the event state
				if baseEvent, ok := event.(*BaseEvent); ok {
					baseEvent.id = stateData.ID
					baseEvent.status = stateData.Status
					baseEvent.startTime = stateData.StartTime
					baseEvent.endTime = stateData.EndTime
					baseEvent.participants = stateData.Participants
					baseEvent.state = stateData.State
				}

				// Register the restored event
				m.events[stateData.ID] = event
				restoredCount++
				slog.Info("Restored active event", "id", stateData.ID, "name", stateData.Name)
			} else {
				slog.Info("Skipped expired event", "id", stateData.ID, "name", stateData.Name)
			}
		}
	}

	slog.Info("Event state restoration completed",
		"total_events", len(eventStates),
		"restored_active", restoredCount)

	return nil
}

// Shutdown
func (m *DefaultEventManager) Shutdown() error {
	if !m.running {
		return nil
	}

	slog.Info("Starting event manager shutdown")
	m.running = false

	// Send stop signal to background goroutine
	select {
	case m.stopChan <- true:
	default:
		// Channel might be full or closed, continue with shutdown
	}

	// Stop ticker if it exists
	if m.ticker != nil {
		m.ticker.Stop()
	}

	// Save event state before stopping
	if err := m.saveEventState(); err != nil {
		slog.Warn("Failed to save event state during shutdown", "error", err)
	}

	// Stop all active events
	for _, event := range m.GetActiveEvents() {
		if err := event.Stop(); err != nil {
			slog.Warn("Failed to stop event during shutdown", "event_id", event.ID(), "error", err)
		}
	}

	// Stop monitoring manager
	if err := m.monitoringManager.Stop(); err != nil {
		slog.Warn("Failed to stop monitoring manager", "error", err)
	}

	// Stop persistence manager
	if err := m.persistenceManager.Stop(); err != nil {
		slog.Warn("Failed to stop persistence manager", "error", err)
	}

	// Remove lock file (clean shutdown)
	if err := m.recoveryManager.RemoveLock(); err != nil {
		slog.Warn("Failed to remove lock file", "error", err)
	}

	slog.Info("Event manager shut down successfully")
	return nil
}

// GetHealthStatus returns the health status of the event system
func (m *DefaultEventManager) GetHealthStatus() map[string]interface{} {
	if m.monitoringManager == nil {
		return map[string]interface{}{
			"status": "monitoring_unavailable",
			"error":  "monitoring manager not initialized",
		}
	}

	healthChecks := m.monitoringManager.GetHealthStatus()

	// Calculate overall status
	overallStatus := HealthStatusHealthy
	for _, check := range healthChecks {
		if check.Status == HealthStatusUnhealthy {
			overallStatus = HealthStatusUnhealthy
			break
		} else if check.Status == HealthStatusDegraded && overallStatus == HealthStatusHealthy {
			overallStatus = HealthStatusDegraded
		}
	}

	return map[string]interface{}{
		"overall_status": overallStatus,
		"components":     healthChecks,
		"timestamp":      time.Now(),
		"uptime":         time.Since(time.Now()).String(), // TODO: Track actual start time
	}
}

// GetPersistenceStats returns persistence statistics
func (m *DefaultEventManager) GetPersistenceStats() map[string]interface{} {
	if m.persistenceManager == nil {
		return map[string]interface{}{
			"status": "persistence_unavailable",
			"error":  "persistence manager not initialized",
		}
	}

	return m.persistenceManager.GetStats()
}
