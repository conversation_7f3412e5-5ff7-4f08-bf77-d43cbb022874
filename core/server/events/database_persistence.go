package events

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log/slog"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

// DatabasePersistence handles database-based event persistence
type DatabasePersistence struct {
	db          *sql.DB
	enabled     bool
	tableName   string
	backupTable string
}

// NewDatabasePersistence creates a new database persistence manager
func NewDatabasePersistence(dsn string) (*DatabasePersistence, error) {
	if dsn == "" {
		return &DatabasePersistence{enabled: false}, nil
	}

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		db.Close()
		slog.Warn("Database connection failed, disabling database persistence", "error", err)
		return &DatabasePersistence{enabled: false}, nil
	}

	dp := &DatabasePersistence{
		db:          db,
		enabled:     true,
		tableName:   "event_states",
		backupTable: "event_states_backup",
	}

	// Initialize tables
	if err := dp.initializeTables(); err != nil {
		slog.Warn("Failed to initialize database tables, disabling database persistence", "error", err)
		db.Close()
		return &DatabasePersistence{enabled: false}, nil
	}

	slog.Info("Database persistence initialized successfully")
	return dp, nil
}

// initializeTables creates the necessary database tables
func (dp *DatabasePersistence) initializeTables() error {
	if !dp.enabled {
		return nil
	}

	// Create main events table
	createMainTable := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			id VARCHAR(255) PRIMARY KEY,
			event_type VARCHAR(100) NOT NULL,
			name VARCHAR(255) NOT NULL,
			description TEXT,
			status VARCHAR(50) NOT NULL,
			start_time TIMESTAMP NULL,
			end_time TIMESTAMP NULL,
			config JSON,
			participants JSON,
			state JSON,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			INDEX idx_status (status),
			INDEX idx_event_type (event_type),
			INDEX idx_start_time (start_time),
			INDEX idx_end_time (end_time)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
	`, dp.tableName)

	if _, err := dp.db.Exec(createMainTable); err != nil {
		return fmt.Errorf("failed to create main table: %w", err)
	}

	// Create backup table
	createBackupTable := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			id VARCHAR(255),
			event_type VARCHAR(100) NOT NULL,
			name VARCHAR(255) NOT NULL,
			description TEXT,
			status VARCHAR(50) NOT NULL,
			start_time TIMESTAMP NULL,
			end_time TIMESTAMP NULL,
			config JSON,
			participants JSON,
			state JSON,
			backup_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			original_updated_at TIMESTAMP,
			INDEX idx_backup_time (backup_time),
			INDEX idx_event_id (id),
			INDEX idx_status (status)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
	`, dp.backupTable)

	if _, err := dp.db.Exec(createBackupTable); err != nil {
		return fmt.Errorf("failed to create backup table: %w", err)
	}

	return nil
}

// SaveEventStates saves event states to the database
func (dp *DatabasePersistence) SaveEventStates(eventStates []EventStateData) error {
	if !dp.enabled {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	tx, err := dp.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// First, backup existing records
	if err := dp.backupExistingRecords(ctx, tx); err != nil {
		slog.Warn("Failed to backup existing records", "error", err)
	}

	// Clear existing records
	if _, err := tx.ExecContext(ctx, fmt.Sprintf("DELETE FROM %s", dp.tableName)); err != nil {
		return fmt.Errorf("failed to clear existing records: %w", err)
	}

	// Insert new records
	insertQuery := fmt.Sprintf(`
		INSERT INTO %s (id, event_type, name, description, status, start_time, end_time, config, participants, state)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`, dp.tableName)

	stmt, err := tx.PrepareContext(ctx, insertQuery)
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement: %w", err)
	}
	defer stmt.Close()

	for _, state := range eventStates {
		configJSON, _ := json.Marshal(state.Config)
		participantsJSON, _ := json.Marshal(state.Participants)
		stateJSON, _ := json.Marshal(state.State)

		var startTime, endTime *time.Time
		if !state.StartTime.IsZero() {
			startTime = &state.StartTime
		}
		if !state.EndTime.IsZero() {
			endTime = &state.EndTime
		}

		_, err := stmt.ExecContext(ctx,
			state.ID,
			string(state.Type),
			state.Name,
			state.Description,
			string(state.Status),
			startTime,
			endTime,
			string(configJSON),
			string(participantsJSON),
			string(stateJSON),
		)
		if err != nil {
			return fmt.Errorf("failed to insert event state %s: %w", state.ID, err)
		}
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	slog.Debug("Event states saved to database", "count", len(eventStates))
	return nil
}

// LoadEventStates loads event states from the database
func (dp *DatabasePersistence) LoadEventStates() ([]EventStateData, error) {
	if !dp.enabled {
		return []EventStateData{}, nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	query := fmt.Sprintf(`
		SELECT id, event_type, name, description, status, start_time, end_time, config, participants, state
		FROM %s
		ORDER BY updated_at DESC
	`, dp.tableName)

	rows, err := dp.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query event states: %w", err)
	}
	defer rows.Close()

	var eventStates []EventStateData
	for rows.Next() {
		var state EventStateData
		var configJSON, participantsJSON, stateJSON string
		var startTime, endTime sql.NullTime

		err := rows.Scan(
			&state.ID,
			&state.Type,
			&state.Name,
			&state.Description,
			&state.Status,
			&startTime,
			&endTime,
			&configJSON,
			&participantsJSON,
			&stateJSON,
		)
		if err != nil {
			slog.Warn("Failed to scan event state row", "error", err)
			continue
		}

		// Parse timestamps
		if startTime.Valid {
			state.StartTime = startTime.Time
		}
		if endTime.Valid {
			state.EndTime = endTime.Time
		}

		// Parse JSON fields
		if err := json.Unmarshal([]byte(configJSON), &state.Config); err != nil {
			slog.Warn("Failed to unmarshal config JSON", "event_id", state.ID, "error", err)
		}
		if err := json.Unmarshal([]byte(participantsJSON), &state.Participants); err != nil {
			slog.Warn("Failed to unmarshal participants JSON", "event_id", state.ID, "error", err)
		}
		if err := json.Unmarshal([]byte(stateJSON), &state.State); err != nil {
			slog.Warn("Failed to unmarshal state JSON", "event_id", state.ID, "error", err)
		}

		eventStates = append(eventStates, state)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	slog.Debug("Event states loaded from database", "count", len(eventStates))
	return eventStates, nil
}

// backupExistingRecords backs up existing records before overwriting
func (dp *DatabasePersistence) backupExistingRecords(ctx context.Context, tx *sql.Tx) error {
	backupQuery := fmt.Sprintf(`
		INSERT INTO %s (id, event_type, name, description, status, start_time, end_time, config, participants, state, original_updated_at)
		SELECT id, event_type, name, description, status, start_time, end_time, config, participants, state, updated_at
		FROM %s
	`, dp.backupTable, dp.tableName)

	_, err := tx.ExecContext(ctx, backupQuery)
	return err
}

// CleanupOldBackups removes old backup records
func (dp *DatabasePersistence) CleanupOldBackups(maxAge time.Duration) error {
	if !dp.enabled {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cutoffTime := time.Now().Add(-maxAge)
	query := fmt.Sprintf("DELETE FROM %s WHERE backup_time < ?", dp.backupTable)

	result, err := dp.db.ExecContext(ctx, query, cutoffTime)
	if err != nil {
		return fmt.Errorf("failed to cleanup old backups: %w", err)
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected > 0 {
		slog.Info("Cleaned up old backup records", "rows_deleted", rowsAffected)
	}

	return nil
}

// GetStats returns database persistence statistics
func (dp *DatabasePersistence) GetStats() map[string]interface{} {
	stats := map[string]interface{}{
		"enabled": dp.enabled,
	}

	if !dp.enabled {
		return stats
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Count main table records
	var mainCount int
	if err := dp.db.QueryRowContext(ctx, fmt.Sprintf("SELECT COUNT(*) FROM %s", dp.tableName)).Scan(&mainCount); err == nil {
		stats["main_records"] = mainCount
	}

	// Count backup table records
	var backupCount int
	if err := dp.db.QueryRowContext(ctx, fmt.Sprintf("SELECT COUNT(*) FROM %s", dp.backupTable)).Scan(&backupCount); err == nil {
		stats["backup_records"] = backupCount
	}

	// Get last update time
	var lastUpdate sql.NullTime
	if err := dp.db.QueryRowContext(ctx, fmt.Sprintf("SELECT MAX(updated_at) FROM %s", dp.tableName)).Scan(&lastUpdate); err == nil && lastUpdate.Valid {
		stats["last_update"] = lastUpdate.Time
	}

	return stats
}

// Close closes the database connection
func (dp *DatabasePersistence) Close() error {
	if dp.enabled && dp.db != nil {
		return dp.db.Close()
	}
	return nil
}

// IsEnabled returns whether database persistence is enabled
func (dp *DatabasePersistence) IsEnabled() bool {
	return dp.enabled
}
