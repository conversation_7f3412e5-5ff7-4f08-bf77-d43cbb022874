# Event System Documentation

## Overview

The Event System is a comprehensive, extensible framework for managing server-wide events in your Minecraft server. It provides a flexible foundation for creating, managing, and tracking various types of events, with built-in support for the Lucky Block event and easy extensibility for future events.

## Features

- **Flexible Event Framework**: Extensible base system for creating custom events
- **Lucky Block Event**: Complete implementation with mining detection, sponge spawning, and random rewards
- **Score Tracking**: Persistent player scores with database integration
- **API Management**: RESTful API endpoints for external event management
- **Configuration System**: JSON-based configuration with hot-reloading
- **In-Game Commands**: Comprehensive command system for administrators and players
- **Real-time Scoreboard**: Dynamic scoreboard updates showing event progress

## Event Types

### Lucky Block Event (`lucky_block`)

Players mine designated "lucky blocks" to find magical sponges that spawn nearby. Interacting with these sponges provides random rewards and points.

**Features:**
- Configurable block types that count as "lucky blocks"
- Percentage-based sponge spawning (default: 15% chance)
- Random reward system with rarity tiers
- Score tracking (1 point per block, 5 points per sponge, 10 points per reward)
- Visual effects and particles
- Persistent statistics tracking

**Configuration:**
```json
{
  "sponge_chance": 0.15,
  "reward_chance": 0.8,
  "spawn_radius": 5.0,
  "lucky_blocks": ["minecraft:stone", "minecraft:cobblestone", ...],
  "score_multipliers": {
    "block_mined": 1,
    "sponge_found": 5,
    "reward_received": 10
  }
}
```

### Future Events

The system is designed to easily support additional event types:
- **Double XP Event**: Multiplied experience gains
- **Rare Drops Event**: Increased rare item drop chances
- **Custom Events**: Easy to implement using the base event framework

## API Endpoints

### Event Management
- `GET /api/events` - List all events
- `GET /api/events/active` - List active events
- `GET /api/events/{id}` - Get specific event details
- `POST /api/events` - Create new event
- `PUT /api/events/{id}` - Update event configuration
- `DELETE /api/events/{id}` - Delete event

### Event Control
- `POST /api/events/{id}/start` - Start an event
- `POST /api/events/{id}/stop` - Stop an event
- `POST /api/events/{id}/pause` - Pause an event
- `POST /api/events/{id}/resume` - Resume an event
- `POST /api/events/{id}/restart` - Restart an event

### Participants & Statistics
- `GET /api/events/{id}/participants` - Get event participants
- `GET /api/events/{id}/participants/{uuid}` - Get specific participant
- `GET /api/events/{id}/stats` - Get event statistics
- `GET /api/events/{id}/leaderboard` - Get event leaderboard

## In-Game Commands

### `/event list`
Shows all events and their current status.

### `/event start <event_type>`
Starts an event by type or ID. Requires moderator+ permissions.
```
/event start lucky_block
/event start double_xp
```

### `/event stop <event_id>`
Stops a running event by ID. Requires moderator+ permissions.
```
/event stop abc12345
```

### `/event info [event_id]`
Shows detailed information about active events or a specific event.
```
/event info
/event info abc12345
```

### `/event create <event_type> [duration_minutes]`
Creates a new event instance. Requires moderator+ permissions.
```
/event create lucky_block 30
/event create double_xp 60
```

### `/event score [player_name]`
Shows event scores for yourself or another player.
```
/event score
/event score PlayerName
```

## Configuration

### Main Configuration (`config/events.json`)

The event system uses a JSON configuration file that defines:
- Event settings (duration, cooldown, player limits)
- Reward configurations with chances and amounts
- Custom settings specific to each event type
- Permissions and world restrictions

### Example Configuration Structure:
```json
{
  "lucky_block_event": {
    "enabled": true,
    "duration": 1800000000000,
    "cooldown": 3600000000000,
    "max_players": 0,
    "min_players": 1,
    "auto_start": false,
    "auto_restart": false,
    "rewards": [...],
    "custom_settings": {...}
  }
}
```

## Database Integration

### Player Data Extensions
The system extends the existing player data structure:
```go
type Stats struct {
    // ... existing fields
    EventScores map[string]int `json:"event_scores"` // Event ID -> Score
}
```

### Persistent Storage
- Event scores are automatically saved to the database
- Player statistics persist across server restarts
- Event configurations can be hot-reloaded

## Development Guide

### Creating Custom Events

1. **Define Event Type**:
```go
const EventTypeCustom EventType = "custom_event"
```

2. **Implement Event Interface**:
```go
type CustomEvent struct {
    *BaseEvent
    // Custom fields
}

func (e *CustomEvent) OnBlockBreak(ctx *player.Context, pos cube.Pos, block world.Block, drops *[]item.Stack, xp *int) bool {
    // Custom logic
    return false
}
```

3. **Register with Factory**:
```go
factory.RegisterCreator(EventTypeCustom, func(config EventConfig) (Event, error) {
    return NewCustomEvent(config), nil
})
```

### Event Lifecycle

1. **Creation**: Events are created from configuration
2. **Registration**: Events are registered with the event manager
3. **Activation**: Events can be started manually or automatically
4. **Execution**: Events handle game events (block breaks, player joins, etc.)
5. **Completion**: Events end based on duration or manual stopping

## Integration Points

### Block Break Handler
The system integrates with the existing faction handler:
```go
func (h FactionHandler) HandleBlockBreak(ctx *player.Context, pos cube.Pos, drops *[]item.Stack, xp *int) {
    // ... existing logic
    
    // Handle events
    blockBroken := pl.Tx().Block(pos)
    events.GetManager().HandleBlockBreak(ctx, pos, blockBroken, drops, xp)
    
    // ... rest of logic
}
```

### Player Join/Leave
Events are notified when players join or leave the server.

### Scoreboard Integration
The scoreboard system automatically shows event information when events are active.

## Performance Considerations

- Events are processed in priority order (highest first)
- Event handlers can return early to prevent lower-priority events from processing
- Background ticker runs every second for event expiration checks
- Configuration is cached in memory for fast access

## Security

- API endpoints require JWT authentication
- In-game commands require appropriate rank permissions
- Event creation and management restricted to moderators+
- Input validation on all configuration changes

## Troubleshooting

### Common Issues

1. **Events not starting**: Check configuration file syntax and permissions
2. **Scores not saving**: Verify database connection and player data structure
3. **Commands not working**: Ensure proper rank permissions
4. **API errors**: Check JWT authentication and request format

### Logging

The system provides comprehensive logging:
- Event creation and registration
- Event state changes (start/stop/pause)
- Player participation
- Reward distribution
- Configuration changes

## Future Enhancements

- Web dashboard for event management
- Scheduled event system
- Event templates and presets
- Advanced statistics and analytics
- Integration with external systems
- Multi-world event support
