package events

import (
	"fmt"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/bossbar"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"log/slog"
	"math/rand"
	eventBlocks "server/server/blocks/events"
	"server/server"
	"server/server/utils"
	"time"
)

// PlayerDataManager interface for managing player data without circular imports
type PlayerDataManager interface {
	AddEventScore(playerUUID string, eventID string, score int)
	GiveMoney(playerUUID string, amount float64)
	GiveItem(playerUUID string, itemName string, amount int) error
}

var playerDataManager PlayerDataManager

// RegisterPlayerDataManager registers the player data manager
func RegisterPlayerDataManager(manager <PERSON><PERSON><PERSON><PERSON>ana<PERSON>) {
	playerDataManager = manager
}

// LuckyBlockEvent implements the lucky block mining event
type LuckyBlockEvent struct {
	*BaseEvent
	spongeChance    float64
	rewardChance    float64
	spawnRadius     float64
	luckyBlocks     map[string]world.Block // Block types that count as lucky blocks
	rewardPool      []LuckyBlockReward
	participantData map[string]*LuckyBlockParticipant
	playerBossBars  map[string]bossbar.BossBar // Track boss bars for each player
}

// LuckyBlockParticipant extends EventParticipant with lucky block specific data
type LuckyBlockParticipant struct {
	*EventParticipant
	BlocksMined   int                    `json:"blocks_mined"`
	SpongesFound  int                    `json:"sponges_found"`
	RewardsGained []LuckyBlockReward     `json:"rewards_gained"`
	LastMineTime  time.Time              `json:"last_mine_time"`
	Streaks       map[string]interface{} `json:"streaks"`
}

// LuckyBlockReward represents a reward from mining lucky blocks
type LuckyBlockReward struct {
	Type        string      `json:"type"`
	Item        string      `json:"item"`
	Amount      int         `json:"amount"`
	Chance      float64     `json:"chance"`
	Message     string      `json:"message"`
	Command     string      `json:"command"`
	Rarity      string      `json:"rarity"`
	Value       interface{} `json:"value"`
	Timestamp   time.Time   `json:"timestamp"`
}

// NewLuckyBlockEvent creates a new lucky block event
func NewLuckyBlockEvent(config EventConfig) *LuckyBlockEvent {
	baseEvent := NewBaseEvent(
		EventTypeLuckyBlock,
		"Lucky Block Event",
		"Mine lucky blocks to find sponges with random rewards and earn points!",
		PriorityNormal,
		config,
	)

	event := &LuckyBlockEvent{
		BaseEvent:       baseEvent,
		spongeChance:    0.15, // 15% chance by default
		rewardChance:    0.8,  // 80% chance for rewards from sponges
		spawnRadius:     5.0,  // 5 block radius around player
		luckyBlocks:     make(map[string]world.Block),
		participantData: make(map[string]*LuckyBlockParticipant),
		playerBossBars:  make(map[string]bossbar.BossBar),
	}

	// Initialize lucky blocks (blocks that count for the event)
	event.initializeLuckyBlocks()
	
	// Initialize reward pool
	event.initializeRewardPool()
	
	// Apply custom settings from config
	event.applyCustomSettings()

	return event
}

// initializeLuckyBlocks sets up which blocks count as "lucky blocks"
func (e *LuckyBlockEvent) initializeLuckyBlocks() {
	// Add various blocks that can be considered "lucky blocks"
	luckyBlockTypes := []world.Block{
		block.Stone{},
		block.Cobblestone{},
		block.Dirt{},
		block.Sand{},
		block.Gravel{},
		block.Coal{},
		block.Iron{},
		block.Gold{},
		block.Diamond{},
		block.Emerald{},
		block.Lapis{},
	}

	for _, blockType := range luckyBlockTypes {
		name, _ := blockType.EncodeBlock()
		e.luckyBlocks[name] = blockType
	}
}

// initializeRewardPool sets up the default reward pool
func (e *LuckyBlockEvent) initializeRewardPool() {
	e.rewardPool = []LuckyBlockReward{
		// Common rewards (high chance)
		{Type: "item", Item: "minecraft:diamond", Amount: 1, Chance: 0.3, Rarity: "common", Message: "You found a diamond!"},
		{Type: "item", Item: "minecraft:emerald", Amount: 2, Chance: 0.25, Rarity: "common", Message: "You found emeralds!"},
		{Type: "item", Item: "minecraft:gold_ingot", Amount: 3, Chance: 0.2, Rarity: "common", Message: "You found gold!"},
		{Type: "money", Amount: 100, Chance: 0.15, Rarity: "common", Message: "You found 100 doubloons!"},
		
		// Uncommon rewards (medium chance)
		{Type: "item", Item: "minecraft:diamond", Amount: 3, Chance: 0.1, Rarity: "uncommon", Message: "You found multiple diamonds!"},
		{Type: "money", Amount: 250, Chance: 0.08, Rarity: "uncommon", Message: "You found 250 doubloons!"},
		{Type: "item", Item: "minecraft:enchanted_book", Amount: 1, Chance: 0.07, Rarity: "uncommon", Message: "You found an enchanted book!"},
		
		// Rare rewards (low chance)
		{Type: "money", Amount: 500, Chance: 0.03, Rarity: "rare", Message: "You struck it rich! 500 doubloons!"},
		{Type: "item", Item: "minecraft:diamond", Amount: 5, Chance: 0.02, Rarity: "rare", Message: "JACKPOT! Multiple diamonds!"},
		{Type: "command", Command: "give {player} minecraft:netherite_ingot 1", Chance: 0.01, Rarity: "legendary", Message: "LEGENDARY! You found netherite!"},
	}
}

// applyCustomSettings applies custom settings from the event configuration
func (e *LuckyBlockEvent) applyCustomSettings() {
	if settings, ok := e.config.CustomSettings["sponge_chance"].(float64); ok {
		e.spongeChance = settings
	}
	if settings, ok := e.config.CustomSettings["reward_chance"].(float64); ok {
		e.rewardChance = settings
	}
	if settings, ok := e.config.CustomSettings["spawn_radius"].(float64); ok {
		e.spawnRadius = settings
	}

	// Apply lucky blocks from configuration
	if luckyBlocksInterface, ok := e.config.CustomSettings["lucky_blocks"]; ok {
		if luckyBlocksSlice, ok := luckyBlocksInterface.([]interface{}); ok {
			e.luckyBlocks = make(map[string]world.Block)
			for _, blockInterface := range luckyBlocksSlice {
				if blockName, ok := blockInterface.(string); ok {
					// For now, we'll use the block name as the key
					// In a real implementation, you'd want to resolve the actual block type
					e.luckyBlocks[blockName] = nil // Placeholder
				}
			}
		}
	}

	// Apply rewards from configuration
	if len(e.config.Rewards) > 0 {
		e.rewardPool = make([]LuckyBlockReward, len(e.config.Rewards))
		for i, rewardConfig := range e.config.Rewards {
			e.rewardPool[i] = LuckyBlockReward{
				Type:    string(rewardConfig.Type),
				Item:    rewardConfig.Item,
				Amount:  rewardConfig.MinAmount, // For simplicity, using min amount
				Chance:  rewardConfig.Chance,
				Message: rewardConfig.Message,
				Command: rewardConfig.Command,
			}

			// Set rarity from conditions
			if rarity, ok := rewardConfig.Conditions["rarity"].(string); ok {
				e.rewardPool[i].Rarity = rarity
			}
		}
	}
}

// OnBlockBreak handles block breaking during the lucky block event
func (e *LuckyBlockEvent) OnBlockBreak(ctx *player.Context, pos cube.Pos, block world.Block, drops *[]item.Stack, xp *int) bool {
	// Only process if event is active
	if e.Status() != EventStatusActive {
		return false
	}

	pl := ctx.Val()
	playerUUID := pl.UUID().String()

	// Random chance for any block to be a "lucky block" (5% chance)
	luckyBlockChance := 5.0 // 5% chance
	if !utils.RandChance(luckyBlockChance) {
		return false // Not a lucky block this time
	}

	// Get or create participant data
	participant := e.getOrCreateParticipant(pl)
	if participant == nil {
		return false
	}

	// Update participant stats
	participant.BlocksMined++
	participant.LastMineTime = time.Now()

	// Add score for mining a lucky block (+1 point only)
	e.AddScore(playerUUID, 1)
	participant.Score++
	if playerDataManager != nil {
		playerDataManager.AddEventScore(playerUUID, e.ID(), 1)
	}

	// Send lucky block message with proper color codes
	pl.Message("§6✨ Lucky Block! §a+1 point")

	// Check if we should spawn a sponge (15% chance when lucky block is triggered)
	if utils.RandChance(e.spongeChance * 100) {
		e.spawnSpongeNearPlayer(pl, pos)
		participant.SpongesFound++

		// No extra score for sponge spawn, just the notification
		pl.Message("§2✨ Lucky! A mysterious sponge appeared nearby!")
		slog.Info("Lucky block sponge spawned", "player", pl.Name(), "position", pos)

		// Update boss bar to show new sponge count
		e.updateBossBarForPlayer(pl)
	}

	// Update boss bar for this player
	e.updateBossBarForPlayer(pl)

	return false // Don't prevent normal block breaking
}

// OnPlayerJoin handles player joining the lucky block event with boss bar
func (e *LuckyBlockEvent) OnPlayerJoin(pl *player.Player) bool {
	// Only allow joining if event is active
	if e.Status() != EventStatusActive {
		return false
	}

	// Call base implementation first
	if !e.BaseEvent.OnPlayerJoin(pl) {
		return false
	}

	// Create and show boss bar for this player
	e.createBossBarForPlayer(pl)

	// Start boss bar updater if this is the first participant
	if len(e.participantData) == 1 {
		go e.startBossBarUpdater()
	}

	// Send welcome message with proper color codes
	pl.Message("§6✨ Welcome to the Lucky Block Event! ✨")
	pl.Message("§eMine any block for a chance to find lucky rewards!")
	pl.Message("§aYour progress is shown in the boss bar above.")
	pl.Message(fmt.Sprintf("§7Event ID: §f%s", e.ID()))

	return true
}

// OnPlayerLeave handles player leaving the lucky block event
func (e *LuckyBlockEvent) OnPlayerLeave(pl *player.Player) bool {
	playerUUID := pl.UUID().String()

	// Remove boss bar if it exists
	if _, exists := e.playerBossBars[playerUUID]; exists {
		pl.RemoveBossBar()
		delete(e.playerBossBars, playerUUID)
	}

	// Call base implementation
	return e.BaseEvent.OnPlayerLeave(pl)
}

// createBossBarForPlayer creates and displays a boss bar for the player
func (e *LuckyBlockEvent) createBossBarForPlayer(pl *player.Player) {
	playerUUID := pl.UUID().String()

	// Remove existing boss bar if any
	if _, exists := e.playerBossBars[playerUUID]; exists {
		pl.RemoveBossBar()
	}

	// Create new boss bar
	bar := bossbar.New("§6Lucky Block Event §7| §aScore: 0 §7| §eTime: Loading...").WithColour(bossbar.Purple()).WithHealthPercentage(1.0)

	// Store the boss bar
	e.playerBossBars[playerUUID] = bar

	// Send boss bar to player
	pl.SendBossBar(bar)

	// Update the boss bar immediately
	e.updateBossBarForPlayer(pl)
}

// updateBossBarForPlayer updates the boss bar content for a specific player
func (e *LuckyBlockEvent) updateBossBarForPlayer(pl *player.Player) {
	playerUUID := pl.UUID().String()

	_, exists := e.playerBossBars[playerUUID]
	if !exists {
		return
	}

	// Get participant data
	participant := e.participantData[playerUUID]
	score := 0
	spongesFound := 0
	if participant != nil {
		score = participant.Score
		spongesFound = participant.SpongesFound
	}

	// Calculate time remaining
	remaining := e.RemainingTime()
	timeStr := "∞"
	progress := float64(1.0)

	if remaining > 0 {
		if remaining < time.Minute {
			timeStr = fmt.Sprintf("%ds", int(remaining.Seconds()))
		} else {
			timeStr = fmt.Sprintf("%dm %ds", int(remaining.Minutes()), int(remaining.Seconds())%60)
		}

		// Calculate progress (1.0 = full, 0.0 = empty)
		if e.Duration() > 0 {
			elapsed := e.Duration() - remaining
			progress = 1.0 - (float64(elapsed) / float64(e.Duration()))
			if progress < 0 {
				progress = 0
			}
		}
	}

	// Update boss bar text and progress
	text := fmt.Sprintf("§6Lucky Block Event §7| §aScore: %d §7| §bSponges: %d §7| §eTime: %s",
		score, spongesFound, timeStr)

	// Create updated boss bar and send to player
	updatedBar := bossbar.New(text).WithColour(bossbar.Purple()).WithHealthPercentage(progress)
	e.playerBossBars[playerUUID] = updatedBar
	pl.SendBossBar(updatedBar)
}

// startBossBarUpdater starts a goroutine to update boss bars periodically
func (e *LuckyBlockEvent) startBossBarUpdater() {
	ticker := time.NewTicker(5 * time.Second) // Update every 5 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// Stop updating if event is not active
			if e.Status() != EventStatusActive {
				// Clean up boss bars when event ends
				e.cleanupAllBossBars()
				return
			}
			e.updateAllBossBars()
		}
	}
}

// updateAllBossBars updates boss bars for all participants
func (e *LuckyBlockEvent) updateAllBossBars() {
	// Update boss bars for all active participants
	if server.MCServer != nil {
		for pl := range server.MCServer.Players(nil) {
			playerUUID := pl.UUID().String()
			if _, exists := e.playerBossBars[playerUUID]; exists {
				e.updateBossBarForPlayer(pl)
			}
		}
	}
}

// cleanupAllBossBars removes all boss bars when event ends
func (e *LuckyBlockEvent) cleanupAllBossBars() {
	// Notify all players that the event has ended and remove boss bars
	if server.MCServer != nil {
		for pl := range server.MCServer.Players(nil) {
			playerUUID := pl.UUID().String()
			if _, exists := e.playerBossBars[playerUUID]; exists {
				pl.Message("§6[EVENT] §cLucky Block Event has ended!")
				pl.RemoveBossBar()
			}
		}
	}

	// Clear the boss bar tracking
	e.playerBossBars = make(map[string]bossbar.BossBar)
	slog.Info("Cleaned up boss bars for ended event", "event_id", e.ID())
}

// cleanupEventSponges removes any remaining sponges from this event
func (e *LuckyBlockEvent) cleanupEventSponges() {
	// This would need to integrate with the sponge system to remove sponges
	// belonging to this specific event ID
	slog.Info("Cleaned up event sponges", "event_id", e.ID())
}

// spawnSpongeNearPlayer spawns a sponge block near the player
func (e *LuckyBlockEvent) spawnSpongeNearPlayer(pl *player.Player, originalPos cube.Pos) {
	playerPos := pl.Position()

	// Try to find a suitable position near the player (closer range)
	for attempts := 0; attempts < 15; attempts++ {
		// Random position within smaller spawn radius (2-4 blocks)
		radius := 2.0 + rand.Float64() * 2.0 // 2-4 block radius
		offsetX := (rand.Float64() - 0.5) * 2 * radius
		offsetZ := (rand.Float64() - 0.5) * 2 * radius
		offsetY := rand.Float64() * 2 - 1 // -1 to +1 blocks vertically

		spawnPos := cube.PosFromVec3(playerPos.Add(mgl64.Vec3{offsetX, offsetY, offsetZ}))

		// Check if the position is suitable (air or replaceable block)
		currentBlock := pl.Tx().Block(spawnPos)
		if _, isAir := currentBlock.(block.Air); isAir || e.isReplaceableBlock(currentBlock) {
			// Place the lucky sponge block
			luckySponge := eventBlocks.NewLuckySponge(e.ID(), pl.UUID().String())
			pl.Tx().SetBlock(spawnPos, luckySponge, nil)

			// Store the sponge data
			eventBlocks.PlaceLuckySponge(spawnPos, e.ID(), pl.UUID().String())

			// Add some visual effects
			e.addSpongeEffects(pl, spawnPos)

			// Send location hint to player
			direction := e.getDirectionToPlayer(playerPos, spawnPos.Vec3())
			pl.Message(fmt.Sprintf("§b💎 Sponge spawned %s! Look around!", direction))

			// Successfully placed sponge, exit loop
			return
		}
	}

	// If we couldn't place a sponge after all attempts, notify player
	pl.Message("§c✨ Lucky sponge tried to spawn but couldn't find a good spot!")
}

// getDirectionToPlayer returns a simple direction string from player to target position
func (e *LuckyBlockEvent) getDirectionToPlayer(playerPos mgl64.Vec3, targetPos mgl64.Vec3) string {
	diff := targetPos.Sub(playerPos)

	// Determine primary direction
	if abs(diff.X()) > abs(diff.Z()) {
		if diff.X() > 0 {
			return "to your east"
		} else {
			return "to your west"
		}
	} else {
		if diff.Z() > 0 {
			return "to your south"
		} else {
			return "to your north"
		}
	}
}

// abs returns absolute value of float64
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}

// isReplaceableBlock checks if a block can be replaced by a sponge
func (e *LuckyBlockEvent) isReplaceableBlock(b world.Block) bool {
	switch b.(type) {
	case block.Grass, block.Dirt, block.Sand, block.Gravel:
		return true
	default:
		return false
	}
}

// addSpongeEffects adds visual and audio effects when a sponge spawns
func (e *LuckyBlockEvent) addSpongeEffects(pl *player.Player, pos cube.Pos) {
	// Add particle effects around the sponge
	spawnPos := pos.Vec3()
	for i := 0; i < 10; i++ {
		particlePos := spawnPos.Add(mgl64.Vec3{
			(rand.Float64() - 0.5) * 2,
			rand.Float64() * 2,
			(rand.Float64() - 0.5) * 2,
		})
		utils.SpawnParticle(pl, particlePos, "minecraft:villager_happy")
	}
}

// OnBlockPlace handles when players interact with sponge blocks
func (e *LuckyBlockEvent) OnBlockPlace(ctx *player.Context, pos cube.Pos, block world.Block) bool {
	// This event doesn't handle block placement
	return false
}

// Custom method to handle sponge interaction (this would be called from a custom sponge block handler)
func (e *LuckyBlockEvent) HandleSpongeInteraction(pl *player.Player, pos cube.Pos) {
	// Only process if event is active
	if e.Status() != EventStatusActive {
		pl.Message("§cThis event has ended!")
		return
	}

	participant := e.getOrCreateParticipant(pl)
	if participant == nil {
		return
	}

	// Check if we should give a reward
	if utils.RandChance(e.rewardChance * 100) {
		reward := e.selectRandomReward()
		if reward != nil {
			e.giveReward(pl, *reward, participant)
		}
	} else {
		pl.Message("§eThe sponge crumbles away... better luck next time!")
	}

	// Remove the sponge block
	pl.Tx().SetBlock(pos, block.Air{}, nil)
}

// selectRandomReward selects a random reward from the reward pool
func (e *LuckyBlockEvent) selectRandomReward() *LuckyBlockReward {
	totalChance := 0.0
	for _, reward := range e.rewardPool {
		totalChance += reward.Chance
	}

	if totalChance == 0 {
		return nil
	}

	randomValue := rand.Float64() * totalChance
	currentChance := 0.0

	for _, reward := range e.rewardPool {
		currentChance += reward.Chance
		if randomValue <= currentChance {
			rewardCopy := reward
			rewardCopy.Timestamp = time.Now()
			return &rewardCopy
		}
	}

	return nil
}

// giveReward gives a reward to the player
func (e *LuckyBlockEvent) giveReward(pl *player.Player, reward LuckyBlockReward, participant *LuckyBlockParticipant) {
	playerUUID := pl.UUID().String()

	switch reward.Type {
	case "item":
		// Give item reward
		if reward.Item != "" && playerDataManager != nil {
			err := playerDataManager.GiveItem(playerUUID, reward.Item, reward.Amount)
			if err == nil {
				pl.Message(fmt.Sprintf("§a%s", reward.Message))
			} else {
				pl.Message("§eCould not give item reward.")
			}
		}
	case "money":
		// Give money reward
		if reward.Amount > 0 && playerDataManager != nil {
			playerDataManager.GiveMoney(playerUUID, float64(reward.Amount))
			pl.Message(fmt.Sprintf("§6%s", reward.Message))
		}
	case "command":
		// Execute command reward
		if reward.Command != "" {
			// This would need to be implemented to execute server commands
			pl.Message(fmt.Sprintf("§d%s", reward.Message))
		}
	}

	// Add reward to participant's history
	participant.RewardsGained = append(participant.RewardsGained, reward)

	// Add small bonus score for getting a reward (+1 point only)
	e.AddScore(pl.UUID().String(), 1)
	participant.Score += 1
	if playerDataManager != nil {
		playerDataManager.AddEventScore(pl.UUID().String(), e.ID(), 1)
	}

	// Update boss bar to reflect new score
	e.updateBossBarForPlayer(pl)

	// Log the reward
	slog.Info("Lucky block reward given", 
		"player", pl.Name(), 
		"reward_type", reward.Type, 
		"reward_amount", reward.Amount,
		"rarity", reward.Rarity)
}

// getOrCreateParticipant gets or creates participant data for a player
func (e *LuckyBlockEvent) getOrCreateParticipant(pl *player.Player) *LuckyBlockParticipant {
	playerUUID := pl.UUID().String()

	// Get or create lucky block specific data first
	if luckyParticipant, exists := e.participantData[playerUUID]; exists {
		return luckyParticipant
	}

	// Check if player is already a participant in the base event
	baseParticipant := e.GetParticipant(playerUUID)
	if baseParticipant == nil {
		// Create new participant
		baseParticipant = &EventParticipant{
			PlayerUUID: playerUUID,
			PlayerName: pl.Name(),
			JoinTime:   time.Now(),
			Score:      0,
			Rewards:    make([]RewardInstance, 0),
			Stats:      make(map[string]interface{}),
		}
		e.AddParticipant(baseParticipant)
	}

	// Create new lucky block participant data
	luckyParticipant := &LuckyBlockParticipant{
		EventParticipant: baseParticipant,
		BlocksMined:      0,
		SpongesFound:     0,
		RewardsGained:    make([]LuckyBlockReward, 0),
		LastMineTime:     time.Now(),
		Streaks:          make(map[string]interface{}),
	}

	e.participantData[playerUUID] = luckyParticipant
	return luckyParticipant
}

// Stop overrides the base Stop method to clean up boss bars and notify players
func (e *LuckyBlockEvent) Stop() error {
	// Broadcast event end message to all players
	if server.MCServer != nil {
		for pl := range server.MCServer.Players(nil) {
			pl.Message(fmt.Sprintf("§6[EVENT] §cLucky Block Event %s has ended!", e.ID()))
			pl.RemoveBossBar() // Remove any boss bars
		}
	}

	// Save final scores to player database before cleanup
	for _, participant := range e.participantData {
		if participant != nil {
			// TODO: Save final score to player database
			// This would integrate with the user package to save EventScores
			slog.Info("Event ended for player",
				"player", participant.PlayerName,
				"final_score", participant.Score,
				"blocks_mined", participant.BlocksMined,
				"sponges_found", participant.SpongesFound)
		}
	}

	// Clean up all boss bars
	for uuid := range e.playerBossBars {
		delete(e.playerBossBars, uuid)
	}

	// Clear all participant data
	e.participantData = make(map[string]*LuckyBlockParticipant)

	// Clean up any remaining sponges from this event
	e.cleanupEventSponges()

	slog.Info("Lucky Block Event stopped", "event_id", e.ID(), "participants", len(e.GetParticipants()))

	// Call base implementation
	return e.BaseEvent.Stop()
}

// GetLuckyBlockStats returns lucky block specific stats for a player
func (e *LuckyBlockEvent) GetLuckyBlockStats(playerUUID string) *LuckyBlockParticipant {
	return e.participantData[playerUUID]
}

// GetTopMiners returns the top miners in the event
func (e *LuckyBlockEvent) GetTopMiners(limit int) []*LuckyBlockParticipant {
	participants := make([]*LuckyBlockParticipant, 0, len(e.participantData))
	for _, participant := range e.participantData {
		participants = append(participants, participant)
	}

	// Sort by score (descending)
	for i := 0; i < len(participants)-1; i++ {
		for j := i + 1; j < len(participants); j++ {
			if participants[i].Score < participants[j].Score {
				participants[i], participants[j] = participants[j], participants[i]
			}
		}
	}

	if limit > 0 && limit < len(participants) {
		participants = participants[:limit]
	}

	return participants
}


