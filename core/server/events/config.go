package events

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"time"
)

// ConfigManager handles loading and saving event configurations
type ConfigManager struct {
	configPath string
	configs    map[string]EventConfig
}

// NewConfigManager creates a new configuration manager
func NewConfigManager(configPath string) *ConfigManager {
	return &ConfigManager{
		configPath: configPath,
		configs:    make(map[string]EventConfig),
	}
}

// LoadConfigs loads event configurations from the config file
func (cm *ConfigManager) LoadConfigs() error {
	// Ensure the config directory exists
	configDir := filepath.Dir(cm.configPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	// Check if config file exists
	if _, err := os.Stat(cm.configPath); os.IsNotExist(err) {
		slog.Info("Config file does not exist, creating default configuration", "path", cm.configPath)
		return cm.createDefaultConfig()
	}

	// Read the config file
	data, err := os.ReadFile(cm.configPath)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	// Parse the JSON
	if err := json.Unmarshal(data, &cm.configs); err != nil {
		return fmt.Errorf("failed to parse config file: %w", err)
	}

	slog.Info("Event configurations loaded successfully", "count", len(cm.configs))
	return nil
}

// SaveConfigs saves the current configurations to the config file
func (cm *ConfigManager) SaveConfigs() error {
	data, err := json.MarshalIndent(cm.configs, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal configs: %w", err)
	}

	if err := os.WriteFile(cm.configPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	slog.Info("Event configurations saved successfully")
	return nil
}

// GetConfig returns the configuration for a specific event type
func (cm *ConfigManager) GetConfig(eventType EventType) (EventConfig, bool) {
	config, exists := cm.configs[string(eventType)]
	return config, exists
}

// SetConfig sets the configuration for a specific event type
func (cm *ConfigManager) SetConfig(eventType EventType, config EventConfig) {
	cm.configs[string(eventType)] = config
}

// GetAllConfigs returns all configurations
func (cm *ConfigManager) GetAllConfigs() map[string]EventConfig {
	// Return a copy to prevent external modification
	configsCopy := make(map[string]EventConfig)
	for k, v := range cm.configs {
		configsCopy[k] = v
	}
	return configsCopy
}

// createDefaultConfig creates a default configuration file
func (cm *ConfigManager) createDefaultConfig() error {
	// Create default configurations for each event type
	cm.configs = map[string]EventConfig{
		string(EventTypeLuckyBlock): {
			Enabled:     true,
			Duration:    30 * time.Minute,
			Cooldown:    1 * time.Hour,
			MaxPlayers:  0, // 0 means unlimited
			MinPlayers:  1,
			AutoStart:   false,
			AutoRestart: false,
			Rewards: []RewardConfig{
				{
					Type:      RewardTypeItem,
					Chance:    0.3,
					MinAmount: 1,
					MaxAmount: 1,
					Item:      "minecraft:diamond",
					Message:   "You found a diamond!",
					Conditions: map[string]interface{}{
						"rarity": "common",
					},
				},
				{
					Type:      RewardTypeItem,
					Chance:    0.25,
					MinAmount: 1,
					MaxAmount: 3,
					Item:      "minecraft:emerald",
					Message:   "You found emeralds!",
					Conditions: map[string]interface{}{
						"rarity": "common",
					},
				},
				{
					Type:      RewardTypeMoney,
					Chance:    0.15,
					MinAmount: 50,
					MaxAmount: 150,
					Message:   "You found doubloons!",
					Conditions: map[string]interface{}{
						"rarity": "common",
					},
				},
			},
			Permissions: []string{},
			Worlds:      []string{},
			CustomSettings: map[string]interface{}{
				"sponge_chance":  0.15,
				"reward_chance":  0.8,
				"spawn_radius":   5.0,
				"lucky_blocks": []string{
					"minecraft:stone",
					"minecraft:cobblestone",
					"minecraft:dirt",
					"minecraft:sand",
					"minecraft:gravel",
					"minecraft:coal_ore",
					"minecraft:iron_ore",
					"minecraft:gold_ore",
					"minecraft:diamond_ore",
					"minecraft:emerald_ore",
				},
				"score_multipliers": map[string]interface{}{
					"block_mined":     1,
					"sponge_found":    5,
					"reward_received": 10,
				},
			},
		},
		string(EventTypeDoubleXP): {
			Enabled:     false,
			Duration:    1 * time.Hour,
			Cooldown:    2 * time.Hour,
			MaxPlayers:  0,
			MinPlayers:  1,
			AutoStart:   false,
			AutoRestart: false,
			Rewards:     []RewardConfig{},
			Permissions: []string{},
			Worlds:      []string{},
			CustomSettings: map[string]interface{}{
				"xp_multiplier": 2.0,
				"applies_to":    []string{"mining", "killing", "crafting"},
			},
		},
		string(EventTypeRareDrops): {
			Enabled:     false,
			Duration:    45 * time.Minute,
			Cooldown:    90 * time.Minute,
			MaxPlayers:  0,
			MinPlayers:  1,
			AutoStart:   false,
			AutoRestart: false,
			Rewards:     []RewardConfig{},
			Permissions: []string{},
			Worlds:      []string{},
			CustomSettings: map[string]interface{}{
				"drop_chance_multiplier": 1.5,
				"rare_items": []string{
					"minecraft:diamond",
					"minecraft:emerald",
					"minecraft:netherite_scrap",
				},
			},
		},
	}

	return cm.SaveConfigs()
}

// ValidateConfig validates an event configuration
func (cm *ConfigManager) ValidateConfig(config EventConfig) error {
	if config.Duration < 0 {
		return fmt.Errorf("duration cannot be negative")
	}

	if config.Cooldown < 0 {
		return fmt.Errorf("cooldown cannot be negative")
	}

	if config.MaxPlayers < 0 {
		return fmt.Errorf("max_players cannot be negative")
	}

	if config.MinPlayers < 0 {
		return fmt.Errorf("min_players cannot be negative")
	}

	if config.MaxPlayers > 0 && config.MinPlayers > config.MaxPlayers {
		return fmt.Errorf("min_players cannot be greater than max_players")
	}

	// Validate rewards
	for i, reward := range config.Rewards {
		if err := cm.validateReward(reward); err != nil {
			return fmt.Errorf("reward %d: %w", i, err)
		}
	}

	return nil
}

// validateReward validates a reward configuration
func (cm *ConfigManager) validateReward(reward RewardConfig) error {
	if reward.Chance < 0 || reward.Chance > 1 {
		return fmt.Errorf("chance must be between 0 and 1")
	}

	if reward.MinAmount < 0 {
		return fmt.Errorf("min_amount cannot be negative")
	}

	if reward.MaxAmount < 0 {
		return fmt.Errorf("max_amount cannot be negative")
	}

	if reward.MaxAmount > 0 && reward.MinAmount > reward.MaxAmount {
		return fmt.Errorf("min_amount cannot be greater than max_amount")
	}

	switch reward.Type {
	case RewardTypeItem:
		if reward.Item == "" {
			return fmt.Errorf("item type rewards must specify an item")
		}
	case RewardTypeCommand:
		if reward.Command == "" {
			return fmt.Errorf("command type rewards must specify a command")
		}
	case RewardTypeMoney, RewardTypeXP:
		if reward.MinAmount == 0 && reward.MaxAmount == 0 {
			return fmt.Errorf("money/xp rewards must specify an amount")
		}
	}

	return nil
}

// ReloadConfigs reloads configurations from the file
func (cm *ConfigManager) ReloadConfigs() error {
	return cm.LoadConfigs()
}

// BackupConfigs creates a backup of the current configuration
func (cm *ConfigManager) BackupConfigs() error {
	backupPath := cm.configPath + ".backup." + time.Now().Format("20060102-150405")
	
	data, err := json.MarshalIndent(cm.configs, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal configs for backup: %w", err)
	}

	if err := os.WriteFile(backupPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write backup file: %w", err)
	}

	slog.Info("Configuration backup created", "path", backupPath)
	return nil
}

// Global configuration manager instance
var globalConfigManager *ConfigManager

// InitializeConfigManager initializes the global configuration manager
func InitializeConfigManager(configPath string) error {
	globalConfigManager = NewConfigManager(configPath)
	return globalConfigManager.LoadConfigs()
}

// GetConfigManager returns the global configuration manager
func GetConfigManager() *ConfigManager {
	return globalConfigManager
}
