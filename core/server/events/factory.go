package events

import (
	"fmt"
)

// DefaultEventFactory is the default implementation of EventFactory
type DefaultEventFactory struct {
	creators map[EventType]func(EventConfig) (Event, error)
}

// NewEventFactory creates a new event factory
func NewEventFactory() *DefaultEventFactory {
	factory := &DefaultEventFactory{
		creators: make(map[EventType]func(EventConfig) (Event, error)),
	}

	// Register default event creators
	factory.RegisterCreator(EventTypeLuckyBlock, factory.createLuckyBlockEvent)
	factory.RegisterCreator(EventTypeDoubleXP, factory.createDoubleXPEvent)
	factory.RegisterCreator(EventTypeRareDrops, factory.createRareDropsEvent)

	return factory
}

// RegisterCreator registers a creator function for a specific event type
func (f *DefaultEventFactory) RegisterCreator(eventType EventType, creator func(EventConfig) (Event, error)) {
	f.creators[eventType] = creator
}

// CreateEvent creates an event of the specified type with the given configuration
func (f *DefaultEventFactory) CreateEvent(eventType EventType, config EventConfig) (Event, error) {
	creator, exists := f.creators[eventType]
	if !exists {
		return nil, fmt.Errorf("unsupported event type: %s", eventType)
	}

	return creator(config)
}

// GetSupportedTypes returns all supported event types
func (f *DefaultEventFactory) GetSupportedTypes() []EventType {
	types := make([]EventType, 0, len(f.creators))
	for eventType := range f.creators {
		types = append(types, eventType)
	}
	return types
}

// Default event creators
func (f *DefaultEventFactory) createLuckyBlockEvent(config EventConfig) (Event, error) {
	// This will be implemented in lucky_block_event.go
	return NewLuckyBlockEvent(config), nil
}

func (f *DefaultEventFactory) createDoubleXPEvent(config EventConfig) (Event, error) {
	// Placeholder for future double XP event
	event := NewBaseEvent(
		EventTypeDoubleXP,
		"Double XP Event",
		"Players receive double experience points",
		PriorityNormal,
		config,
	)
	return event, nil
}

func (f *DefaultEventFactory) createRareDropsEvent(config EventConfig) (Event, error) {
	// Placeholder for future rare drops event
	event := NewBaseEvent(
		EventTypeRareDrops,
		"Rare Drops Event",
		"Increased chance of rare item drops",
		PriorityNormal,
		config,
	)
	return event, nil
}
