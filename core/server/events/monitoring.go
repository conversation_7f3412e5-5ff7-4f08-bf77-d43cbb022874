package events

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// HealthStatus represents the health status of a component
type HealthStatus string

const (
	HealthStatusHealthy   HealthStatus = "healthy"
	HealthStatusDegraded  HealthStatus = "degraded"
	HealthStatusUnhealthy HealthStatus = "unhealthy"
	HealthStatusUnknown   HealthStatus = "unknown"
)

// HealthCheck represents a health check result
type HealthCheck struct {
	Component   string                 `json:"component"`
	Status      HealthStatus           `json:"status"`
	Message     string                 `json:"message"`
	LastCheck   time.Time              `json:"last_check"`
	Duration    time.Duration          `json:"duration"`
	Details     map[string]interface{} `json:"details,omitempty"`
	Error       string                 `json:"error,omitempty"`
}

// MonitoringManager handles monitoring and health checks for the event system
type MonitoringManager struct {
	manager            EventManager
	persistenceManager *PersistenceManager
	recoveryManager    *CrashRecoveryManager
	healthChecks       map[string]*HealthCheck
	mutex              sync.RWMutex
	checkInterval      time.Duration
	stopChan           chan bool
	running            bool
	alertThresholds    map[string]time.Duration
}

// NewMonitoringManager creates a new monitoring manager
func NewMonitoringManager(manager EventManager, pm *PersistenceManager, rm *CrashRecoveryManager) *MonitoringManager {
	return &MonitoringManager{
		manager:            manager,
		persistenceManager: pm,
		recoveryManager:    rm,
		healthChecks:       make(map[string]*HealthCheck),
		checkInterval:      30 * time.Second,
		stopChan:           make(chan bool),
		running:            false,
		alertThresholds: map[string]time.Duration{
			"event_manager":    5 * time.Second,
			"persistence":      10 * time.Second,
			"database":         15 * time.Second,
			"file_system":      5 * time.Second,
			"recovery_system":  5 * time.Second,
		},
	}
}

// Start starts the monitoring system
func (mm *MonitoringManager) Start() error {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	if mm.running {
		return fmt.Errorf("monitoring manager is already running")
	}

	mm.running = true

	// Run initial health checks
	mm.runAllHealthChecks()

	// Start monitoring loop
	go mm.monitoringLoop()

	slog.Info("Event monitoring system started", "check_interval", mm.checkInterval)
	return nil
}

// Stop stops the monitoring system
func (mm *MonitoringManager) Stop() error {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	if !mm.running {
		return nil
	}

	mm.running = false

	// Send stop signal
	select {
	case mm.stopChan <- true:
	default:
		// Channel might be full, continue
	}

	slog.Info("Event monitoring system stopped")
	return nil
}

// monitoringLoop runs the main monitoring loop
func (mm *MonitoringManager) monitoringLoop() {
	ticker := time.NewTicker(mm.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			mm.runAllHealthChecks()
			mm.checkAlerts()
		case <-mm.stopChan:
			return
		}
	}
}

// runAllHealthChecks runs all health checks
func (mm *MonitoringManager) runAllHealthChecks() {
	checks := []func(){
		mm.checkEventManager,
		mm.checkPersistence,
		mm.checkFileSystem,
		mm.checkRecoverySystem,
		mm.checkEventStates,
	}

	for _, check := range checks {
		go check()
	}
}

// checkEventManager checks the health of the event manager
func (mm *MonitoringManager) checkEventManager() {
	start := time.Now()
	check := &HealthCheck{
		Component: "event_manager",
		LastCheck: start,
	}

	defer func() {
		check.Duration = time.Since(start)
		mm.updateHealthCheck(check)
	}()

	if mm.manager == nil {
		check.Status = HealthStatusUnhealthy
		check.Message = "Event manager is not initialized"
		check.Error = "manager is nil"
		return
	}

	// Check if manager is responsive
	events := mm.manager.GetAllEvents()
	activeEvents := mm.manager.GetActiveEvents()

	check.Status = HealthStatusHealthy
	check.Message = "Event manager is healthy"
	check.Details = map[string]interface{}{
		"total_events":  len(events),
		"active_events": len(activeEvents),
		"uptime":        time.Since(start).String(),
	}

	// Check for potential issues
	if len(activeEvents) > 10 {
		check.Status = HealthStatusDegraded
		check.Message = "High number of active events detected"
	}
}

// checkPersistence checks the health of the persistence system
func (mm *MonitoringManager) checkPersistence() {
	start := time.Now()
	check := &HealthCheck{
		Component: "persistence",
		LastCheck: start,
	}

	defer func() {
		check.Duration = time.Since(start)
		mm.updateHealthCheck(check)
	}()

	if mm.persistenceManager == nil {
		check.Status = HealthStatusUnhealthy
		check.Message = "Persistence manager is not initialized"
		check.Error = "persistence manager is nil"
		return
	}

	// Get persistence stats
	stats := mm.persistenceManager.GetStats()
	check.Details = stats

	// Check if persistence is working
	running, ok := stats["running"].(bool)
	if !ok || !running {
		check.Status = HealthStatusUnhealthy
		check.Message = "Persistence manager is not running"
		return
	}

	check.Status = HealthStatusHealthy
	check.Message = "Persistence system is healthy"

	// Check database if enabled
	if dbEnabled, ok := stats["database_enabled"].(bool); ok && dbEnabled {
		if dbStats, ok := stats["database"].(map[string]interface{}); ok {
			if enabled, ok := dbStats["enabled"].(bool); !ok || !enabled {
				check.Status = HealthStatusDegraded
				check.Message = "Database persistence is disabled"
			}
		}
	}
}

// checkFileSystem checks the health of the file system
func (mm *MonitoringManager) checkFileSystem() {
	start := time.Now()
	check := &HealthCheck{
		Component: "file_system",
		LastCheck: start,
	}

	defer func() {
		check.Duration = time.Since(start)
		mm.updateHealthCheck(check)
	}()

	// Check data directory
	dataDir := "data/events"
	if err := mm.checkDirectoryHealth(dataDir); err != nil {
		check.Status = HealthStatusUnhealthy
		check.Message = "Data directory is not accessible"
		check.Error = err.Error()
		return
	}

	// Check disk space
	diskSpace, err := mm.getDiskSpace(dataDir)
	if err != nil {
		check.Status = HealthStatusDegraded
		check.Message = "Cannot determine disk space"
		check.Error = err.Error()
	} else {
		check.Details = map[string]interface{}{
			"disk_space_mb": diskSpace / (1024 * 1024),
		}

		if diskSpace < 100*1024*1024 { // Less than 100MB
			check.Status = HealthStatusDegraded
			check.Message = "Low disk space detected"
		} else {
			check.Status = HealthStatusHealthy
			check.Message = "File system is healthy"
		}
	}
}

// checkRecoverySystem checks the health of the recovery system
func (mm *MonitoringManager) checkRecoverySystem() {
	start := time.Now()
	check := &HealthCheck{
		Component: "recovery_system",
		LastCheck: start,
	}

	defer func() {
		check.Duration = time.Since(start)
		mm.updateHealthCheck(check)
	}()

	if mm.recoveryManager == nil {
		check.Status = HealthStatusUnhealthy
		check.Message = "Recovery manager is not initialized"
		check.Error = "recovery manager is nil"
		return
	}

	// Check if lock file exists and is valid
	lockFile := filepath.Join("data/events", "event_manager.lock")
	if _, err := os.Stat(lockFile); err != nil {
		check.Status = HealthStatusDegraded
		check.Message = "Lock file is missing"
		check.Error = err.Error()
		return
	}

	check.Status = HealthStatusHealthy
	check.Message = "Recovery system is healthy"
	check.Details = map[string]interface{}{
		"lock_file_exists": true,
		"process_id":       os.Getpid(),
	}
}

// checkEventStates checks the consistency of event states
func (mm *MonitoringManager) checkEventStates() {
	start := time.Now()
	check := &HealthCheck{
		Component: "event_states",
		LastCheck: start,
	}

	defer func() {
		check.Duration = time.Since(start)
		mm.updateHealthCheck(check)
	}()

	if mm.manager == nil {
		check.Status = HealthStatusUnknown
		check.Message = "Cannot check event states - manager not available"
		return
	}

	events := mm.manager.GetAllEvents()
	activeEvents := mm.manager.GetActiveEvents()
	
	inconsistencies := 0
	expiredActive := 0

	for _, event := range activeEvents {
		if baseEvent, ok := event.(*BaseEvent); ok {
			if baseEvent.HasExpired() {
				expiredActive++
			}
		}
	}

	check.Details = map[string]interface{}{
		"total_events":    len(events),
		"active_events":   len(activeEvents),
		"expired_active":  expiredActive,
		"inconsistencies": inconsistencies,
	}

	if expiredActive > 0 {
		check.Status = HealthStatusDegraded
		check.Message = fmt.Sprintf("Found %d expired active events", expiredActive)
	} else if inconsistencies > 0 {
		check.Status = HealthStatusDegraded
		check.Message = fmt.Sprintf("Found %d state inconsistencies", inconsistencies)
	} else {
		check.Status = HealthStatusHealthy
		check.Message = "Event states are consistent"
	}
}

// updateHealthCheck updates a health check result
func (mm *MonitoringManager) updateHealthCheck(check *HealthCheck) {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	mm.healthChecks[check.Component] = check

	// Log health check results
	level := slog.LevelInfo
	if check.Status == HealthStatusDegraded {
		level = slog.LevelWarn
	} else if check.Status == HealthStatusUnhealthy {
		level = slog.LevelError
	}

	slog.Log(context.Background(), level, "Health check completed",
		"component", check.Component,
		"status", check.Status,
		"message", check.Message,
		"duration", check.Duration,
	)
}

// checkAlerts checks for alert conditions
func (mm *MonitoringManager) checkAlerts() {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	for component, check := range mm.healthChecks {
		threshold, exists := mm.alertThresholds[component]
		if !exists {
			continue
		}

		if check.Duration > threshold {
			slog.Warn("Health check duration exceeded threshold",
				"component", component,
				"duration", check.Duration,
				"threshold", threshold,
			)
		}

		if check.Status == HealthStatusUnhealthy {
			slog.Error("Component is unhealthy",
				"component", component,
				"message", check.Message,
				"error", check.Error,
			)
		}
	}
}

// GetHealthStatus returns the overall health status
func (mm *MonitoringManager) GetHealthStatus() map[string]*HealthCheck {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	result := make(map[string]*HealthCheck)
	for k, v := range mm.healthChecks {
		result[k] = v
	}
	return result
}

// checkDirectoryHealth checks if a directory is accessible
func (mm *MonitoringManager) checkDirectoryHealth(dir string) error {
	// Check if directory exists
	if _, err := os.Stat(dir); err != nil {
		return fmt.Errorf("directory not accessible: %w", err)
	}

	// Try to create a test file
	testFile := filepath.Join(dir, ".health_check")
	if err := os.WriteFile(testFile, []byte("test"), 0644); err != nil {
		return fmt.Errorf("cannot write to directory: %w", err)
	}

	// Clean up test file
	os.Remove(testFile)
	return nil
}

// getDiskSpace returns available disk space in bytes
func (mm *MonitoringManager) getDiskSpace(path string) (int64, error) {
	// This is a simplified implementation
	// In a real implementation, you'd use syscalls to get actual disk space
	if info, err := os.Stat(path); err == nil {
		// Return a mock value for now
		_ = info
		return 1024 * 1024 * 1024, nil // 1GB
	}
	return 0, fmt.Errorf("cannot stat path")
}
