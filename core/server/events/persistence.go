package events

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// PersistenceManager handles advanced event data persistence
type PersistenceManager struct {
	primaryPath      string
	backupPath       string
	tempPath         string
	mutex            sync.RWMutex
	saveInterval     time.Duration
	maxBackups       int
	autoSave         bool
	stopChan         chan bool
	running          bool
	dbPersistence    *DatabasePersistence
	enableDatabase   bool
}

// NewPersistenceManager creates a new persistence manager
func NewPersistenceManager(dataDir string) *PersistenceManager {
	pm := &PersistenceManager{
		primaryPath:    filepath.Join(dataDir, "events_state.json"),
		backupPath:     filepath.Join(dataDir, "backups"),
		tempPath:       filepath.Join(dataDir, "temp"),
		saveInterval:   10 * time.Second, // Save every 10 seconds
		maxBackups:     10,               // Keep 10 backup files
		autoSave:       true,
		stopChan:       make(chan bool),
		running:        false,
		enableDatabase: false, // Will be enabled if database is available
	}

	// Try to initialize database persistence
	// This would typically come from configuration
	dsn := "" // TODO: Get from config
	if dsn != "" {
		if dbPersistence, err := NewDatabasePersistence(dsn); err == nil {
			pm.dbPersistence = dbPersistence
			pm.enableDatabase = dbPersistence.IsEnabled()
		}
	}

	return pm
}

// Start starts the persistence manager with auto-save
func (pm *PersistenceManager) Start() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if pm.running {
		return fmt.Errorf("persistence manager is already running")
	}

	// Ensure directories exist
	if err := pm.ensureDirectories(); err != nil {
		return fmt.Errorf("failed to create directories: %w", err)
	}

	pm.running = true

	// Start auto-save goroutine if enabled
	if pm.autoSave {
		go pm.autoSaveLoop()
	}

	slog.Info("Event persistence manager started", 
		"primary_path", pm.primaryPath,
		"backup_path", pm.backupPath,
		"save_interval", pm.saveInterval)

	return nil
}

// Stop stops the persistence manager
func (pm *PersistenceManager) Stop() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if !pm.running {
		return nil
	}

	pm.running = false

	// Send stop signal
	select {
	case pm.stopChan <- true:
	default:
		// Channel might be full, continue
	}

	// Close database connection if enabled
	if pm.dbPersistence != nil {
		if err := pm.dbPersistence.Close(); err != nil {
			slog.Warn("Failed to close database connection", "error", err)
		}
	}

	slog.Info("Event persistence manager stopped")
	return nil
}

// ensureDirectories creates necessary directories
func (pm *PersistenceManager) ensureDirectories() error {
	dirs := []string{
		filepath.Dir(pm.primaryPath),
		pm.backupPath,
		pm.tempPath,
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
	}

	return nil
}

// autoSaveLoop runs the auto-save loop
func (pm *PersistenceManager) autoSaveLoop() {
	ticker := time.NewTicker(pm.saveInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// Auto-save will be triggered by the event manager
			// This loop is mainly for cleanup and maintenance
			pm.cleanupOldBackups()

			// Cleanup old database backups if enabled
			if pm.enableDatabase && pm.dbPersistence != nil {
				if err := pm.dbPersistence.CleanupOldBackups(24 * time.Hour); err != nil {
					slog.Warn("Failed to cleanup old database backups", "error", err)
				}
			}
		case <-pm.stopChan:
			return
		}
	}
}

// SaveEventState saves event state with multiple backup strategies
func (pm *PersistenceManager) SaveEventState(eventStates []EventStateData) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// Create timestamp for this save
	timestamp := time.Now()

	// Save to database first (if enabled)
	if pm.enableDatabase && pm.dbPersistence != nil {
		if err := pm.dbPersistence.SaveEventStates(eventStates); err != nil {
			slog.Warn("Failed to save to database, continuing with file save", "error", err)
		} else {
			slog.Debug("Event state saved to database successfully")
		}
	}

	// Marshal the data for file save
	data, err := json.MarshalIndent(eventStates, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal event state: %w", err)
	}

	// Save to temporary file first (atomic write)
	tempFile := filepath.Join(pm.tempPath, fmt.Sprintf("events_state_%d.json.tmp", timestamp.Unix()))
	if err := os.WriteFile(tempFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write temp file: %w", err)
	}

	// Create backup of current primary file if it exists
	if _, err := os.Stat(pm.primaryPath); err == nil {
		backupFile := filepath.Join(pm.backupPath, fmt.Sprintf("events_state_%s.json",
			timestamp.Format("2006-01-02_15-04-05")))
		if err := pm.copyFile(pm.primaryPath, backupFile); err != nil {
			slog.Warn("Failed to create backup", "error", err)
		}
	}

	// Atomically move temp file to primary location
	if err := os.Rename(tempFile, pm.primaryPath); err != nil {
		return fmt.Errorf("failed to move temp file to primary location: %w", err)
	}

	// Save additional backup with timestamp
	timestampedBackup := filepath.Join(pm.backupPath, fmt.Sprintf("events_state_%d.json", timestamp.Unix()))
	if err := pm.copyFile(pm.primaryPath, timestampedBackup); err != nil {
		slog.Warn("Failed to create timestamped backup", "error", err)
	}

	slog.Debug("Event state saved successfully",
		"events_count", len(eventStates),
		"file_size", len(data),
		"database_enabled", pm.enableDatabase,
		"timestamp", timestamp)

	return nil
}

// LoadEventState loads event state with fallback to backups
func (pm *PersistenceManager) LoadEventState() ([]EventStateData, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// Try to load from database first (if enabled)
	if pm.enableDatabase && pm.dbPersistence != nil {
		if data, err := pm.dbPersistence.LoadEventStates(); err == nil && len(data) > 0 {
			slog.Info("Loaded event state from database", "count", len(data))
			return data, nil
		} else if err != nil {
			slog.Warn("Failed to load from database, falling back to files", "error", err)
		}
	}

	// Try to load from primary file
	if data, err := pm.loadFromFile(pm.primaryPath); err == nil {
		slog.Info("Loaded event state from primary file", "path", pm.primaryPath)
		return data, nil
	} else {
		slog.Warn("Failed to load from primary file", "error", err)
	}

	// Try to load from most recent backup
	backupFiles, err := pm.getBackupFiles()
	if err != nil {
		return nil, fmt.Errorf("failed to get backup files: %w", err)
	}

	for _, backupFile := range backupFiles {
		if data, err := pm.loadFromFile(backupFile); err == nil {
			slog.Info("Loaded event state from backup", "path", backupFile)

			// Restore the primary file from this backup
			if err := pm.copyFile(backupFile, pm.primaryPath); err != nil {
				slog.Warn("Failed to restore primary file from backup", "error", err)
			}

			return data, nil
		} else {
			slog.Warn("Failed to load from backup", "file", backupFile, "error", err)
		}
	}

	// No valid files found
	slog.Info("No valid event state files found, starting fresh")
	return []EventStateData{}, nil
}

// loadFromFile loads and validates event state from a specific file
func (pm *PersistenceManager) loadFromFile(filePath string) ([]EventStateData, error) {
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("file does not exist: %s", filePath)
	}

	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	var eventStates []EventStateData
	if err := json.Unmarshal(data, &eventStates); err != nil {
		return nil, fmt.Errorf("failed to unmarshal event state: %w", err)
	}

	// Validate the loaded data
	if err := pm.validateEventStates(eventStates); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	return eventStates, nil
}

// validateEventStates validates the loaded event state data
func (pm *PersistenceManager) validateEventStates(eventStates []EventStateData) error {
	for i, state := range eventStates {
		if state.ID == "" {
			return fmt.Errorf("event %d has empty ID", i)
		}
		if state.Type == "" {
			return fmt.Errorf("event %s has empty type", state.ID)
		}
		if state.StartTime.IsZero() && state.Status == EventStatusActive {
			return fmt.Errorf("active event %s has zero start time", state.ID)
		}
	}
	return nil
}

// copyFile copies a file from src to dst
func (pm *PersistenceManager) copyFile(src, dst string) error {
	data, err := os.ReadFile(src)
	if err != nil {
		return err
	}
	return os.WriteFile(dst, data, 0644)
}

// getBackupFiles returns backup files sorted by modification time (newest first)
func (pm *PersistenceManager) getBackupFiles() ([]string, error) {
	files, err := filepath.Glob(filepath.Join(pm.backupPath, "events_state_*.json"))
	if err != nil {
		return nil, err
	}

	// Sort by modification time (newest first)
	type fileInfo struct {
		path    string
		modTime time.Time
	}

	var fileInfos []fileInfo
	for _, file := range files {
		info, err := os.Stat(file)
		if err != nil {
			continue
		}
		fileInfos = append(fileInfos, fileInfo{
			path:    file,
			modTime: info.ModTime(),
		})
	}

	// Sort by modification time (newest first)
	for i := 0; i < len(fileInfos)-1; i++ {
		for j := i + 1; j < len(fileInfos); j++ {
			if fileInfos[i].modTime.Before(fileInfos[j].modTime) {
				fileInfos[i], fileInfos[j] = fileInfos[j], fileInfos[i]
			}
		}
	}

	var sortedFiles []string
	for _, info := range fileInfos {
		sortedFiles = append(sortedFiles, info.path)
	}

	return sortedFiles, nil
}

// cleanupOldBackups removes old backup files beyond the maximum limit
func (pm *PersistenceManager) cleanupOldBackups() {
	files, err := pm.getBackupFiles()
	if err != nil {
		slog.Warn("Failed to get backup files for cleanup", "error", err)
		return
	}

	if len(files) <= pm.maxBackups {
		return
	}

	// Remove oldest files
	filesToRemove := files[pm.maxBackups:]
	for _, file := range filesToRemove {
		if err := os.Remove(file); err != nil {
			slog.Warn("Failed to remove old backup file", "file", file, "error", err)
		} else {
			slog.Debug("Removed old backup file", "file", file)
		}
	}
}

// GetStats returns persistence statistics
func (pm *PersistenceManager) GetStats() map[string]interface{} {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	stats := map[string]interface{}{
		"running":         pm.running,
		"primary_path":    pm.primaryPath,
		"backup_path":     pm.backupPath,
		"save_interval":   pm.saveInterval.String(),
		"max_backups":     pm.maxBackups,
		"auto_save":       pm.autoSave,
		"database_enabled": pm.enableDatabase,
	}

	// Add file information
	if info, err := os.Stat(pm.primaryPath); err == nil {
		stats["primary_file_size"] = info.Size()
		stats["primary_file_modified"] = info.ModTime()
	}

	// Count backup files
	if files, err := pm.getBackupFiles(); err == nil {
		stats["backup_count"] = len(files)
	}

	// Add database stats if enabled
	if pm.enableDatabase && pm.dbPersistence != nil {
		dbStats := pm.dbPersistence.GetStats()
		stats["database"] = dbStats
	}

	return stats
}
