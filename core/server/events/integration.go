package events

import (
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/player"
	eventBlocks "server/server/blocks/events"
	"time"
)

// EventIntegrationManager handles integration between events and other systems
type EventIntegrationManager struct {
	manager EventManager
}

// NewEventIntegrationManager creates a new integration manager
func NewEventIntegrationManager(manager EventManager) *EventIntegrationManager {
	return &EventIntegrationManager{
		manager: manager,
	}
}

// HandleSpongeInteraction implements the SpongeInteractionHandler interface
func (eim *EventIntegrationManager) HandleSpongeInteraction(pl *player.Player, pos cube.Pos, eventID string, spawnTime time.Time) {
	if eim.manager == nil {
		return
	}

	// Find the lucky block event
	var luckyEvent *LuckyBlockEvent
	activeEvents := eim.manager.GetActiveEvents()
	for _, event := range activeEvents {
		if event.Type() == EventTypeLuckyBlock && event.ID() == eventID {
			if lbe, ok := event.(*LuckyBlockEvent); ok {
				luckyEvent = lbe
				break
			}
		}
	}

	if luckyEvent == nil {
		// Remove the sponge since there's no active event
		pl.Tx().SetBlock(pos, block.Air{}, nil)
		return
	}

	// Handle the sponge interaction through the event
	luckyEvent.HandleSpongeInteraction(pl, pos)
}

// PlayerDataManagerImpl implements the PlayerDataManager interface
type PlayerDataManagerImpl struct{}

// AddEventScore adds event score to player data
func (pdm *PlayerDataManagerImpl) AddEventScore(playerUUID string, eventID string, score int) {
	// This will be implemented to work with the user package
	// For now, we'll leave it as a placeholder to avoid circular imports
}

// GiveMoney gives money to a player
func (pdm *PlayerDataManagerImpl) GiveMoney(playerUUID string, amount float64) {
	// This will be implemented to work with the user package
	// For now, we'll leave it as a placeholder to avoid circular imports
}

// GiveItem gives an item to a player
func (pdm *PlayerDataManagerImpl) GiveItem(playerUUID string, itemName string, amount int) error {
	// This will be implemented to work with the user package
	// For now, we'll leave it as a placeholder to avoid circular imports
	return nil
}

// ServerConfig interface for accessing server configuration
type ServerConfig interface {
	GetLanguages() map[string][]string
}

// InitializeEventIntegration initializes all event system integrations
func InitializeEventIntegration(manager EventManager, serverConfig interface{}) {
	// Register sponge interaction handler
	integrationManager := NewEventIntegrationManager(manager)
	eventBlocks.RegisterSpongeHandler(integrationManager)

	// Register player data manager
	playerDataManager := &PlayerDataManagerImpl{}
	RegisterPlayerDataManager(playerDataManager)

	// Set up language configuration - we'll handle this differently
	// The language configuration will be set up in the main server initialization
}
