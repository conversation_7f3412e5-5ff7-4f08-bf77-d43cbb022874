package utils

import (
	"github.com/sandertv/gophertunnel/minecraft/text"
	"log/slog"
	"os"
	"reflect"
	"runtime/debug"
	"server/server/database"
	"sync"
	"syscall"
	"time"

	srv "github.com/df-mc/dragonfly/server"
	"github.com/df-mc/dragonfly/server/session"
	"github.com/df-mc/dragonfly/server/world"
)

var (
	logger             = slog.Default()
	server *srv.Server = nil
)

func SetServer(s *srv.Server) {
	server = s
}

func Panics[T any](t T, err error) T {
	Panic(err)
	return t
}

func EnumPanic[T any](err error) (t T) {
	Panic(err)
	return t
}

func Panic(err error) {
	if err != nil {
		errorCode := RandString(6)
		logger.With("code", errorCode).Error(err.Error())
		debug.PrintStack()

		// Trigger emergency save before crashing
		if database.CrashProtectionInstance != nil {
			logger.Error("Triggering emergency save due to panic", "error_code", errorCode)
			if emergencyErr := database.CrashProtectionInstance.EmergencySave("panic_" + errorCode); emergencyErr != nil {
				logger.Error("Emergency save failed during panic", "emergency_error", emergencyErr, "original_error", err)
			} else {
				logger.Info("Emergency save completed during panic", "error_code", errorCode)
			}
		}

		if server != nil {
			var wg sync.WaitGroup
			wg.Add(server.PlayerCount())
			go func() {
				defer func() {
					if r := recover(); r != nil {
						logger.Error("Panic during player disconnection", "panic", r)
					}
				}()

				iter := reflect.ValueOf(FetchPrivateField[any](server, "p")).MapRange()
				for iter.Next() {
					p := iter.Value().Interface()
					handle := FetchPrivateField[*world.EntityHandle](p, "handle")
					data := FetchPrivateField[world.EntityData](handle, "data")
					s := FetchPrivateField[*session.Session](data.Data, "s")
					if s != nil {
						s.Disconnect(text.Colourf("<red>Server error has occured! Please report this as a bug with the code: <bold><yellow>%v</yellow></bold></red>", errorCode))
					}
					wg.Done()
				}
			}()

			// Wait for disconnections with timeout
			done := make(chan struct{})
			go func() {
				wg.Wait()
				close(done)
			}()

			select {
			case <-done:
				logger.Info("All players disconnected successfully")
			case <-time.After(5 * time.Second):
				logger.Warn("Timeout waiting for player disconnections")
			}
		}
		os.Exit(int(syscall.SIGTERM)) // closes the server from this
	}
}
