package schedulers

import (
	"log/slog"
	"os"
	"server/server/database"
)

func InitMongo(log *slog.Logger) {
	if conn := os.Getenv("DATABASE_URI"); conn != "" { //"mongodb://localhost:27017" OR "mongodb://mongo:27017"
		log.Info("Connecting to MongoDB...!")
		db, err := database.NewMongoDBDatabase(conn)
		if err != nil {
			log.Error("Failed to connect to MongoDB", "error", err)
			panic(err) // Maintain the same behavior as utils.Panics
		}
		database.DB = db
	} else {
		database.DB = database.NewLocalDatabase()
	}

	log.Info("Successfully connected to the database!", "type", database.DB.Type())

	// Initialize the save queue with 4 workers and queue size of 10000
	database.SaveQueueInstance = database.NewSaveQueue(database.DB, 4, 10000)
	log.Info("Save queue initialized", "workers", 4, "queue_size", 10000)

	// Database initialization complete
}
