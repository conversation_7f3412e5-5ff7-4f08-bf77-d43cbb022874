package schedulers

import (
	"github.com/df-mc/dragonfly/server"
	"log/slog"
	core "server/server"
	"server/server/database"
	"server/server/user"
	"server/server/utils"
	"time"
)

func ScheduleWorldSaving(conf server.Config) {
	// Optimized save intervals:
	// - World save every 60 seconds (less frequent, world saves are expensive)
	// - Player data save every 30 seconds (more critical for player experience)
	// - Batch database save every 2 minutes (efficient bulk operations)

	worldSaveTicker := time.NewTicker(60 * time.Second)
	playerSaveTicker := time.NewTicker(30 * time.Second)
	batchSaveTicker := time.NewTicker(2 * time.Minute)

	go func() {
		for range worldSaveTicker.C {
			// Save world data (blocks, chunks, etc.)
			core.MCServer.World().Save()
		}
	}()

	go func() {
		for range playerSaveTicker.C {
			// Save individual player data
			for pl := range core.MCServer.Players(nil) {
				// Save Dragonfly player data (inventory, position, health, etc.)
				// This must be synchronous as required by Dragonfly
				if err := conf.PlayerProvider.Save(pl.UUID(), pl.Data(), core.MCServer.World()); err != nil {
					errorCode := utils.RandString(6)
					slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save Dragonfly player data during scheduled save: " + err.Error())
				}

				// Check if player is in combat and prioritize their save
				u := user.GetUserByUUID(pl.UUID())
				var priority database.SavePriority = database.PriorityNormal
				if u != nil && u.CoolDownTimeRemaining(user.Combat) > 0 {
					// Player is in combat - use high priority to ensure data is saved quickly
					priority = database.PriorityHigh
				}

				// Queue custom player data save asynchronously
				if database.SaveQueueInstance != nil {
					if u != nil {
						// Refresh XP snapshot before queueing scheduled save
					u.Data.XPLevel = pl.ExperienceLevel()
					u.Data.XPProgress = pl.ExperienceProgress()

					op := &database.SaveOperation{
							ID:       "player_" + pl.UUID().String(), // unified key for dedupe across sources
							Type:     database.SaveTypePlayer,
							Data:     u.Data,
							Priority: priority,
						}
						if err := database.SaveQueueInstance.QueueSave(op); err != nil {
							// Fallback to synchronous save if queue fails
							if err := user.Save(pl); err != nil {
								errorCode := utils.RandString(6)
								slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data during scheduled save (fallback): " + err.Error())
							}
						}
					}
				} else {
					// Fallback to synchronous save if queue not available
					if err := user.Save(pl); err != nil {
						errorCode := utils.RandString(6)
						slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data during scheduled save: " + err.Error())
					}
				}
			}
		}
	}()

	go func() {
		for range batchSaveTicker.C {
			// Queue batch save operation for all cached data
			if database.SaveQueueInstance != nil {
				op := &database.SaveOperation{
					ID:       "batch_save_" + utils.RandString(8),
					Type:     database.SaveTypeBatch,
					Data:     nil, // Batch operations don't need specific data
					Priority: database.PriorityLow,
				}
				if err := database.SaveQueueInstance.QueueSave(op); err != nil {
					// Fallback to synchronous batch save
					for identifier, err := range database.DB.SaveAll() {
						errorCode := utils.RandString(6)
						slog.Default().With("code", errorCode).With("identifier", identifier).Error("Failed to save cached data during batch save (fallback): " + err.Error())
					}
				}
			} else {
				// Fallback to synchronous batch save
				for identifier, err := range database.DB.SaveAll() {
					errorCode := utils.RandString(6)
					slog.Default().With("code", errorCode).With("identifier", identifier).Error("Failed to save cached data during batch save: " + err.Error())
				}
			}
		}
	}()

	// Keep the main goroutine alive (this function should not return)
	select {}
}
