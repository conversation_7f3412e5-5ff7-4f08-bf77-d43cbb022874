package schedulers

import (
	"log/slog"
	"math/rand"
	"server/server/database"
	"time"
)

// ScheduleJackpotGrowth starts the jackpot growth scheduler
func ScheduleJackpotGrowth() {
	// Load jackpot from database on startup
	if err := database.LoadJackpotFromDatabase(); err != nil {
		slog.Default().Error("Failed to load jackpot from database", "error", err)
	}

	// Increase jackpot every 15 seconds
	for range time.NewTicker(30 * time.Second).C {
		// Random amount between 50,000 and 200,000 doubloons
		randomIncrease := float64(50000000 + rand.Intn(1500000100))
		
		database.AddToJackpot(randomIncrease)
		
		currentJackpot := database.GetJackpot()
		slog.Default().Info("Jackpot increased",
			"increase", int64(randomIncrease),
			"new_total", int64(currentJackpot.Amount))
	}
}
