package server

import (
	"github.com/df-mc/dragonfly/server"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"path"
	"sync/atomic"
)

var shutdownMode atomic.Bool

func SetShutdownMode(on bool) { shutdownMode.Store(on) }
func IsShutdownMode() bool    { return shutdownMode.Load() }

type ConfigServer struct {
	Prefix          string
	WTPrefix        string
	Languages       map[string][]string
	VoteAPIKey      string

	Hub struct {
		SpawnPoint  mgl64.Vec3
		Crates      []mgl64.Vec3
		Leaderboard struct {
			FactionStrength mgl64.Vec3
			FactionRich     mgl64.Vec3
			PlayerStrength  mgl64.Vec3
			PlayerRich      mgl64.Vec3
		}
		Border mgl64.Vec2
		NoPvp  struct {
			C1 mgl64.Vec3
			C2 mgl64.Vec3
		}
		BlockProtectionZone struct {
			C1 mgl64.Vec3
			C2 mgl64.Vec3
		}
	}

	Checkpoint struct {
		IntervalSeconds         int
		CriticalIntervalSeconds int
	}
}

func DefaultConfig() server.UserConfig {
	c := server.DefaultConfig()
	c.Network.Address = ":19132"

	c.Server.Name = text.Colourf("<dark-red>MMC</dark-red>")
	c.Server.DisableJoinQuitMessages = false
	c.Server.AuthEnabled = true
	c.Players.MaxCount = 200
	c.Players.SaveData = true
	c.World.SaveData = true
	c.World.Folder = path.Join(".", "worlds", "hub")
	c.Resources.AutoBuildPack = false
	c.Resources.Required = true

	
	return c
}
