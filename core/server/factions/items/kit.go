package items

import (
	"crypto/rand"
	"encoding/hex"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/item/enchantment"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server/blocks"
	"server/server/blocks/vanilla"
	"server/server/database"
	"server/server/factions/enchants"
	"server/server/language"
	"server/server/utils"
	"time"
)

func init() {
	blocks.RegisterSpecialItem(blocks.Kit, Kit{})
}

// generateKitUniqueID creates a unique identifier for kit items to prevent stacking
func generateKitUniqueID() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// Fallback to timestamp-based ID if crypto/rand fails
		return hex.EncodeToString([]byte(time.Now().Format("20060102150405.000000")))
	}
	return hex.EncodeToString(bytes)
}

type Kit struct {
	vanilla.ChestMineCart
	Type database.Rank
}

func (k Kit) Stack() item.Stack {
	// Generate unique identifiers to prevent kit stacking
	uniqueID := generateKitUniqueID()
	creationTime := time.Now().Unix()

	s := item.NewStack(vanilla.ChestMineCart{}, 1).WithValue("special_item", int16(blocks.Kit)).WithValue("kit_type", int(k.Type)).WithEnchantments(item.NewEnchantment(enchants.Glitter{}, 1))

	// Add unique identifiers to prevent stacking
	s = s.WithValue("unique_id", uniqueID)
	s = s.WithValue("creation_time", creationTime)
	s = s.WithValue("kit_tracked", true)

	s = s.WithCustomName(text.Colourf("%v<green>Kit</green>", k.Type.Prefix())).WithLore("Click to claim the kit")
	return s
}

func (Kit) Use(tx *world.Tx, user item.User, ctx *item.UseContext) bool {
	pl := user.(*player.Player)
	it, _ := pl.HeldItems()
	if d, ok := it.Value("kit_type"); ok {
		// Safe type assertion to prevent panic
		var r database.Rank
		switch v := d.(type) {
		case int:
			r = database.Rank(v)
		case int32:
			r = database.Rank(v)
		case int64:
			r = database.Rank(v)
		default:
			pl.Message(text.Colourf("<red>Error: Invalid kit type. Please contact an administrator.</red>"))
			return false
		}

		var res []item.Stack

		var enchantLevel, xpLevel int
		var armourTier item.ArmourTier
		var toolTier item.ToolTier

		switch r {
		case database.Player:
			enchantLevel = 1
			armourTier = item.ArmourTierIron{}
			toolTier = item.ToolTierIron
		case database.VIP:
			enchantLevel = 4
			xpLevel = 30
			armourTier = item.ArmourTierDiamond{}
			toolTier = item.ToolTierDiamond
		case database.MVP:
			enchantLevel = 3
			xpLevel = 60
			armourTier = item.ArmourTierDiamond{}
			toolTier = item.ToolTierDiamond
		case database.MMP:
			enchantLevel = 2
			xpLevel = 90
			armourTier = item.ArmourTierDiamond{}
			toolTier = item.ToolTierDiamond
		case database.MLP:
			enchantLevel = 5
			xpLevel = 120
			armourTier = item.ArmourTierDiamond{}
			toolTier = item.ToolTierDiamond
		case database.MGP:
			enchantLevel = 1
			xpLevel = 150
			armourTier = item.ArmourTierNetherite{}
			toolTier = item.ToolTierNetherite
		default:
			panic("Should not happen")
		}

		pl.SetExperienceLevel(pl.ExperienceLevel() + xpLevel)

		armourIts := []func(t item.ArmourTier) world.Item{
			func(t item.ArmourTier) world.Item {
				return item.Helmet{Tier: t}
			},
			func(t item.ArmourTier) world.Item {
				return item.Chestplate{Tier: t}
			},
			func(t item.ArmourTier) world.Item {
				return item.Leggings{Tier: t}
			},
			func(t item.ArmourTier) world.Item {
				return item.Boots{Tier: t}
			},
		}

		for _, i := range armourIts {
			res = append(res, fixKitItemName(item.NewStack(i(armourTier), 1), r).WithEnchantments(
				item.NewEnchantment(enchantment.Unbreaking, enchantLevel),
				item.NewEnchantment(enchantment.Protection, enchantLevel),
			))
		}

		toolIts := []func(t item.ToolTier) world.Item{
			func(t item.ToolTier) world.Item {
				return item.Sword{Tier: t}
			},
			func(t item.ToolTier) world.Item {
				return item.Axe{Tier: t}
			},
			func(t item.ToolTier) world.Item {
				return item.Pickaxe{Tier: t}
			},
			func(t item.ToolTier) world.Item {
				return item.Shovel{Tier: t}
			},
			func(t item.ToolTier) world.Item {
				return item.Hoe{Tier: t}
			},
		}

		for _, i := range toolIts {
			s := fixKitItemName(item.NewStack(i(toolTier), 1), r).WithEnchantments(item.NewEnchantment(enchantment.Unbreaking, enchantLevel))
			if _, ok := s.Item().(item.Sword); ok {
				s = s.WithEnchantments(item.NewEnchantment(enchantment.Sharpness, enchantLevel))
			} else {
				s = s.WithEnchantments(item.NewEnchantment(enchantment.Efficiency, enchantLevel))
			}
			res = append(res, s)
		}

		switch r {
		case database.Player:
			res = append(res,
				item.NewStack(block.Obsidian{}, 2*64),
				item.NewStack(block.Log{Wood: block.CherryWood()}, 1*64),
				item.NewStack(item.EnchantedApple{}, 64),
			)
		case database.VIP:
			res = append(res,
				item.NewStack(block.Obsidian{}, 3*64),
				item.NewStack(block.Bedrock{}, 42),
				item.NewStack(block.Log{Wood: block.CherryWood()}, 4*64),
				item.NewStack(item.EnchantedApple{}, 64),
			)
		case database.MVP:
			res = append(res,
				item.NewStack(block.Obsidian{}, 4*64),
				item.NewStack(block.Bedrock{}, 64),
				item.NewStack(block.Log{Wood: block.CherryWood()}, 5*64),
				item.NewStack(item.EnchantedApple{}, 64),
			)
		case database.MMP:
			res = append(res,
				item.NewStack(block.Obsidian{}, 5*64),
				item.NewStack(block.Bedrock{}, 2*64),
				item.NewStack(block.Log{Wood: block.CherryWood()}, 3*64),
				item.NewStack(item.EnchantedApple{}, 2*64),
			)
		case database.MLP:
			res = append(res,
				item.NewStack(block.Obsidian{}, 6*64),
				item.NewStack(block.Bedrock{}, 2*64),
				item.NewStack(block.Log{Wood: block.CherryWood()}, 4*64),
				item.NewStack(item.EnchantedApple{}, 2*64),
			)
		default:
			res = append(res,
				item.NewStack(block.Obsidian{}, 7*64),
				item.NewStack(block.Bedrock{}, 2*64),
				item.NewStack(block.Log{Wood: block.CherryWood()}, 4*64),
				item.NewStack(item.EnchantedApple{}, 3*64),
			)
		}

		if r != database.Player {
			res = append(res,
				fixKitItemName(item.NewStack(item.Elytra{}, 1), r),
				fixKitItemName(item.NewStack(item.Firework{Duration: 2 * time.Second}, 2*64), r),
			)
		}

		res = append(res,
			item.NewStack(block.Cactus{}, 1*64),
			item.NewStack(item.Bow{}, 1).WithEnchantments(item.NewEnchantment(enchantment.Power, 1)),
			item.NewStack(item.Arrow{}, 128),
		)

		for _, stack := range res {
			if _, err := pl.Inventory().AddItem(stack); err != nil {
				pl.Drop(stack)
				pl.Message(text.Colourf(language.Translate(pl).Error.InventoryFull))
			}
		}
	}

	ctx.CountSub = 1
	return true
}

func (k Kit) UseOnBlock(pos cube.Pos, face cube.Face, _ mgl64.Vec3, tx *world.Tx, usr item.User, ctx *item.UseContext) (used bool) {
	return k.Use(tx, usr, ctx)
}

func fixKitItemName(s item.Stack, r database.Rank) item.Stack {
	return s.WithCustomName(text.Colourf("%v %v", r.Prefix(), utils.ItemDisplay(s)))
}
