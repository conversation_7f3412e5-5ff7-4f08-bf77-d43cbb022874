package items

import (
    "github.com/df-mc/dragonfly/server/block/cube"
    "github.com/df-mc/dragonfly/server/entity"
    "github.com/df-mc/dragonfly/server/item"
    "github.com/df-mc/dragonfly/server/item/creative"
    "github.com/df-mc/dragonfly/server/player"
    "github.com/df-mc/dragonfly/server/world"
    "github.com/df-mc/dragonfly/server/world/particle"
    "github.com/df-mc/dragonfly/server/world/sound"
    "github.com/go-gl/mathgl/mgl64"
    "github.com/sandertv/gophertunnel/minecraft/text"
    "image/color"
    "math/rand"
    "server/server/blocks"
    "server/server/factions/enchants"
)

func init() {
    blocks.RegisterSpecialItem(blocks.BagOfExperience, BagOfExperience{})
    // Register the item in the world and creative inventory
    world.RegisterItem(BagOfExperience{})
    creative.RegisterItem(creative.Item{Stack: item.NewStack(BagOfExperience{}, 1), Group: "Custom"})
}

type BagOfExperience struct {
    XPAmount int // Amount of XP this bag contains
}

// EncodeItem makes this item appear as an Ender Chest (representing a magical bag) in the game
func (BagOfExperience) EncodeItem() (name string, meta int16) {
    return "minecraft:ender_chest", 0
}

func (BagOfExperience) Stack() item.Stack {
    return BagOfExperience{XPAmount: 1 + rand.Intn(5)}.StackWithAmount() // 1-5 XP
}

func (b BagOfExperience) StackWithAmount() item.Stack {
    if b.XPAmount <= 0 {
        b.XPAmount = 1 + rand.Intn(5) // Default to 1-5 XP if not set
    }

    s := item.NewStack(BagOfExperience{XPAmount: b.XPAmount}, 1).WithValue("special_item", int16(blocks.BagOfExperience)).WithValue("xp_amount", b.XPAmount).WithEnchantments(item.NewEnchantment(enchants.Glitter{}, 1))
    s = s.WithCustomName(text.Colourf("<gold>Bag of Experience</gold>")).WithLore(
        text.Colourf("<grey>Contains: <green>%d XP</green></grey>", b.XPAmount),
        "",
        text.Colourf("<yellow>Right-click to gain experience!</yellow>"),
    )
    return s
}

func (BagOfExperience) Use(tx *world.Tx, user item.User, ctx *item.UseContext) bool {
    pl := user.(*player.Player)
    it, _ := pl.HeldItems()
    
    // Get XP amount from item data
    xpAmount := 75 // Default amount
    if amount, ok := it.Value("xp_amount"); ok {
        if amt, ok := amount.(int); ok {
            xpAmount = amt
        }
    }
    
    // Add experience to player
    currentXP := pl.ExperienceLevel()
    pl.SetExperienceLevel(currentXP + xpAmount)
    
    // Play effects
    pl.PlaySound(sound.LevelUp{})
    
    // Spawn experience orb particles around the player
    for i := 0; i < 10; i++ {
        offset := mgl64.Vec3{
            (rand.Float64() - 0.5) * 2,
            rand.Float64() * 2,
            (rand.Float64() - 0.5) * 2,
        }
        tx.AddParticle(pl.Position().Add(offset), particle.Dust{Colour: color.RGBA{R: 0, G: 255, B: 0, A: 255}})
    }
    
    // Add some experience orbs for visual effect
    for i := 0; i < 3; i++ {
        orbPos := pl.Position().Add(mgl64.Vec3{
            (rand.Float64() - 0.5) * 1.5,
            1 + rand.Float64(),
            (rand.Float64() - 0.5) * 1.5,
        })
        orb := entity.NewExperienceOrb(world.EntitySpawnOpts{Position: orbPos}, 0) // Visual only, no actual XP
        tx.AddEntity(orb)
    }
    
    // Send success message
    pl.Message(text.Colourf("<green>You gained %d experience levels from the bag!</green>", xpAmount))
    
    ctx.CountSub = 1
    return true
}

func (b BagOfExperience) UseOnBlock(pos cube.Pos, face cube.Face, _ mgl64.Vec3, tx *world.Tx, usr item.User, ctx *item.UseContext) (used bool) {
    return b.Use(tx, usr, ctx)
}

// CreateBagOfExperience creates a bag with a specific XP amount
func CreateBagOfExperience(xpAmount int) item.Stack {
    return BagOfExperience{XPAmount: xpAmount}.StackWithAmount()
}

// CreateRandomBagOfExperience creates a bag with random XP (1-5)
func CreateRandomBagOfExperience() item.Stack {
    return BagOfExperience{XPAmount: 1 + rand.Intn(5)}.StackWithAmount()
}

// CreateSmallBagOfExperience creates a bag with 5-10 XP
func CreateSmallBagOfExperience() item.Stack {
    return BagOfExperience{XPAmount: 5 + rand.Intn(6)}.StackWithAmount()
}

// CreateLargeBagOfExperience creates a bag with 10-15 XP
func CreateLargeBagOfExperience() item.Stack {
    return BagOfExperience{XPAmount: 10 + rand.Intn(6)}.StackWithAmount()
}
