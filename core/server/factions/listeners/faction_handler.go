package listeners

import (
	"anticheat/handlers"
	"fmt"
	"github.com/df-mc/dragonfly/server/block"
	"github.com/df-mc/dragonfly/server/block/cube"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/item"
	"github.com/df-mc/dragonfly/server/item/enchantment"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/chat"
	"github.com/df-mc/dragonfly/server/player/title"
	"github.com/df-mc/dragonfly/server/world"
	"github.com/df-mc/dragonfly/server/world/particle"
	"github.com/df-mc/dragonfly/server/world/sound"
	"github.com/go-gl/mathgl/mgl64"
	"github.com/sandertv/gophertunnel/minecraft/protocol"
	"github.com/sandertv/gophertunnel/minecraft/protocol/packet"
	"github.com/sandertv/gophertunnel/minecraft/text"
	"server/server/blocks"
	"image/color"
	"log/slog"
	"math"
	"math/rand"
	core "server/server"
	items2 "server/server/blocks"
	"server/server/database"
	"server/server/entity/mobs"
	serverFactions "server/server/factions"
	"server/server/factions/backpack"
	"server/server/factions/items"
	"server/server/language"
	"server/server/user"
	"server/server/utils"
	"slices"
	"strings"
	"time"
)

type FactionHandler struct {
	player.NopHandler

	ACHandler *handlers.ACPlayerHandler
}

func (h FactionHandler) HandleHurt(ctx *player.Context, damage *float64, immune bool, attackImmunity *time.Duration, src world.DamageSource) {
	if h.ACHandler != nil {
		h.ACHandler.HandleHurt(ctx, damage, immune, attackImmunity, src)
	}


		// Block all incoming damage during shutdown to prevent last-second deaths
		if core.IsShutdownMode() {
			ctx.Cancel()
			return
		}

	pl := ctx.Val()
	u := user.GetUser(pl)

	if _, ok := src.(entity.FallDamageSource); ok {
		ctx.Cancel()
		return
	}

	// Cancel teleport timer when player takes any damage
	u.CancelTeleportTimer()

	c1 := core.Config.Hub.NoPvp.C1
	c2 := core.Config.Hub.NoPvp.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pl.Position()) {
		ctx.Cancel()
		pl.Message(text.Colourf(language.Translate(pl).Error.PvPDisabled))
		return
	}

	var killer world.Entity
	if s1, ok := src.(entity.AttackDamageSource); ok {
		killer = s1.Attacker
	} else if s2, ok := src.(entity.ProjectileDamageSource); ok {
		killer = s2.Owner
	}


	if killer, ok := killer.(*player.Player); ok {
		uk := user.GetUser(killer)
		if u.Data.Faction.HasFaction() && uk.Data.Faction.HasFaction() && u.Data.Faction.Faction() == uk.Data.Faction.Faction() {
			return
		}

		// PvP-only: start/renew victim's combat tag since attacker is a player
		if !u.IsCoolDownActive(user.Combat, 15*time.Second, true, true, false) {
			pl.Message(text.Colourf(language.Translate(pl).CombatLogged))
		}

		// Start/renew attacker's combat tag
		if !uk.IsCoolDownActive(user.Combat, 15*time.Second, true, true, false) {
			killer.Message(text.Colourf(language.Translate(killer).CombatLogged))
			// Save the attacking player's data
			user.DebouncedSave(killer)
		}

		// Cancel any active teleport timers for both players when combat starts
		u.CancelTeleportTimer()
		uk.CancelTeleportTimer()
	}

	if pl.Health()+pl.Absorption() <= *damage {
		up := user.GetUser(pl)

		up.Data.Faction.Stats.Deaths++
		up.Data.Faction.Stats.BestKillStreak = max(up.Data.Faction.Stats.KillStreak, up.Data.Faction.Stats.BestKillStreak)
		up.Data.Faction.Stats.KillStreak = 0

		performFactionDeath(pl)

		if _, err := pl.Inventory().AddItem(backpack.Backpack{}.Stack()); err == nil {
			pl.Message(text.Colourf(language.Translate(pl).GiveBackpack, core.Config.Prefix, backpack.Backpack{}.Stack().CustomName()))
		}

		pl.Heal(pl.MaxHealth(), nil)
		pl.SetFood(20)
		for _, e := range pl.Effects() {
			pl.RemoveEffect(e.Type())
		}
		pl.Extinguish()
		pl.RemoveExperience(pl.Experience())
		pl.RemoveBossBar()
		pl.SendTitle(title.New(text.Colourf(language.Translate(pl).YouDied)))
		if killer, ok := killer.(*player.Player); ok {
			uk := user.GetUser(killer)
			uk.Data.Faction.Stats.Strength += 0.5
			if uk.Data.Faction.HasFaction() {
				uk.Data.Faction.Faction().Strength += 5
			}
			uk.Data.Faction.Stats.Kills++
			uk.Data.Faction.Stats.KillStreak++
			if _, err := killer.Inventory().AddItem(items.PlayerHead{KilledUuid: pl.UUID()}.Stack()); err != nil {
				killer.Message(text.Colourf(language.Translate(killer).Error.InventoryFull))
			}

			if up.Data.Faction.Stats.Strength >= 0.5 {
				up.Data.Faction.Stats.Strength -= 0.5
			}
			if up.Data.Faction.HasFaction() && up.Data.Faction.Faction().Strength >= 10 {
				up.Data.Faction.Faction().Strength -= 10
			}
		}
		ctx.Cancel()
	}

	EnchantsHandleHurt(ctx, damage, attackImmunity, src)
}

func performFactionDeath(pl *player.Player) {
	pos := pl.Position()
	for _, orb := range entity.NewExperienceOrbs(pos, int(math.Min(float64(pl.ExperienceLevel()*7), 100))) {
		pl.Tx().AddEntity(orb)
	}
	pl.SetExperienceLevel(0)
	utils.Session(pl).SendExperience(pl.ExperienceLevel(), pl.ExperienceProgress())

	pl.MoveItemsToInventory()
	for _, it := range append(pl.Inventory().Clear(), pl.Armour().Clear()...) {
		if _, ok := it.Enchantment(enchantment.CurseOfVanishing); ok {
			continue
		}
		opts := world.EntitySpawnOpts{Position: pos, Velocity: mgl64.Vec3{rand.Float64()*0.2 - 0.1, 0.2, rand.Float64()*0.2 - 0.1}}
		pl.Tx().AddEntity(entity.NewItem(opts, it))
	}

	pl.Teleport(core.Config.Hub.SpawnPoint)
}

func (h FactionHandler) HandleAttackEntity(ctx *player.Context, e world.Entity, force, height *float64, critical *bool) {
	if h.ACHandler != nil {
		h.ACHandler.HandleAttackEntity(ctx, e, force, height, critical)
	}
}

func (h FactionHandler) HandleQuit(pl *player.Player) {
	if h.ACHandler != nil {
		h.ACHandler.HandleQuit(pl)
	}

	u := user.GetUser(pl)
	u.Data.LastLogin = time.Now()
	u.Data.Online = false

	// Normal quit handling

	// Cancel any active teleport timer when player quits
	u.CancelTeleportTimer()

		// Clear combat tag on quit to avoid false combat log state persisting
		u.ClearCombatTag()


	// Save Dragonfly player data (inventory, position, health, etc.) first
	if err := core.DragonflyConfig.PlayerProvider.Save(pl.UUID(), pl.Data(), core.MCServer.World()); err != nil {
		errorCode := utils.RandString(6)
		slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save Dragonfly player data on quit: " + err.Error())
	}

		// Record current XP into custom data as a redundancy backup
		u.Data.XPLevel = pl.ExperienceLevel()
		u.Data.XPProgress = pl.ExperienceProgress()


		// Take a one-time inventory snapshot for recovery safety
		{
			inv := pl.Inventory().Items()
			arm := pl.Armour().Items()
			u.Data.InventorySnapshot = u.Data.InventorySnapshot[:0]
			u.Data.ArmourSnapshot = u.Data.ArmourSnapshot[:0]
			for _, it := range inv {
				if it.Count() > 0 { u.Data.InventorySnapshot = append(u.Data.InventorySnapshot, database.NewCustomStack(it)) }
			}
			for _, it := range arm {
				if it.Count() > 0 { u.Data.ArmourSnapshot = append(u.Data.ArmourSnapshot, database.NewCustomStack(it)) }
			}
			u.Data.LastLogin = time.Now()
			u.Data.Online = false
			u.Data.SnapshotAt = time.Now()
			if err := user.Save(pl); err != nil {
				errorCode := utils.RandString(6)
				slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save inventory snapshot on quit: "+err.Error())
			}
		}


	// Save custom player data (faction data, stats, etc.)
	if err := user.Save(pl); err != nil {
		// Log the error but don't crash the server
		errorCode := utils.RandString(6)
		slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data on quit: " + err.Error())
	}

	if u.CoolDownTimeRemaining(user.Combat) > 0 {
		pl.Hurt(10000, entity.VoidDamageSource{})
	}
}

func (h FactionHandler) HandleChat(ctx *player.Context, msg *string) {
	if h.ACHandler != nil {
		h.ACHandler.HandleChat(ctx, msg)
	}

	pl := ctx.Val()
	u := user.GetUser(pl)

	ctx.Cancel()
	*msg = text.Colourf("%v<white>:</white> %v", user.FactionNameDisplay.Name(u.Data), *msg)
	_, _ = fmt.Fprintf(chat.Global, *msg)
}

func CheckPlayerChangeTerritory(u *user.User, pos cube.Pos) bool {
	facWithin := database.FactionWithin(pos.Vec3())
	if facWithin != nil {
		if u.Data.Faction.HasFaction() && u.Data.Faction.Name != facWithin.Name {
			return false
		} else {
			return u.Data.Faction.Stats.Strength > facWithin.Strength
		}
	}
	return true
}

func (h FactionHandler) HandleItemUse(ctx *player.Context) {
	if h.ACHandler != nil {
		h.ACHandler.HandleItemUse(ctx)
	}

	pl := ctx.Val()
	u := user.GetUser(pl)
	if u.IsCoolDownActive(user.Use, 50*time.Millisecond, false, true, false) {
		return
	}

	main, off := pl.HeldItems()
	if v, ok := main.Value("special_item"); ok && main.Count() != 0 {
		if _, ok := main.Value("wild_only"); ok {
			if !CheckPlayerChangeTerritory(user.GetUser(pl), cube.PosFromVec3(pl.Position())) {
				pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pl.Position()).Name))
				ctx.Cancel()
				return
			}

			c1 := core.Config.Hub.BlockProtectionZone.C1
			c2 := core.Config.Hub.BlockProtectionZone.C2
			box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
			if box.Vec3Within(pl.Position()) && pl.GameMode() != world.GameModeCreative {
				pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
				ctx.Cancel()
				return
			}
		}

		if act, ok2 := specialActionOf(v); ok2 {
			if uit, ok := items2.SpecialItem(items2.ItemAction(act)).(item.Usable); ok {
				ctx.Cancel()
				useCtx := item.UseContext{}
				uit.Use(pl.Tx(), pl, &useCtx)
				if useCtx.CountSub > 0 {
					pl.SetHeldItems(main.Grow(-1*useCtx.CountSub), off)
				}
			}
		}
	}
}

func (h FactionHandler) HandleItemUseOnBlock(ctx *player.Context, pos cube.Pos, face cube.Face, clickPos mgl64.Vec3) {
	if h.ACHandler != nil {
		h.ACHandler.HandleItemUseOnBlock(ctx, pos, face, clickPos)
	}

	pl := ctx.Val()
	u := user.GetUser(pl)
	if u.IsCoolDownActive(user.Use, 50*time.Millisecond, false, true, false) {
		return
	}

	main, off := pl.HeldItems()
	if v, ok := main.Value("special_item"); ok && main.Count() != 0 {
		if _, ok := main.Value("wild_only"); ok {
			allowClaimShovel := false
			if v2, ok2 := main.Value("special_item"); ok2 {
				if act, ok3 := specialActionOf(v2); ok3 && act == items2.ClaimShovel {
					allowClaimShovel = true
				}
			}
			if !allowClaimShovel && !CheckPlayerChangeTerritory(user.GetUser(pl), cube.PosFromVec3(pl.Position())) {
				pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pl.Position()).Name))
				ctx.Cancel()
				return
			}

			c1 := core.Config.Hub.BlockProtectionZone.C1
			c2 := core.Config.Hub.BlockProtectionZone.C2
			box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
			if box.Vec3Within(pl.Position()) && pl.GameMode() != world.GameModeCreative {
				pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
				ctx.Cancel()
				return
			}
		}

		if act, ok2 := specialActionOf(v); ok2 {
			if uit, ok := items2.SpecialItem(items2.ItemAction(act)).(item.UsableOnBlock); ok {
			ctx.Cancel()

			useCtx := item.UseContext{}
			uit.UseOnBlock(pos, face, clickPos, pl.Tx(), pl, &useCtx)
			if useCtx.CountSub > 0 {
				pl.SetHeldItems(main.Grow(-1*useCtx.CountSub), off)
			}
		}
			}

	}

	if !CheckPlayerChangeTerritory(user.GetUser(pl), pos) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pos.Vec3()).Name))
		ctx.Cancel()
		return
	}

	b := pl.Tx().Block(pos)
	if _, ok := b.(block.Chest); ok {
		var ct *database.CrateType
		for _, t := range []database.CrateType{database.Common, database.Rare, database.Legendary, database.Ancient, database.Vote} {
			if equal(pos.Vec3(), core.Config.Hub.Crates[t], 0.5) {
				ct = &t
				break
			}
		}

		if ct != nil {
			ctx.Cancel()
			// All crates now work the same way - give rewards directly
			{
				// Simple crate opening - no UI needed
				if u.Data.Faction.Stats.CrateKeys[*ct] > 0 {
					// Roll reward first, then only consume a key if we can safely add it
					reward := rollCrateReward(*ct)

					// Try to add safely. If it cannot be added, do NOT consume the key and do NOT drop.
					if !u.AddItem(reward) {
						pl.Message(text.Colourf("<red>✗ Your inventory is full. Free up a slot before opening %v.</red>", ct.Name()))
						pl.PlaySound(sound.Deny{})
						return
					}

					// Successfully added: now consume a key
					u.Data.Faction.Stats.CrateKeys[*ct]--

					// Check for jackpot win on Vote crates
					if *ct == database.Vote {
						checkVoteCrateJackpot(pl)
					}

					// Play success sound
					pl.PlaySound(sound.LevelUp{})

					// Update the crate display to show what was won
					go func() {
						// Show the reward for 3 seconds
						time.Sleep(100 * time.Millisecond) // Small delay to ensure the crate display updates
						updateCrateDisplayWithReward(pl, *ct, reward)

						// Reset to normal display after 3 seconds
						time.Sleep(3 * time.Second)
						updateCrateDisplayNormal(pl, *ct)
					}()
				} else {
					pl.Message(text.Colourf("<red>✗ You don't have any %v <red>keys!</red></red>", ct.Name()))
					pl.PlaySound(sound.Deny{})
				}
			}
			return
		}
	}

	c1 := core.Config.Hub.BlockProtectionZone.C1
	c2 := core.Config.Hub.BlockProtectionZone.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pos.Vec3()) && pl.GameMode() != world.GameModeCreative {
		pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
		ctx.Cancel()
		return
	}

	if _, ok := main.Item().(item.Firework); ok {
		if u.CoolDownTimeRemaining(user.Combat) > 0 {
			ctx.Cancel()
		}
	}
}

func (h FactionHandler) HandleItemUseOnEntity(ctx *player.Context, e world.Entity) {
	if h.ACHandler != nil {
		h.ACHandler.HandleItemUseOnEntity(ctx, e)
	}

	pl := ctx.Val()

	if !CheckPlayerChangeTerritory(user.GetUser(pl), cube.PosFromVec3(e.Position())) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(e.Position()).Name))
		ctx.Cancel()
		return
	}

	c1 := core.Config.Hub.BlockProtectionZone.C1
	c2 := core.Config.Hub.BlockProtectionZone.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(e.Position()) && pl.GameMode() != world.GameModeCreative {
		pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
		ctx.Cancel()
		return
	}
}

func (h FactionHandler) HandleHeldSlotChange(ctx *player.Context, from, to int) {
	if h.ACHandler != nil {
		h.ACHandler.HandleHeldSlotChange(ctx, from, to)
	}

	pl := ctx.Val()
	u := user.GetUser(pl)
	toStack, _ := pl.Inventory().Item(to)
	fromStack, _ := pl.Inventory().Item(from)

	if v, ok := toStack.Value("special_item"); ok {
			if act, ok2 := specialActionOf(v); ok2 && act == items2.ClaimShovel && u.Data.Faction.HasFaction() {
				u.Data.Faction.Faction().RefreshChunkLines(pl)
			}
		} else if v, ok := fromStack.Value("special_item"); ok {
			if act, ok2 := specialActionOf(v); ok2 && act == items2.ClaimShovel && u.Data.Faction.HasFaction() {
				utils.Session(pl).RemoveAllDebugShapes()
			}
		}
}

// HandleInventoryChange triggers saves when player inventory changes
func (h FactionHandler) HandleInventoryChange(ctx *player.Context, slot int, before, after item.Stack) {
	pl := ctx.Val()

	// Trigger save for inventory change
	user.DebouncedSave(pl)
}

// HandleItemPickup triggers saves when player picks up items
func (h FactionHandler) HandleItemPickup(ctx *player.Context, it *item.Stack) {
	pl := ctx.Val()

	// Trigger debounced save when picking up items to prevent item loss
	user.DebouncedSave(pl)
}

// HandleItemDrop triggers saves when player drops items
func (h FactionHandler) HandleItemDrop(ctx *player.Context, it item.Stack) {
	pl := ctx.Val()

	// Trigger debounced save when dropping items to prevent item loss
	user.DebouncedSave(pl)
}

func (h FactionHandler) HandleStartBreak(ctx *player.Context, pos cube.Pos) {
	if h.ACHandler != nil {
		h.ACHandler.HandleStartBreak(ctx, pos)
	}

	pl := ctx.Val()

	main, off := pl.HeldItems()
	if v, ok := main.Value("special_item"); ok {
		ctx.Cancel()

		if act, ok2 := specialActionOf(v); ok2 {
			if uit, ok := items2.SpecialItem(items2.ItemAction(act)).(ActivatedOnStartBreak); ok {
			ctx.Cancel()

			useCtx := item.UseContext{}
			uit.OnStartBreak(pl, pos)
			if useCtx.CountSub > 0 {
				pl.SetHeldItems(main.Grow(-1*useCtx.CountSub), off)
			}
		}
			}

	}

	if !CheckPlayerChangeTerritory(user.GetUser(pl), pos) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pos.Vec3()).Name))
		ctx.Cancel()
		return
	}
}

func (h FactionHandler) HandleBlockBreak(ctx *player.Context, pos cube.Pos, drops *[]item.Stack, xp *int) {
	if h.ACHandler != nil {
		h.ACHandler.HandleBlockBreak(ctx, pos, drops, xp)
	}

	pl := ctx.Val()
	u := user.GetUser(pl)

	if !CheckPlayerChangeTerritory(user.GetUser(pl), pos) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pos.Vec3()).Name))
		ctx.Cancel()
		return
	}

	c1 := core.Config.Hub.BlockProtectionZone.C1
	c2 := core.Config.Hub.BlockProtectionZone.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pos.Vec3()) && pl.GameMode() != world.GameModeCreative {
		pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
		ctx.Cancel()
		return
	}

	if !slices.Contains(u.FactionInfo.IgnoreDrilledBlocksAt, pos.Vec3()) {
		EnchantsHandleBlockBreak(ctx, pos, drops, xp)
	}
}

func (h FactionHandler) HandleBlockPlace(ctx *player.Context, pos cube.Pos, b world.Block) {
	if h.ACHandler != nil {
		h.ACHandler.HandleBlockPlace(ctx, pos, b)
	}

	pl := ctx.Val()

	if !CheckPlayerChangeTerritory(user.GetUser(pl), pos) {
		pl.Message(text.Colourf(language.Translate(pl).Error.OccupiedArea, database.FactionWithin(pos.Vec3()).Name))
		ctx.Cancel()
		return
	}

	c1 := core.Config.Hub.BlockProtectionZone.C1
	c2 := core.Config.Hub.BlockProtectionZone.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pos.Vec3()) && pl.GameMode() != world.GameModeCreative {
		pl.Message(text.Colourf(language.Translate(pl).Error.BlockProtected))
		ctx.Cancel()
		return
	}

	// Debounced save after block placement to prevent inventory loss
	user.DebouncedSave(pl)
}

func (h FactionHandler) HandleMove(ctx *player.Context, from mgl64.Vec3, to cube.Rotation) {
	if h.ACHandler != nil {
		h.ACHandler.HandleMove(ctx, from, to)
	}

	pl := ctx.Val()
	u := user.GetUser(pl)

	// Check if player has moved and cancel teleport timer if so
	u.CheckTeleportMovement(pl.Position())

	if u.CoolDownTimeRemaining(user.Combat) > 0 {
		if pl.Gliding() {
			pl.StopGliding()
			pl.SetVelocity(mgl64.Vec3{0, 0, 0})
		}

		if pl.Flying() {
			pl.StopFlying()
			pl.SetGameMode(world.GameModeSurvival)
			pl.SetVelocity(mgl64.Vec3{0, 0, 0})
		}
	}
}

func (h FactionHandler) HandleFoodLoss(ctx *player.Context, from int, to *int) {
	if h.ACHandler != nil {
		h.ACHandler.HandleFoodLoss(ctx, from, to)
	}

	pl := ctx.Val()


	// Check if player is in NoPvP zone
	c1 := core.Config.Hub.NoPvp.C1
	c2 := core.Config.Hub.NoPvp.C2
	box := cube.Box(c1.X(), c1.Y(), c1.Z(), c2.X(), c2.Y(), c2.Z())
	if box.Vec3Within(pl.Position()) {
		// Cancel hunger loss in NoPvP zones
		ctx.Cancel()
		return
	}
}

func HandlePlayerJoin(pl *player.Player) {
	u := user.GetUser(pl)
	u.Data.Online = true

	// Normal join message
	pl.Message(text.Colourf("<green>Welcome back to MassacreMC Factions Beta!</green>"))
	pl.Message(text.Colourf("<aqua>You might experience some bugs or data loss. Please report any issues to our developers.</aqua>"))

	if pl.Name() == "AlphICEter" || pl.Name() == "GroundZer0Mike" || pl.Name() == "ShotDead7297" {
		u.Data.RankId = database.Owner.Shortened()
	}

	// Perform immediate world operations on main thread
	pl.Teleport(core.Config.Hub.SpawnPoint)
	pl.SetNameTag(user.FactionNameDisplay.Name(u.Data))
	pl.SetGameMode(world.GameModeSurvival)
	pl.ShowCoordinates()


		// Restore XP from backup if current level is lower than our snapshot
		if u.Data.XPLevel > pl.ExperienceLevel() {
			pl.SetExperienceLevel(u.Data.XPLevel)
			utils.Session(pl).SendExperience(u.Data.XPLevel, u.Data.XPProgress)
		}


		// One-time recovery BEFORE giving any starter items: if inventory is unexpectedly empty, restore from snapshot
		{
			invEmpty := true
			for _, it := range pl.Inventory().Items() { if it.Count() > 0 { invEmpty = false; break } }
			if invEmpty && (len(u.Data.InventorySnapshot) > 0 || len(u.Data.ArmourSnapshot) > 0) {
				// Restore normal inventory items first
				for _, cs := range u.Data.InventorySnapshot {
					_, _ = pl.Inventory().AddItem(cs.Stack())
				}
				// Restore armour into correct slots when possible; fallback to inventory if occupied/unknown
				for _, cs := range u.Data.ArmourSnapshot {
					st := cs.Stack()
					name, _ := st.Item().EncodeItem()
					lname := strings.ToLower(name)
					slotted := false
					if strings.Contains(lname, "helmet") {
						if pl.Armour().Helmet().Count() == 0 { pl.Armour().SetHelmet(st); slotted = true }
					} else if strings.Contains(lname, "chestplate") {
						if pl.Armour().Chestplate().Count() == 0 { pl.Armour().SetChestplate(st); slotted = true }
					} else if strings.Contains(lname, "leggings") {
						if pl.Armour().Leggings().Count() == 0 { pl.Armour().SetLeggings(st); slotted = true }
					} else if strings.Contains(lname, "boots") {
						if pl.Armour().Boots().Count() == 0 { pl.Armour().SetBoots(st); slotted = true }
					}
					if !slotted { _, _ = pl.Inventory().AddItem(st) }
				}
				// Also restore XP to snapshot if that is higher than current
				if u.Data.XPLevel > pl.ExperienceLevel() {
					pl.SetExperienceLevel(u.Data.XPLevel)
					utils.Session(pl).SendExperience(u.Data.XPLevel, u.Data.XPProgress)
				}

				u.Data.InventorySnapshot = nil
				u.Data.ArmourSnapshot = nil
				u.Data.SnapshotAt = time.Time{}
				if err := user.Save(pl); err != nil {
					errorCode := utils.RandString(6)
					slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to clear inventory snapshot after recovery: "+err.Error())
				}
				pl.Message(text.Colourf("<red>Oops! Your inventory was unexpectedly empty. We restored it from your last snapshot. We're sorry for the inconvenience as were still in beta.</red>"))
			}
		}

	giveMissingItems(pl)
	giveDailyDoubloons(pl)
	giveBetaCrateKeys(pl)

	// Save player data asynchronously to avoid blocking
	go func() {
		if err := user.Save(pl); err != nil {
			errorCode := utils.RandString(6)
			slog.Default().With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save player data on join: " + err.Error())
		}
	}()



	// Start background tasks
	go updateCrateText(pl)


	go spawnMob(pl)
}

func hasSpecialItemAnywhere(pl *player.Player, action items2.ItemAction) bool {
	for _, st := range pl.Inventory().Items() {
		if st.Count() <= 0 { continue }
		if v, ok := st.Value("special_item"); ok {
			switch vv := v.(type) {
			case int16:
				if vv == int16(action) { return true }
			case int32:
				if int16(vv) == int16(action) { return true }
			case int:
				if int16(vv) == int16(action) { return true }
			}
		}
	}
	return false
}

func giveMissingItems(pl *player.Player) {
	u := user.GetUser(pl)

	if u.Data.Faction.HasFaction() && u.Data.Faction.Role == database.Leader {
		if !hasSpecialItemAnywhere(pl, items2.ClaimShovel) {
			if _, err := pl.Inventory().AddItem(items.ClaimShovel{}.Stack()); err == nil {
				pl.Message(text.Colourf(language.Translate(pl).NoClaimArea, core.Config.Prefix, items.ClaimShovel{}.Stack().CustomName()))
			}
		}
	}

	if !hasSpecialItemAnywhere(pl, items2.Backpack) {
		if _, err := pl.Inventory().AddItem(backpack.Backpack{}.Stack()); err == nil {

			pl.Message(text.Colourf(language.Translate(pl).GiveBackpack, core.Config.Prefix, backpack.Backpack{}.Stack().CustomName()))
		}
	}
}

func giveDailyDoubloons(pl *player.Player) {
	u := user.GetUser(pl)
	offlineMul := (int)(time.Now().Sub(u.Data.LastLogin).Seconds() / (time.Hour * 24).Seconds())
	var dailyDoubloons int
	switch u.Data.Rank() {
	case database.MGP, database.MLP:
		dailyDoubloons = 500000
	case database.MMP:
		dailyDoubloons = 450000
	case database.MVP:
		dailyDoubloons = 350000
	case database.VIP:
		dailyDoubloons = 250000

	default:
		dailyDoubloons = 0
	}
	dailyDoubloons *= offlineMul
	u.Data.Faction.Stats.Doubloons += float64(dailyDoubloons)
	for i := 0; i < offlineMul; i++ {
		u.Data.Faction.Stats.CrateKeys[database.RandCrateType()]++
	}

	if dailyDoubloons != 0 {
		pl.Message(text.Colourf(language.Translate(pl).DailyDoubloons, core.Config.Prefix, dailyDoubloons, offlineMul))
	}
}

func giveBetaCrateKeys(pl *player.Player) {
	u := user.GetUser(pl)
	// Ensure the crate keys map is initialized
	if u.Data.Faction.Stats.CrateKeys == nil {
		u.Data.Faction.Stats.CrateKeys = map[database.CrateType]int{}
	}

	amount := 1000
	for _, ct := range []database.CrateType{database.Common, database.Rare, database.Legendary, database.Ancient, database.Vote} {
		u.Data.Faction.Stats.CrateKeys[ct] += amount
	}

	pl.Message(text.Colourf("<gold>Beta Reward:</gold> <green>Added 1,000 keys to each crate type.</green>"))

	// Immediate save to prevent key loss
	if err := user.Save(pl); err != nil {
		pl.Message(text.Colourf("<red>Warning: Failed to save beta keys immediately. They will be saved on next auto-save.</red>"))
	}
}


func updateCrateText(pl *player.Player) {
	// Wait a bit for text entities to be fully initialized
	time.Sleep(1 * time.Second)

	// Initial update before starting the ticker
	updateCrateTextOnce(pl)

	for range time.NewTicker(5 * time.Second).C {
		u := user.GetUser(pl)
		if u == nil {
			break
		}
		serverFactions.SendMainScoreboard(pl)
		updateCrateTextOnce(pl)
	}
}

func updateCrateTextOnce(pl *player.Player) {
	u := user.GetUser(pl)
	if u == nil {
		return
	}

	for t := range core.Config.Hub.Crates {
		pl.H().ExecWorld(func(tx *world.Tx, _ world.Entity) {
			key := fmt.Sprintf("crate.%d", t)
			if h := core.TextEntities[key]; h != nil {
				if e, ok := h.Entity(tx); ok {
					ct := database.CrateType(t)
					session := utils.Session(pl)
					md := utils.ParseEntityMetadata(session, e)

					// Ensure CrateKeys map is initialized
					if u.Data.Faction.Stats.CrateKeys == nil {
						u.Data.Faction.Stats.CrateKeys = map[database.CrateType]int{}
					}

					// Get the crate color for the key count
					var keyColor string
					switch ct {
					case database.Common:
						keyColor = "grey"
					case database.Rare:
						keyColor = "blue"
					case database.Legendary:
						keyColor = "dark-purple"
					case database.Ancient:
						keyColor = "gold"
					case database.Vote:
						keyColor = "green"
					default:
						keyColor = "white"
					}

					// Get key count (default to 0 if not found)
					keyCount := u.Data.Faction.Stats.CrateKeys[ct]

					// Special display for Vote crate with jackpot
					if ct == database.Vote {
						jackpot := database.GetJackpot()
						md[protocol.EntityDataKeyName] = text.Colourf("%v\n\n<grey>You have <%v>%v</%v> keys</grey>\n\n<grey>Jackpot</grey> <aqua>$%s</aqua>",
							ct.Name(), keyColor, keyCount, keyColor, utils.AddCommas(jackpot.Amount))
					} else {
						md[protocol.EntityDataKeyName] = text.Colourf("%v\n\n<grey>You have <%v>%v</%v> keys</grey>", ct.Name(), keyColor, keyCount, keyColor)
					}
					utils.WritePacket(session, &packet.SetActorData{
						EntityRuntimeID: utils.EntityRuntimeID(session, e),
						EntityMetadata:  md,
					})
				}
			}
		})
	}
}

func spawnMob(pl *player.Player) {
	for {
		time.Sleep((3 + time.Duration(rand.Intn(3))) * time.Minute)
		if utils.Distance(core.Config.Hub.SpawnPoint, pl.Position()) >= 500 {
			x := pl.Position().X() + float64(rand.Intn(15))
			z := pl.Position().Z() + float64(rand.Intn(15))
			pl.H().ExecWorld(func(tx *world.Tx, e world.Entity) {
				y := float64(tx.HighestBlock(int(x), int(z)) + 3)
				v := mgl64.Vec3{x, y, z}
				facWithin := database.FactionWithin(v)
				if facWithin == nil {
					mobs.SpawnEntity(v, tx)
				}
			})
		}
	}
}

func equal(v1, v2 mgl64.Vec3, tolerance float64) bool {
	return math.Abs(v1.X()-v2.X()) <= tolerance &&
		math.Abs(v1.Y()-v2.Y()) <= tolerance &&
		math.Abs(v1.Z()-v2.Z()) <= tolerance
}

type ActivatedOnStartBreak interface {
	OnStartBreak(pl *player.Player, pos cube.Pos) bool
}

// updateCrateDisplayWithReward shows what reward was won on the crate
func updateCrateDisplayWithReward(pl *player.Player, ct database.CrateType, reward item.Stack) {
	pl.H().ExecWorld(func(tx *world.Tx, _ world.Entity) {
		key := fmt.Sprintf("crate.%d", ct)
		if h := core.TextEntities[key]; h != nil {
			if e, ok := h.Entity(tx); ok {
				session := utils.Session(pl)
				md := utils.ParseEntityMetadata(session, e)
				// Show the reward with celebration
				rewardName := reward.CustomName()
				if rewardName == "" {
					rewardName = getItemDisplayName(reward)
				}
				md[protocol.EntityDataKeyName] = text.Colourf("<yellow>%v</yellow>", rewardName)
				utils.WritePacket(session, &packet.SetActorData{
					EntityRuntimeID: utils.EntityRuntimeID(session, e),
					EntityMetadata:  md,
				})
			}
		}
	})
}

// updateCrateDisplayNormal resets the crate display to normal
func updateCrateDisplayNormal(pl *player.Player, ct database.CrateType) {
	pl.H().ExecWorld(func(tx *world.Tx, _ world.Entity) {
		key := fmt.Sprintf("crate.%d", ct)
		if h := core.TextEntities[key]; h != nil {
			if e, ok := h.Entity(tx); ok {
				u := user.GetUser(pl)

				// Ensure CrateKeys map is initialized
				if u.Data.Faction.Stats.CrateKeys == nil {
					u.Data.Faction.Stats.CrateKeys = map[database.CrateType]int{}
				}

				// Get the crate color for the key count
				var keyColor string
				switch ct {
				case database.Common:
					keyColor = "grey"
				case database.Rare:
					keyColor = "blue"
				case database.Legendary:
					keyColor = "dark-purple"
				case database.Ancient:
					keyColor = "gold"
				case database.Vote:
					keyColor = "green"
				default:
					keyColor = "white"
				}

				session := utils.Session(pl)
				md := utils.ParseEntityMetadata(session, e)

				// Get key count (default to 0 if not found)
				keyCount := u.Data.Faction.Stats.CrateKeys[ct]

				// Special display for Vote crate with jackpot
				if ct == database.Vote {
					jackpot := database.GetJackpot()
					md[protocol.EntityDataKeyName] = text.Colourf("%v\n\n<grey>You have <%v>%v</%v> keys</grey>\n\n<grey>Jackpot</grey> <aqua>$%s</aqua>",
						ct.Name(), keyColor, keyCount, keyColor, utils.AddCommas(jackpot.Amount))
				} else {
					md[protocol.EntityDataKeyName] = text.Colourf("%v\n\n<grey>You have <%v>%v</%v> keys</grey>",
						ct.Name(), keyColor, keyCount, keyColor)
				}
				utils.WritePacket(session, &packet.SetActorData{
					EntityRuntimeID: utils.EntityRuntimeID(session, e),
					EntityMetadata:  md,
				})
			}
		}
	})
}

// getItemDisplayName converts raw item identifiers to user-friendly names
func getItemDisplayName(stack item.Stack) string {
	itemName, _ := stack.Item().EncodeItem()
	count := stack.Count()

	// Convert common item names to user-friendly versions
	displayName := itemName
	switch itemName {
	case "minecraft:enchanted_book":
		displayName = "Enchanted Book"
	case "minecraft:bedrock":
		displayName = "Bedrock"
	case "minecraft:obsidian":
		displayName = "Obsidian"
	case "minecraft:enchanted_golden_apple":
		displayName = "Enchanted Golden Apple"
	case "minecraft:experience_bottle":
		displayName = "Bottle o' Enchanting"
	case "minecraft:paper":
		displayName = "Bank Note"
	default:
		// Remove minecraft: prefix and replace underscores with spaces, then title case
		if strings.HasPrefix(displayName, "minecraft:") {
			displayName = strings.TrimPrefix(displayName, "minecraft:")
		}
		displayName = strings.ReplaceAll(displayName, "_", " ")
		displayName = strings.Title(displayName)
	}

	// Add count if more than 1
	if count > 1 {
		return text.Colourf("%v x%v", displayName, count)
	}
	return displayName
}


// specialActionOf normalises a stored special_item value to an ItemAction regardless of its concrete type.
func specialActionOf(v any) (items2.ItemAction, bool) {
	switch t := v.(type) {
	case int16:
		return items2.ItemAction(t), true
	case int32:
		return items2.ItemAction(int16(t)), true
	case int:
		return items2.ItemAction(int16(t)), true
	default:
		return 0, false
	}
}

// HandleVoteCrateReward handles vote crate rewards including jackpot logic (exported for use in listeners)
func HandleVoteCrateReward(pl *player.Player, reward item.Stack, tx *world.Tx) {
	// Add random amount to jackpot (1K-10K doubloons)
	jackpotIncrease := float64(1000 + rand.Intn(9001))
	database.AddToJackpot(jackpotIncrease)

	// Check for jackpot win (5% chance = 50 in 1,000)
	if rand.Intn(1000) < 50 {
		// JACKPOT WIN!
		wonAmount := database.WinJackpot(pl.Name())

		// Give jackpot doubloons to player
		u := user.GetUser(pl)
		u.Data.Faction.Stats.Doubloons += wonAmount

		// Immediate save for jackpot win
		user.SavePlayerDataImmediate(pl)

		// Announce to all players
		announceJackpotWin(pl, wonAmount, tx)

		// Spawn fireworks
		spawnJackpotFireworks(pl, tx)

		// Special jackpot message to winner
		pl.Message(text.Colourf("<gold><bold>JACKPOT!</bold></gold>\n<yellow>You won %s doubloons!</yellow>", utils.AddCommas(wonAmount)))
	}
}

// announceJackpotWin announces the jackpot win to all players
func announceJackpotWin(winner *player.Player, amount float64, tx *world.Tx) {
	announcement := text.Colourf(
		"<gold><bold>JACKPOT WINNER!</bold></gold>\n" +
		"<yellow>%s</yellow> <green>won</green> <gold>%s doubloons</gold> <green>from the Vote Jackpot!</green>",
		winner.Name(), utils.AddCommas(amount))

	// Send to all online players
	for pl := range core.MCServer.Players(tx) {
		pl.Message(announcement)
		pl.PlaySound(sound.LevelUp{})
	}
}

// spawnJackpotFireworks spawns colorful fireworks around the winner
func spawnJackpotFireworks(pl *player.Player, tx *world.Tx) {
	pos := pl.Position()

	// Beautiful firework colors
	colors := []color.RGBA{
		{R: 255, G: 0, B: 0, A: 255},     // Red
		{R: 255, G: 165, B: 0, A: 255},   // Orange
		{R: 255, G: 255, B: 0, A: 255},   // Yellow
		{R: 0, G: 255, B: 0, A: 255},     // Green
		{R: 0, G: 191, B: 255, A: 255},   // Blue
		{R: 138, G: 43, B: 226, A: 255},  // Purple
		{R: 255, G: 20, B: 147, A: 255},  // Pink
		{R: 255, G: 215, B: 0, A: 255},   // Gold
	}

	// Spawn multiple colorful fireworks in a circle around the player
	for i := 0; i < 16; i++ {
		angle := float64(i) * 0.392699 // 22.5 degrees in radians (16 fireworks)
		distance := 2.0 + rand.Float64()*3.0 // Random distance 2-5 blocks
		x := pos.X() + distance * math.Cos(angle)
		z := pos.Z() + distance * math.Sin(angle)
		y := pos.Y() + 1.0 + rand.Float64()*4.0 // Random height 1-5 blocks up

		fireworkPos := mgl64.Vec3{x, y, z}
		selectedColor := colors[i%len(colors)]

		// Spawn colorful dust particles (main firework effect)
		tx.AddParticle(fireworkPos, particle.Dust{Colour: selectedColor})
		tx.AddParticle(fireworkPos.Add(mgl64.Vec3{0.5, 0, 0}), particle.Dust{Colour: selectedColor})
		tx.AddParticle(fireworkPos.Add(mgl64.Vec3{-0.5, 0, 0}), particle.Dust{Colour: selectedColor})
		tx.AddParticle(fireworkPos.Add(mgl64.Vec3{0, 0, 0.5}), particle.Dust{Colour: selectedColor})
		tx.AddParticle(fireworkPos.Add(mgl64.Vec3{0, 0, -0.5}), particle.Dust{Colour: selectedColor})

		// Add explosion effects for some fireworks
		if i%4 == 0 {
			tx.AddParticle(fireworkPos, particle.HugeExplosion{})
		}
	}

	// Play multiple explosion sounds with slight delays for realistic effect
	go func() {
		for i := 0; i < 5; i++ {
			pl.PlaySound(sound.Explosion{})
			time.Sleep(200 * time.Millisecond)
		}
	}()
}

// chooseKit selects a kit based on chance percentage, returns nil if no kit
func chooseKit(chance int) *database.Rank {
	if rand.Intn(100) < chance {
		kits := []database.Rank{database.VIP, database.MVP, database.MMP}
		kit := kits[rand.Intn(len(kits))]
		return &kit
	}
	return nil // No kit
}

// createKitReward creates a kit reward if a kit is selected, otherwise returns a fallback item
func createKitReward(chance int) item.Stack {
	kit := chooseKit(chance)
	if kit != nil {
		return items.Kit{Type: *kit}.Stack()
	}
	// Fallback reward when no kit is won - give some diamonds instead
	return item.NewStack(item.Diamond{}, 3+rand.Intn(5)).WithCustomName(text.Colourf("<aqua>Diamonds</aqua>"))
}

// createEnchantmentBook creates a random enchantment book
func createEnchantmentBook() item.Stack {
	// Get all available enchantments
	allEnchantments := item.Enchantments()

	// Filter out glitter enchantment (internal use only)
	var validEnchantments []item.EnchantmentType
	for _, ench := range allEnchantments {
		if id, ok := item.EnchantmentID(ench); ok && id > 1000 {
			validEnchantments = append(validEnchantments, ench)
		}
	}

	if len(validEnchantments) == 0 {
		// Fallback to diamonds if no enchantments available
		return item.NewStack(item.Diamond{}, 2).WithCustomName(text.Colourf("<aqua>Diamonds</aqua>"))
	}

	// Select random enchantment
	selectedEnch := validEnchantments[rand.Intn(len(validEnchantments))]

	// Create enchanted book with the enchantment
	enchBook := item.NewStack(item.EnchantedBook{}, 1).WithEnchantments(item.NewEnchantment(selectedEnch, 1))

	// Add enchantment lore (this handles the display name and description)
	return blocks.AddEnchantmentLore(enchBook)
}

// rollCrateReward generates a random reward for the given crate type
func rollCrateReward(ct database.CrateType) item.Stack {
	// Helper function to create colored items
	createColoredItem := func(it world.Item, count int, colorName string) item.Stack {
		return item.NewStack(it, count).WithCustomName(text.Colourf(colorName))
	}

	its := map[database.CrateType][]item.Stack{
		database.Common: {
			createColoredItem(block.Bedrock{}, 5, "<grey>Bedrock</grey>"),
			createColoredItem(block.Obsidian{}, 10, "<dark-purple>Obsidian</dark-purple>"),
			createColoredItem(item.EnchantedApple{}, 10, "<aqua>Enchanted Golden Apple</aqua>"),
			createColoredItem(item.BottleOfEnchanting{}, 6+rand.Intn(7), "<green>Bottle o' Enchanting</green>"),
			createEnchantmentBook(),
			createKitReward(20),
			items.BankNote{Amount: float64(8000 + rand.Intn(7001))}.Stack(),
			items.CreateSmallBagOfExperience(), // 25-50 XP
		},
		database.Rare: {
			createColoredItem(block.Bedrock{}, 15, "<grey>Bedrock</grey>"),
			createColoredItem(block.Obsidian{}, 20, "<dark-purple>Obsidian</dark-purple>"),
			createColoredItem(item.EnchantedApple{}, 20, "<aqua>Enchanted Golden Apple</aqua>"),
			createColoredItem(item.BottleOfEnchanting{}, 6+rand.Intn(7), "<green>Bottle o' Enchanting</green>"),
			createEnchantmentBook(),
			createKitReward(40),
			items.BankNote{Amount: float64(17000 + rand.Intn(8001))}.Stack(),
			items.CreateRandomBagOfExperience(), // 50-100 XP
		},
		database.Legendary: {
			createColoredItem(block.Bedrock{}, 35, "<grey>Bedrock</grey>"),
			createColoredItem(block.Obsidian{}, 30, "<dark-purple>Obsidian</dark-purple>"),
			createColoredItem(item.EnchantedApple{}, 30, "<aqua>Enchanted Golden Apple</aqua>"),
			createColoredItem(item.BottleOfEnchanting{}, 6+rand.Intn(7), "<green>Bottle o' Enchanting</green>"),
			createEnchantmentBook(),
			createKitReward(60),
			items.BankNote{Amount: float64(26000 + rand.Intn(9001))}.Stack(),
			items.CreateLargeBagOfExperience(), // 100-200 XP
		},
		database.Ancient: {
			createColoredItem(block.Bedrock{}, 50, "<grey>Bedrock</grey>"),
			createColoredItem(block.Obsidian{}, 40, "<dark-purple>Obsidian</dark-purple>"),
			createColoredItem(item.EnchantedApple{}, 40, "<aqua>Enchanted Golden Apple</aqua>"),
			createColoredItem(item.BottleOfEnchanting{}, 6+rand.Intn(7), "<green>Bottle o' Enchanting</green>"),
			createEnchantmentBook(),
			createKitReward(80),
			items.BankNote{Amount: float64(50000 + rand.Intn(50001))}.Stack(),
			items.CreateBagOfExperience(200 + rand.Intn(101)), // 200-300 XP
		},
		database.Vote: {
			createColoredItem(block.Bedrock{}, 3, "<grey>Bedrock</grey>"),
			createColoredItem(block.Obsidian{}, 5, "<dark-purple>Obsidian</dark-purple>"),
			createColoredItem(item.EnchantedApple{}, 5, "<aqua>Enchanted Golden Apple</aqua>"),
			createColoredItem(item.BottleOfEnchanting{}, 6+rand.Intn(7), "<green>Bottle o' Enchanting</green>"),
			createEnchantmentBook(),
			createKitReward(15),
			items.BankNote{Amount: float64(5000 + rand.Intn(5001))}.Stack(), // 5K-10K doubloons
			items.CreateSmallBagOfExperience(), // 25-50 XP
		},
	}

	rewards := its[ct]
	if len(rewards) == 0 {
		// Fallback for unknown crate types
		return item.NewStack(item.Diamond{}, 1)
	}

	return rewards[rand.Intn(len(rewards))]
}

// checkVoteCrateJackpot handles jackpot logic for Vote crates (simplified to prevent freezing)
func checkVoteCrateJackpot(pl *player.Player) {
	// Add random amount to jackpot (1K-10K doubloons)
	jackpotIncrease := float64(1000 + rand.Intn(9001))
	database.AddToJackpot(jackpotIncrease)

	// Check for jackpot win (15% chance = 15 in 100)
	if rand.Intn(1000) < 15 {
		// JACKPOT WIN!
		wonAmount := database.WinJackpot(pl.Name())

		// Give jackpot doubloons to player
		u := user.GetUser(pl)
		u.Data.Faction.Stats.Doubloons += wonAmount

		// Immediate save for jackpot win
		user.SavePlayerDataImmediate(pl)

		// Simple announcement without complex world operations
		announcement := text.Colourf(
			"<gold><bold>JACKPOT WINNER!</bold></gold> " +
			"<yellow>%s</yellow> <green>won</green> <gold>%s doubloons</gold> <green>from the Vote Jackpot!</green>",
			pl.Name(), utils.AddCommas(wonAmount))

		// Send to all online players (simplified)
		go func() {
			for p := range core.MCServer.Players(nil) {
				p.Message(announcement)
				p.PlaySound(sound.LevelUp{})
			}
		}()

		// Fireworks and sound effects for winner
		go func() {
			// Spawn fireworks safely
			pl.H().ExecWorld(func(tx *world.Tx, _ world.Entity) {
				spawnJackpotFireworks(pl, tx)
			})

			// Additional explosion sounds
			for i := 0; i < 5; i++ {
				pl.PlaySound(sound.Explosion{})
				time.Sleep(200 * time.Millisecond)
			}
		}()

		// Special jackpot message to winner
		pl.Message(text.Colourf("<gold><bold>JACKPOT!</bold></gold>\n<yellow>You won %s doubloons!</yellow>", utils.AddCommas(wonAmount)))
	}
}

