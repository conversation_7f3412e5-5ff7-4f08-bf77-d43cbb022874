package spawner

import (
	"log/slog"
	"sync"
	"sync/atomic"
	"time"
)

// SpawnerPerformanceMonitor tracks spawner system performance
type SpawnerPerformanceMonitor struct {
	activeSpawners    int64
	totalTicks        int64
	totalSpawns       int64
	lastReportTime    time.Time
	mu                sync.RWMutex
}

var (
	perfMonitor = &SpawnerPerformanceMonitor{
		lastReportTime: time.Now(),
	}
)

// RegisterSpawnerTick should be called each time a spawner ticks
func RegisterSpawnerTick() {
	atomic.AddInt64(&perfMonitor.totalTicks, 1)
}

// RegisterSpawnerSpawn should be called each time a spawner spawns an entity
func RegisterSpawnerSpawn() {
	atomic.AddInt64(&perfMonitor.totalSpawns, 1)
}

// SetActiveSpawners updates the count of active spawners
func SetActiveSpawners(count int) {
	atomic.StoreInt64(&perfMonitor.activeSpawners, int64(count))
}

// GetPerformanceStats returns current performance statistics
func GetPerformanceStats() (activeSpawners, totalTicks, totalSpawns int64) {
	return atomic.LoadInt64(&perfMonitor.activeSpawners),
		atomic.LoadInt64(&perfMonitor.totalTicks),
		atomic.LoadInt64(&perfMonitor.totalSpawns)
}

// StartPerformanceMonitoring begins periodic performance reporting
func StartPerformanceMonitoring() {
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()
		
		for range ticker.C {
			reportPerformance()
		}
	}()
}

func reportPerformance() {
	perfMonitor.mu.Lock()
	defer perfMonitor.mu.Unlock()
	
	now := time.Now()
	timeSinceLastReport := now.Sub(perfMonitor.lastReportTime)
	
	activeSpawners := atomic.LoadInt64(&perfMonitor.activeSpawners)
	totalTicks := atomic.LoadInt64(&perfMonitor.totalTicks)
	totalSpawns := atomic.LoadInt64(&perfMonitor.totalSpawns)
	
	if timeSinceLastReport > 0 {
		ticksPerSecond := float64(totalTicks) / timeSinceLastReport.Seconds()
		spawnsPerSecond := float64(totalSpawns) / timeSinceLastReport.Seconds()
		
		// Performance warnings
		if activeSpawners > 50 {
			slog.Default().Warn("High spawner count detected",
				"active_spawners", activeSpawners,
				"ticks_per_second", ticksPerSecond,
				"spawns_per_second", spawnsPerSecond)
		}
		
		if ticksPerSecond > 1000 {
			slog.Default().Warn("High spawner tick rate detected",
				"ticks_per_second", ticksPerSecond,
				"active_spawners", activeSpawners,
				"recommendation", "Consider reducing spawner count or increasing tick intervals")
		}
		
		// Reset counters
		atomic.StoreInt64(&perfMonitor.totalTicks, 0)
		atomic.StoreInt64(&perfMonitor.totalSpawns, 0)
		perfMonitor.lastReportTime = now
	}
}

// GetRecommendedMaxSpawners returns the recommended maximum spawner count based on server performance
func GetRecommendedMaxSpawners() int {
	// Conservative recommendations based on typical server performance
	return 75 // This should handle most servers without significant lag
}

// IsSpawnerCountSafe checks if the current spawner count is within safe limits
func IsSpawnerCountSafe(count int) bool {
	return count <= GetRecommendedMaxSpawners()
}
