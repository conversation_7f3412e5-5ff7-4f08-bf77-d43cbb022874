package api

import (
	"github.com/google/uuid"
	"net/http"
	"server/server/database"
	"server/server/utils"

	"github.com/gin-gonic/gin"
)

func initGetRequests(rg *gin.RouterGroup) {
	rg.GET("/players/:uuid", jwtAuthMiddleware(), func(c *gin.Context) {
		id := utils.Panics(uuid.Parse(c.Param("uuid")))
		pd, err := database.DB.FindPlayer(id)
		if err != nil {
			panic(err)
		}
		c.<PERSON>(http.StatusOK, gin.H{
			"data": pd,
		})
	})

	rg.GET("/factions/:name", jwtAuthMiddleware(), func(c *gin.Context) {
		name := c.<PERSON>("name")
		fd, err := database.DB.FindFaction(name)
		if err != nil {
			panic(err)
		}
		c.<PERSON>(http.StatusOK, gin.H{
			"data": fd,
		})
	})

	rg.GET("/jackpot", jwtAuthMiddleware(), func(c *gin.Context) {
		jackpot := database.GetJackpot()
		c.<PERSON>(http.StatusOK, gin.H{
			"amount":         jackpot.Amount,
			"last_winner":    jackpot.LastWinner,
			"last_win_time":  jackpot.LastWinTime,
			"last_win_amount": jackpot.LastWinAmount,
			"total_wins":     jackpot.TotalWins,
			"updated_at":     jackpot.UpdatedAt,
		})
	})
}