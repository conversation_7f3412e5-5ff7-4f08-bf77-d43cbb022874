package main

import (
	"anticheat"
	"anticheat/core"
	"anticheat/handlers"
	"github.com/ThronesMC/camera"
	"github.com/bedrock-gophers/intercept/intercept"
	"github.com/df-mc/dragonfly/server/cmd"
	"github.com/df-mc/dragonfly/server/entity"
	"github.com/df-mc/dragonfly/server/player"
	"github.com/df-mc/dragonfly/server/player/chat"
	"github.com/df-mc/dragonfly/server/world"
	"log/slog"
	"os"
	"os/signal"
	"path"
	dfserver "github.com/df-mc/dragonfly/server"
	"server/server"
	"server/server/command/base"
	"server/server/command/worldedit"
	"server/server/database"
	"server/server/entity/mobs"
	"syscall"
	"time"
	"server/server/entity/mobs/boss"
	"server/server/factions/listeners"
	"server/server/factions/spawner"
	"server/server/language"
	"server/server/schedulers"
	"server/server/user"
	"server/server/utils"

	_ "server/server/api"
)

func main() {
	chat.Global.Subscribe(chat.StdoutSubscriber{})
	log := slog.Default()

	schedulers.RegisterConfigurations()
	language.RegisterLanguages()
	schedulers.InitMongo(log)

	// Initialize crash protection system
	database.InitCrashProtection()

	// Initialize checkpoint system for frequent saves
	database.InitCheckpointSystem(server.Config.Checkpoint.IntervalSeconds, server.Config.Checkpoint.CriticalIntervalSeconds)

	// Initialize recovery system for incomplete save detection
	if err := database.InitRecoverySystem(); err != nil {
		log.Error("Failed to initialize recovery system", "error", err)
	}

	base.RegisterLobbyCommands()
	base.RegisterBuildCommands()
	base.RegisterFactionCommands()
	worldedit.RefreshBlocks()

	conf, err := server.DefaultConfig().Config(log)
	if err != nil {
		log.Error("Failed to load server config", "error", err)
		panic(err)
	}
	conf.Entities = entity.DefaultRegistry.Config().New(utils.ConcatMultipleSlices[world.EntityType]([][]world.EntityType{
		{
			entity.AreaEffectCloudType,
			entity.ArrowType,
			entity.BottleOfEnchantingType,
			entity.EggType,
			entity.EnderPearlType,
			entity.ExperienceOrbType,
			entity.FallingBlockType,
			entity.FireworkType,
			entity.ItemType,
			entity.LightningType,
			entity.LingeringPotionType,
			entity.SnowballType,
			entity.SplashPotionType,
			entity.TNTType,
			entity.TextType,
		},
		{
			mobs.Zombie{},
			boss.Dragon{DragonIdentifier: "custom:dark_dragon"},
			boss.Dragon{DragonIdentifier: "custom:earth_dragon"},
			boss.Dragon{DragonIdentifier: "custom:fire_dragon"},
			boss.Dragon{DragonIdentifier: "custom:ice_dragon"},
			boss.Dragon{DragonIdentifier: "custom:light_dragon"},
			boss.Dragon{DragonIdentifier: "custom:water_dragon"},
		},
	}))
	conf.Listeners = intercept.WrapListeners(conf.Listeners)
	srv := conf.New()
	srv.CloseOnProgramEnd()
	srv.Listen()
	srv.World().SetTime(1200)
	srv.World().StopTime()
	srv.World().StopWeatherCycle()
	srv.World().StopRaining()
	srv.World().StopThundering()
	srv.World().Handle(listeners.WorldHandler{})
	server.MCServer = srv
	server.DragonflyConfig = conf  // Store the Dragonfly configuration globally

	// Set crash protection references
	database.ServerInstance = srv
	database.DragonflyConfigInstance = &conf
	anticheat.HookServer(srv, path.Join(".", "config", "anticheat.json"))
	intercept.Start(srv)

	// CRITICAL: Setup signal handling for graceful shutdown
	setupGracefulShutdown(srv, conf, log)

	go schedulers.ScheduleWorldSaving(conf)
	go schedulers.ScheduleItemsClearing()
	go schedulers.ScheduleDragonSpawning()
	go schedulers.ScheduleJackpotGrowth()

	// Initialize spawner performance monitoring
	spawner.StartPerformanceMonitoring()

	schedulers.RefreshTextEntities()

	for pl := range srv.Accept() {
		camera.SendPresets(pl)

		anticheat.HookSession(pl)
		anticheat.SetPermissions(func(src cmd.Source) bool {
			return true
		}, func(src cmd.Source) bool {
			p, _ := src.(*player.Player)
			up := user.GetUser(p)
			return up.Data.Rank() <= database.Trainee
		})

		facHandler := &listeners.FactionHandler{}
		if (core.ACInvincible{}).Allow(pl) {
			facHandler.ACHandler = &handlers.ACPlayerHandler{}
		}
		pl.Handle(facHandler)

		listeners.HandlePlayerJoin(pl)
	}

	// Graceful shutdown: save all data before terminating
	log.Info("Starting graceful shutdown...")


		// Enter shutdown mode: block damage and risky actions
		server.SetShutdownMode(true)

	// Stop checkpoint system first
	if database.CheckpointSystemInstance != nil {
		log.Info("Stopping checkpoint system...")
		database.CheckpointSystemInstance.Stop()
	}

	// First, save all player data immediately
	for pl := range srv.Players(nil) {
		// Move player to safe zone and sanitize state to prevent late deaths
		pl.Teleport(server.Config.Hub.SpawnPoint)
		pl.Heal(pl.MaxHealth(), nil)
		pl.SetFood(20)
		for _, e := range pl.Effects() { pl.RemoveEffect(e.Type()) }
		pl.Extinguish()

		// Cancel combat/teleport tags to avoid false combat log/death on shutdown
		if u := user.GetUserByUUID(pl.UUID()); u != nil {
			u.ClearCombatTag()
			u.CancelTeleportTimer()
		}
		// Save Dragonfly player data (inventory, position, health, etc.)
		if err := conf.PlayerProvider.Save(pl.UUID(), pl.Data(), srv.World()); err != nil {
			errorCode := utils.RandString(6)
			log.With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save Dragonfly player data: " + err.Error())
		}

		// Queue critical save for custom player data
		if database.SaveQueueInstance != nil {
			u := user.GetUserByUUID(pl.UUID())
			if u != nil {
				op := &database.SaveOperation{
					ID:       "shutdown_player_" + pl.UUID().String(),
					Type:     database.SaveTypePlayer,
					Data:     u.Data,
					Priority: database.PriorityCritical,
				}
				if err := database.SaveQueueInstance.QueueSave(op); err != nil {
					// Fallback to synchronous save
					if err := user.Save(pl); err != nil {
						errorCode := utils.RandString(6)
						log.With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data: " + err.Error())
					}
				}
			}
		} else {
			// Fallback to synchronous save
			if err := user.Save(pl); err != nil {
				errorCode := utils.RandString(6)
				log.With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data: " + err.Error())
			}
		}
	}

	// Queue final batch save
	if database.SaveQueueInstance != nil {
		op := &database.SaveOperation{
			ID:       "shutdown_batch_save",
			Type:     database.SaveTypeBatch,
			Data:     nil,
			Priority: database.PriorityCritical,
		}
		if err := database.SaveQueueInstance.QueueSave(op); err != nil {
			log.Error("Failed to queue shutdown batch save", "error", err)
		}

		// Wait for save queue to complete with timeout
		log.Info("Waiting for save queue to complete...")
		if err := database.SaveQueueInstance.Shutdown(30 * time.Second); err != nil {
			log.Error("Save queue shutdown failed", "error", err)
		}
	}

	// Final fallback: save all cached data synchronously
	log.Info("Performing final synchronous save...")
	for identifier, err := range database.DB.SaveAll() {
		errorCode := utils.RandString(6)
		log.With("code", errorCode).With("identifier", identifier).Error("Failed to save cached data: " + err.Error())
	}

	// Perform final recovery state save
	if database.RecoverySystemInstance != nil {
		log.Info("Saving final recovery state...")
		if err := database.RecoverySystemInstance.PerformRecoveryCheck(); err != nil {
			log.Error("Failed to save final recovery state", "error", err)
		}
	}

	// Stop crash protection system
	if database.CrashProtectionInstance != nil {
		log.Info("Stopping crash protection...")
		database.CrashProtectionInstance.Shutdown()
	}

	log.Info("Graceful shutdown completed")
}

// setupGracefulShutdown sets up signal handlers for graceful shutdown
func setupGracefulShutdown(srv *dfserver.Server, conf dfserver.Config, log *slog.Logger) {
	// Create channel to receive OS signals
	sigChan := make(chan os.Signal, 1)

	// Register the channel to receive specific signals
	signal.Notify(sigChan,
		syscall.SIGINT,  // Ctrl+C
		syscall.SIGTERM, // Termination signal
		syscall.SIGQUIT, // Quit signal
	)

	// Start goroutine to handle signals
	go func() {
		sig := <-sigChan
		log.With("signal", sig.String()).Info("Received shutdown signal - initiating graceful shutdown")

		// Perform the same graceful shutdown as the main function
		performGracefulShutdown(srv, conf, log)

		// Exit the program
		os.Exit(0)
	}()

	log.Info("Signal handlers registered for graceful shutdown")
}

// performGracefulShutdown executes the complete shutdown procedure
func performGracefulShutdown(srv *dfserver.Server, conf dfserver.Config, log *slog.Logger) {
	log.Info("Starting graceful shutdown...")


	// Enter shutdown mode: block damage and risky actions
	server.SetShutdownMode(true)

	// Stop checkpoint system first
	if database.CheckpointSystemInstance != nil {
		log.Info("Stopping checkpoint system...")
		database.CheckpointSystemInstance.Stop()
	}

	// Save all player data before shutdown
	log.Info("Saving all player data before shutdown...")
	for pl := range srv.Players(nil) {
		log.With("player", pl.Name()).Info("Saving player data before shutdown")
	}

	// First, save all player data immediately
	for pl := range srv.Players(nil) {
		// Move player to safe zone and sanitize state to prevent late deaths
		pl.Teleport(server.Config.Hub.SpawnPoint)
		pl.Heal(pl.MaxHealth(), nil)
		pl.SetFood(20)
		for _, e := range pl.Effects() { pl.RemoveEffect(e.Type()) }
		pl.Extinguish()

		// Cancel combat/teleport tags to avoid false combat log/death on shutdown
		if u := user.GetUserByUUID(pl.UUID()); u != nil {
			u.ClearCombatTag()
			u.CancelTeleportTimer()
		}
		// Save Dragonfly player data (inventory, position, health, etc.)
		if err := conf.PlayerProvider.Save(pl.UUID(), pl.Data(), srv.World()); err != nil {
			errorCode := utils.RandString(6)
			log.With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save Dragonfly player data: " + err.Error())
		}

		// Queue critical save for custom player data
		if database.SaveQueueInstance != nil {
			u := user.GetUserByUUID(pl.UUID())
			if u != nil {
				op := &database.SaveOperation{
					ID:       "shutdown_player_" + pl.UUID().String(),
					Type:     database.SaveTypePlayer,
					Data:     u.Data,
					Priority: database.PriorityCritical,
				}
				if err := database.SaveQueueInstance.QueueSave(op); err != nil {
					// Fallback to synchronous save
					if err := user.Save(pl); err != nil {
						errorCode := utils.RandString(6)
						log.With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data: " + err.Error())
					}
				}
			}
		} else {
			// Fallback to synchronous save
			if err := user.Save(pl); err != nil {
				errorCode := utils.RandString(6)
				log.With("code", errorCode).With("player", pl.Name()).With("uuid", pl.UUID().String()).Error("Failed to save custom player data: " + err.Error())
			}
		}
	}

	// Queue final batch save
	if database.SaveQueueInstance != nil {
		op := &database.SaveOperation{
			ID:       "shutdown_batch_save",
			Type:     database.SaveTypeBatch,
			Data:     nil,
			Priority: database.PriorityCritical,
		}
		if err := database.SaveQueueInstance.QueueSave(op); err != nil {
			log.Error("Failed to queue shutdown batch save", "error", err)
		}

		// Wait for save queue to complete with timeout
		log.Info("Waiting for save queue to complete...")
		if err := database.SaveQueueInstance.Shutdown(30 * time.Second); err != nil {
			log.Error("Save queue shutdown failed", "error", err)
		}
	}

	// Final fallback: save all cached data synchronously
	log.Info("Performing final synchronous save...")
	for identifier, err := range database.DB.SaveAll() {
		errorCode := utils.RandString(6)
		log.With("code", errorCode).With("identifier", identifier).Error("Failed to save cached data: " + err.Error())
	}

	// Perform final recovery state save
	if database.RecoverySystemInstance != nil {
		log.Info("Saving final recovery state...")
		if err := database.RecoverySystemInstance.PerformRecoveryCheck(); err != nil {
			log.Error("Failed to save final recovery state", "error", err)
		}
	}

	// Stop crash protection system
	if database.CrashProtectionInstance != nil {
		log.Info("Stopping crash protection...")
		database.CrashProtectionInstance.Shutdown()
	}

	// Close the server
	log.Info("Closing server...")
	if err := srv.Close(); err != nil {
		log.Error("Error closing server", "error", err)
	}

	log.Info("Graceful shutdown completed")
}
